plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.treino_droid_upgrade"
    compileSdk = 35
    ndkVersion = "27.0.12077973"


    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true // Habilita a dessugarização
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    
    signingConfigs {
        create("release_treino") {
            storeFile = file("../../.cert/Treino.key")
            storePassword = "310800"
            keyAlias = "pactotreinokey"
            keyPassword = "123456"
        }
        create("release_box") {
            storeFile = file("../../.cert/zwCross.keystore")
            storePassword = "123456"
            keyAlias = "zwCross_Alias"
            keyPassword = "123456"
        }
    }

    // Configure APK splits to reduce size (only for release builds)
    splits {
        abi {
            isEnable = true
            reset()
            include("arm64-v8a", "armeabi-v7a")
            // Generate universal APK for debug, split APKs for release
            isUniversalApk = true
        }
    }

    flavorDimensions += "flavor-type"



    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.pacto"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 26
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6348268772"
        manifestPlaceholders["googleFitOauthClientId"] = "977449544580-598i2vp5ul2qoljc7tle09njm9u0gntj.apps.googleusercontent.com"
        resValue("string", "app_name", "Treino")
    }

    buildTypes {
        debug {
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            applicationIdSuffix = ".debug"
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            // variant with `isDebuggable=false`.
            isMinifyEnabled = true
            isShrinkResources = true
            signingConfig = signingConfigs.getByName("release_treino")
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    productFlavors {
        create("treino") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6348268772"
            manifestPlaceholders["googleFitOauthClientId"] = "977449544580-598i2vp5ul2qoljc7tle09njm9u0gntj.apps.googleusercontent.com"
            dimension = "flavor-type"
            applicationId = "com.pacto"
            resValue("string", "app_name", "Treino")
        }
        create("academia") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~7849052701"
            dimension = "flavor-type"
            applicationId = "com.pacto.zwacademia"
            resValue("string", "app_name", "Minha Academia")
            signingConfig = signingConfigs.getByName("release_treino")
        }
        create("box") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            manifestPlaceholders["googleFitOauthClientId"] = "977449544580-598i2vp5ul2qoljc7tle09njm9u0gntj.apps.googleusercontent.com"
            dimension = "flavor-type"
            applicationId = "com.pactosolucoes.meubox"
            resValue("string", "app_name", "Meu box")
            signingConfig = signingConfigs.getByName("release_box")
        }
        create("selfit") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            dimension = "flavor-type"
            applicationId = "br.com.pactosolucoes.selfit"
            resValue("string", "app_name", "Selfit")
            signingConfig = signingConfigs.getByName("release_treino")
        }
        create("wellness") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            dimension = "flavor-type"
            applicationId = "com.pacto.wellness"
            resValue("string", "app_name", "Wellness")
            signingConfig = signingConfigs.getByName("release_treino")
        }
        create("winnersgym") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            dimension = "flavor-type"
            applicationId = "com.pacto.winnersgym"
            resValue("string", "app_name", "Winners Gym")
            signingConfig = signingConfigs.getByName("release_treino")
        }
        create("engenhariaDoCorpo") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            dimension = "flavor-type"
            applicationId = "com.pacto.engenhariadocorpo"
            resValue("string", "app_name", "Engenharia do Corpo")
            signingConfig = signingConfigs.getByName("release_treino")
        }       
        create("apppersonalizado") {
            manifestPlaceholders["appID"] = "ca-app-pub-3678266089524496~6331376304"
            dimension = "flavor-type"
            applicationId = "br.com.pactosolucoes.thesimplegym"
            resValue("string", "app_name", "The Simple Gym")
            signingConfig = signingConfigs.getByName("release_treino")
        }
    }


}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")
    implementation("com.google.android.material:material:1.11.0")
}
