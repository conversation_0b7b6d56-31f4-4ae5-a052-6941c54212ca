{"project_info": {"project_number": "977449544580", "firebase_url": "https://app-do-aluno-unificado.firebaseio.com", "project_id": "app-do-aluno-unificado", "storage_bucket": "app-do-aluno-unificado.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:977449544580:android:e71cbce8159c523019cbd1", "android_client_info": {"package_name": "app.com.meubox"}}, "oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:81cf641657abe8d0", "android_client_info": {"package_name": "com.pacto"}}, "oauth_client": [{"client_id": "977449544580-598i2vp5ul2qoljc7tle09njm9u0gntj.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "f58363ab7bb3d59db8350f1564c9d5802d3da849"}}, {"client_id": "977449544580-5i29tfei7km62acjbu92ora5edilcmn3.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "b27fa5ac9a117937ec778bc4045e7f98f8a20302"}}, {"client_id": "977449544580-6nh8b6ahut7jma63qkhubsne4vaqrhdv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "40ffca2bb8e329c5ec90a15be826d7a9c43d8bca"}}, {"client_id": "977449544580-hfk7fqt57iaes51rv63mh598ib425sae.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "ec254d9fbc5f58b663d06577849310497919d020"}}, {"client_id": "977449544580-q6csoti7ccifq12vv0o1r127mer1kjlc.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "3e0d30fd5a5dbfacfd4f14b9eefeefdf3cf70d73"}}, {"client_id": "977449544580-upcrju096cfhfb31d9ia9gpfmeal8nlk.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto", "certificate_hash": "57ce9f9ed5bd7a69344684fd7d9536cd77d4d882"}}, {"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:67fb8fd588c3dd4119cbd1", "android_client_info": {"package_name": "com.pacto.engenhariadocorpo"}}, "oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:bf718655954f742a19cbd1", "android_client_info": {"package_name": "com.pacto.flynow"}}, "oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:6f338fda5d3ae59319cbd1", "android_client_info": {"package_name": "com.pacto.wellness"}}, "oauth_client": [{"client_id": "977449544580-1s7r2nuetnnkgtdqbk6gvoedgcgnbnbv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto.wellness", "certificate_hash": "b27fa5ac9a117937ec778bc4045e7f98f8a20302"}}, {"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:4c99ae59ada51b2519cbd1", "android_client_info": {"package_name": "com.pacto.winnersgym"}}, "oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:977449544580:android:c14fd61b5021acde", "android_client_info": {"package_name": "com.pacto.zwacademia"}}, "oauth_client": [{"client_id": "977449544580-en05mt90k901jp29vhujivqnpn2r7cko.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto.zwacademia", "certificate_hash": "f58363ab7bb3d59db8350f1564c9d5802d3da849"}}, {"client_id": "977449544580-f2too0ge42e947u20s74tp2a1mg5njbb.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.pacto.zwacademia", "certificate_hash": "b27fa5ac9a117937ec778bc4045e7f98f8a20302"}}, {"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com", "client_type": 3}, {"client_id": "977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.treino.ios", "app_store_id": "862662527"}}]}}}], "configuration_version": "1"}