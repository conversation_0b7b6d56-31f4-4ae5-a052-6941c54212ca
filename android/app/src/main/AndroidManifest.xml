<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
  xmlns:tools="http://schemas.android.com/tools">
  <supports-screens android:smallScreens="true" android:normalScreens="true" android:largeScreens="true" android:xlargeScreens="true" android:requiresSmallestWidthDp="120"/>
  <uses-feature android:name="android.hardware.telephony" android:required="false" />
  <uses-feature android:name="android.hardware.camera" android:required="false" />
  <uses-feature android:name="android.hardware.bluetooth" android:required="false" />
  <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />
  <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" android:required="false" />
  <uses-permission android:name="android.permission.NFC" android:required="false" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:required="false" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:required="false" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" android:maxSdkVersion="33" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" android:maxSdkVersion="33" />
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" android:maxSdkVersion="33" />
  <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" android:required="false" />
  <uses-permission android:name="android.permission.GET_ACCOUNTS" android:required="false"/>
  <uses-permission android:name="android.permission.WAKE_LOCK" android:required="false" />
  <uses-permission android:name="android.permission.INTERNET" android:required="true" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" android:required="false" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" android:required="false" />
  <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" android:required="false" />
  <uses-permission android:name="android.permission.CAMERA" android:required="false" />
  <uses-permission android:name="android.permission.VIBRATE" android:required="false" />
  <uses-permission android:name="android.permission.FLASHLIGHT" android:required="false" />
  <uses-permission android:name="com.android.email.permission.ACCESS_PROVIDER" android:required="false" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" android:required="false" />
  <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" android:required="false" />
  <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" android:required="false" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" android:required="false" />
  <uses-permission android:name="com.ryanheise.audioservice.AudioServiceActivity" android:required="false" />
  <uses-permission android:name="android.permission." android:required="false"/>
  <uses-permission android:name="android.permission.health.READ_STEPS" android:required="false" />
  <uses-permission android:name="android.permission.health.READ_DISTANCE" android:required="false" />
  <uses-permission android:name="android.permission.BODY_SENSORS" android:required="false" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" android:maxSdkVersion="30" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" android:maxSdkVersion="28" />
  <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
  <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" android:required="false" />

  <application android:name="${applicationName}" android:label="@string/app_name" android:icon="@mipmap/ic_launcher" android:requestLegacyExternalStorage="true">
    <activity-alias android:name="ViewPermissionUsageActivity" android:exported="true" android:targetActivity=".MainActivity" android:permission="android.permission.START_VIEW_PERMISSION_USAGE">
      <intent-filter>
        <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />
        <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
      </intent-filter>
    </activity-alias>
    <activity android:name="com.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback" android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="signinwithapple" />
        <data android:path="callback" />
      </intent-filter>
    </activity>
    <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
    <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
    <service android:name="com.dexterous.flutterlocalnotifications.ForegroundService" android:exported="false" android:stopWithTask="false"/>
    <meta-data android:name="com.google.android.gms.ads.AD_MANAGER_APP" android:value="true"/>
    <meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="${appID}"/>
    <uses-library android:name="org.apache.http.legacy" android:required="false"/>
    <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@mipmap/ic_launcher"/>
    <meta-data android:name="com.google.firebase.messaging.default_notification_color" android:value="#ff4b2d"/>
    <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="my_channel_01" />
    <meta-data android:name="com.google.android.gms.auth.api.fitness" android:value="${googleFitOauthClientId}"/>
    <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
    </receiver>
    <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
    <activity android:name=".MainActivity" android:launchMode="singleTop" android:theme="@style/LaunchTheme" android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize" android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="app-do-aluno-unificado.firebaseapp.com" />
      </intent-filter>
      <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme"/>
      <meta-data android:name="io.flutter.embedding.android.SplashScreenDrawable" android:resource="@drawable/launch_background"/>
      <intent-filter>
        <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:host="treino.page.link" android:scheme="https"/>
      </intent-filter>
      <intent-filter>
        <action android:name="FLUTTER_NOTIFICATION_CLICK" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
    </activity>
    <provider android:name="androidx.core.content.FileProvider" android:authorities="${applicationId}.com.shekarmudaliyar.social_share" android:exported="false" android:grantUriPermissions="true" tools:replace="android:authorities">
      <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths" />
    </provider>
    <activity android:name="com.yalantis.ucrop.UCropActivity" android:screenOrientation="portrait" android:theme="@style/Theme.AppCompat.Light.NoActionBar" android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.DEFAULT" />
        </intent-filter>
    </activity>
    <meta-data android:name="flutterEmbedding" android:value="2"/>
    <meta-data android:name="io.flutter.embedding.android.DisableImpeller" android:value="false" />
  </application>
  <queries>
    <package android:name="com.google.android.apps.healthdata" />
    <intent>
      <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
    </intent>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="https" />
    </intent>
    <intent>
      <action android:name="android.intent.action.DIAL" />
      <data android:scheme="tel" />
    </intent>
    <intent>
      <action android:name="android.intent.action.SENDTO" />
      <data android:scheme="smsto" />
    </intent>
    <intent>
      <action android:name="android.intent.action.SEND" />
      <data android:mimeType="*/*" />
    </intent>
  </queries>
</manifest>
