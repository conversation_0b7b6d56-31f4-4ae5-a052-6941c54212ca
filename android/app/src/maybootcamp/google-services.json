{"project_info": {"project_number": "458760368028", "firebase_url": "https://appgestor-3f25c.firebaseio.com", "project_id": "appgestor-3f25c", "storage_bucket": "appgestor-3f25c.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:458760368028:android:da01372df593944a", "android_client_info": {"package_name": "br.com.pactosolucoes.app.gestor"}}, "oauth_client": [{"client_id": "458760368028-82uf8oc3v5qvceuo7jg68kp5v44rbj8v.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.app.gestor", "certificate_hash": "6f6a12c2eb152993e634c17ccf3003ede787fa14"}}, {"client_id": "458760368028-dp9h3cbku5vepnkmsffnf1fjt0va8acs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.app.gestor", "certificate_hash": "fbde3b9c2cbe5a092b21eafb4c0e7d5fda728fef"}}, {"client_id": "458760368028-itse0bsntnjnfhbf5fspjshkam6dv569.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.app.gestor", "certificate_hash": "ec254d9fbc5f58b663d06577849310497919d020"}}, {"client_id": "458760368028-r5b3af12lf3v5m8q16dkeb2tdshjr9hp.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.app.gestor", "certificate_hash": "706a7410e56b34e2db2bef66027613d5a74ac416"}}, {"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:458760368028:android:15af79d90a7dc26ff9a259", "android_client_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct"}}, "oauth_client": [{"client_id": "458760368028-4j29ibtaf47i7nml5cgk964d5k58s4tj.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "40ffca2bb8e329c5ec90a15be826d7a9c43d8bca"}}, {"client_id": "458760368028-alhfu1d17lohqkubilf38bdro2kluk2l.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "57ce9f9ed5bd7a69344684fd7d9536cd77d4d882"}}, {"client_id": "458760368028-j40phh2sgr0cbgd9lv2rc00dvs93bfh5.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "f58363ab7bb3d59db8350f1564c9d5802d3da849"}}, {"client_id": "458760368028-jdcf8kflo777le6lbs3a6tae0ibvob6c.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "ec254d9fbc5f58b663d06577849310497919d020"}}, {"client_id": "458760368028-krmt0nm75qcgrid4qlpj6en3ijoaih5j.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "b27fa5ac9a117937ec778bc4045e7f98f8a20302"}}, {"client_id": "458760368028-rak6m0cq035bilrgfbqk74gobmdlvnnr.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "br.com.pactosolucoes.fabricademonstrosct", "certificate_hash": "3e0d30fd5a5dbfacfd4f14b9eefeefdf3cf70d73"}}, {"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:458760368028:android:66e6e6bbfc34c89af9a259", "android_client_info": {"package_name": "br.com.pactosolucoes.liveacademia"}}, "oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:458760368028:android:754f52e87fa0d676f9a259", "android_client_info": {"package_name": "br.com.pactosolucoes.maybootcamp"}}, "oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:458760368028:android:8b0397f5c6cf5b99f9a259", "android_client_info": {"package_name": "br.com.sistemapacto.app"}}, "oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:458760368028:android:25b2c0ee22343bd5f9a259", "android_client_info": {"package_name": "com.wagi.acessofacil"}}, "oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "458760368028-5rh9qevq0i74nvd12918migqqs22dh1k.apps.googleusercontent.com", "client_type": 3}, {"client_id": "458760368028-1ovfo7s3gd08lcoj1fsnlmi9kfjsf15j.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "br.com.pactosolucoes.app.gestor.ios", "app_store_id": "1219400733"}}]}}}], "configuration_version": "1"}