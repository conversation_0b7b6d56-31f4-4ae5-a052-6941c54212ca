allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
configurations.all {
    resolutionStrategy {
        // Força o uso de uma versão específica
        force("com.google.android.play:core-common:2.0.4")
        // Ou exclua completamente uma dependência problemática
        exclude(group = "com.google.android.play", module = "core")
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)
subprojects {
    tasks.register("fixManifestsAndNamespace") {
        doLast {
            // Ensure namespace in build.gradle
            val buildGradleFile = file("${project.projectDir}/build.gradle")
            if (buildGradleFile.exists()) {
                val buildGradleContent = buildGradleFile.readText()
                val manifestFile = file("${project.projectDir}/src/main/AndroidManifest.xml")
                if (manifestFile.exists()) {
                    val manifestContent = manifestFile.readText()
                    val packagePattern = "package=\"([^\"]+)\"".toRegex()
                    val packageMatch = packagePattern.find(manifestContent)
                    val packageName = packageMatch?.groupValues?.get(1)
                    
                    if (packageName != null && !buildGradleContent.contains("namespace")) {
                        println("Setting namespace in ${buildGradleFile}")
                        val updatedContent = buildGradleContent.replace(
                            "android\\s*\\{".toRegex(), 
                            "android {\n    namespace '${packageName}'"
                        )
                        buildGradleFile.writeText(updatedContent)
                    }
                }
            }

            // Remove package attribute from AndroidManifest.xml
            val manifests = fileTree(project.projectDir) { include("**/AndroidManifest.xml") }
            manifests.forEach { manifestFile ->
                val manifestContent = manifestFile.readText()
                if (manifestContent.contains("package=")) {
                    println("Removing package attribute from ${manifestFile}")
                    val updatedContent = manifestContent.replace("package=\"[^\"]*\"".toRegex(), "")
                    manifestFile.writeText(updatedContent)
                }
            }
        }
    }

    // Ensure the task runs before the build process
    tasks.matching { it.name.startsWith("preBuild") }.configureEach {
        dependsOn("fixManifestsAndNamespace")
    }
    afterEvaluate {
        // if (project.name == "image_gallery_saver") {
            val android = extensions.findByType<com.android.build.gradle.BaseExtension>()
            android?.apply {
                compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_17
                    targetCompatibility = JavaVersion.VERSION_17
                }
                // Definir compileSdkVersion para 33 (ou outro valor acima de 30)
                compileSdkVersion(35)
                
                // // Adiciona dependência do embedding v1 para o assets_audio_player_web
                // if (project.name == "assets_audio_player_web") {
                //     dependencies.add("compileOnly", "io.flutter:flutter-embedding-v1:1.0.0-3316dd8728419ad3534e3f6112aa6291f587078a")
                // }
            }
            
            tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
                kotlinOptions {
                    jvmTarget = JavaVersion.VERSION_17.toString()
                }
            }
        // }
        if (project.hasProperty("android")) {
            val androidExtension = project.extensions.findByName("android")
            androidExtension?.let {
                val androidExt = it as? com.android.build.gradle.BaseExtension
                if (androidExt?.namespace == null) {
                    androidExt?.namespace = project.group.toString()
                }
            }
        }
    }
}
subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
