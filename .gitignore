# Miscellaneous
*.class
*.lock
*.log
*.pyc
*.swp
.DS_Store
.yaml
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Visual Studio Code related
.classpath
.project
.settings/

# Flutter repo-specific
/bin/cache/
/bin/internal/bootstrap.bat
/bin/internal/bootstrap.sh
/bin/mingit/
/dev/benchmarks/mega_gallery/
/dev/bots/.recipe_deps
/dev/bots/android_tools/
/dev/devicelab/ABresults*.json
/dev/docs/doc/
/dev/docs/flutter.docs.zip
/dev/docs/lib/
/dev/docs/pubspec.yaml
/dev/integration_tests/**/xcuserdata
/dev/integration_tests/**/Pods
/packages/flutter/coverage/
version
analysis_benchmark.json

# packages file containing multi-root paths
.packages.generated

# Flutter/Dart/Pub related
android/app/.cxx/
**/doc/api/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
**/generated_plugin_registrant.dart
.packages
.pub-cache/
.pub/
.lunux
.lunux/*
/linux
/linux/*
build/
flutter_*.png
linked_*.ds
unlinked.ds
unlinked_spec.ds

# Android related
**/android/**/gradle-wrapper.jar
.gradle/
**/android/captures/
**/android/gradlew
**/android/app/.cxx/
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
*.jks

# iOS/XCode related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/.last_build_id
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# macOS
**/Flutter/ephemeral/
**/Pods/
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral
**/xcuserdata/

# Windows
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h

# Coverage
coverage/

# Symbols
app.*.symbols

# Exceptions to above rules.
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages
!/dev/ci/**/Gemfile.lock
.fvm/
.vscode/
linux
.vscode/settings.json
linux/
# Ignore todos os symlinks de plugins do Flutter no Linux
linux/flutter/ephemeral/.plugin_symlinks/
.plugin_symlinks
## arquivos de configuração de flavor

**/android/app/src/apppersonalizado/config.json
**/android/app/src/apppersonalizado/google-services.json
**/android/app/src/apppersonalizado/res/mipmap-hdpi/ic_launcher.png
**/android/app/src/apppersonalizado/res/mipmap-hdpi/ic_launcher_round.png
**/android/app/src/apppersonalizado/res/mipmap-mdpi/ic_launcher.png
**/android/app/src/apppersonalizado/res/mipmap-mdpi/ic_launcher_round.png
**/android/app/src/apppersonalizado/res/mipmap-xhdpi/ic_launcher.png
**/android/app/src/apppersonalizado/res/mipmap-xhdpi/ic_launcher_round.png
**/android/app/src/apppersonalizado/res/mipmap-xxhdpi/ic_launcher.png
**/android/app/src/apppersonalizado/res/mipmap-xxhdpi/ic_launcher_round.png
**/android/app/src/apppersonalizado/res/mipmap-xxxhdpi/ic_launcher.png
**/android/app/src/apppersonalizado/res/mipmap-xxxhdpi/ic_launcher_round.png
**/android/app/src/apppersonalizado/res/values/strings.xml
**/android/app/src/apppersonalizado/res/values/styles.xml
**/ios/Runner.xcodeproj/project.pbxproj.bak
**/android/app/build.gradle.kts.bak
**/ios/Runner/Assets.xcassets/AppIcon.apppersonalizado.appiconset/logo.png
**/ios/testing Watch App/Assets.xcassets/AppIcon.apppersonalizado.appiconset/logo.png
**/lib/flavors.dart.bak
