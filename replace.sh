#!/bin/bash

# fvm flutter build appbundle --release --flavor box -t lib/main-Box.dart --build-name=6.5.1 --build-number 2019013857
# Caminho do arquivo
FILE="ios/Runner.xcodeproj/project.pbxproj"
if [ -f "$FILE" ]; then
  sed -i '' 's/CURRENT_PROJECT_VERSION = [^;]*/CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)"/' "$FILE"
  sed -i '' 's/MARKETING_VERSION = [^;]*/MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)"/' "$FILE"
  sed -i '' 's/FLUTTER_BUILD_NAME = [^;]*/FLUTTER_BUILD_NAME = "$(FLUTTER_BUILD_NAME)"/' "$FILE"
  sed -i '' 's/FLUTTER_BUILD_NUMBER = [^;]*/FLUTTER_BUILD_NUMBER = "$(FLUTTER_BUILD_NUMBER)"/' "$FILE"
  echo "Modificações concluídas com sucesso."
else
  echo "Arquivo não encontrado: $FILE"
fi