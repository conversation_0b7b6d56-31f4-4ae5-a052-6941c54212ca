import 'package:flutter_test/flutter_test.dart';

// Mock da função de exibição de carga, baseada na lógica do app
double converterQuiloEmLibra(double valor, {bool isLibra = false}) {
  if (isLibra) {
    return valor * 2.20462262;
  } else {
    return valor;
  }
}

String getDescricaoCargaMock(double carga, {bool isLibra = false, bool entradaEmLibra = false}) {
  if (carga == 0.0) return '0';
  double cargaKgLimite;

  if (entradaEmLibra) {
    // Se o valor foi digitado em lbs, converte para kg antes de salvar seguindo o fluxo original do app
    cargaKgLimite = carga * 0.45359237;
  } else {
    cargaKgLimite = carga;
  }

  if (isLibra) {
    double cargaLbs = double.parse((cargaKgLimite * 2.20462262).toString());
    if ((cargaLbs - cargaLbs.round()).abs() < 0.05) {
      return cargaLbs.round().toString();
    }
    return cargaLbs.toStringAsFixed(1);
  } else {
    if ((cargaKgLimite - cargaKgLimite.round()).abs() < 0.05) {
      return cargaKgLimite.round().toString();
    }
    return cargaKgLimite.toStringAsFixed(1);
  }
}

void main() {
  group('Testes Unitários - getDescricaoCarga', () {
    test('deve exibir 0 quando carga é zero', () {
      expect(getDescricaoCargaMock(0.0), equals('0'));
      expect(getDescricaoCargaMock(0.0, isLibra: true), equals('0'));
    });

    test('deve exibir valor inteiro quando carga é próxima de inteiro (kg)', () {
      expect(getDescricaoCargaMock(100.0), equals('100'));
      expect(getDescricaoCargaMock(99.98), equals('100'));
    });

    test('deve exibir valor decimal quando carga não é inteiro (kg)', () {
      expect(getDescricaoCargaMock(97.17), equals('97.2'));
      expect(getDescricaoCargaMock(97.15), equals('97.2'));
      expect(getDescricaoCargaMock(97.19), equals('97.2'));
      expect(getDescricaoCargaMock(97.11), equals('97.1'));
      expect(getDescricaoCargaMock(97.10), equals('97.1'));
      expect(getDescricaoCargaMock(97.00), equals('97'));
    });

    test('deve exibir valor inteiro quando carga é próxima de inteiro (lbs)', () {
      expect(getDescricaoCargaMock(43.09, isLibra: true), equals('95'));
      expect(getDescricaoCargaMock(100.0, isLibra: true), equals('220.5'));
    });

    test('deve exibir valor decimal quando carga não é inteiro (lbs)', () {
      // Simula valor digitado em lbs, convertido para kg, salvo, e exibido em lbs
      expect(getDescricaoCargaMock(214.1, isLibra: true, entradaEmLibra: true), equals('214.1'));
      expect(getDescricaoCargaMock(214.2, isLibra: true, entradaEmLibra: true), equals('214.2'));
      expect(getDescricaoCargaMock(214.0, isLibra: true, entradaEmLibra: true), equals('214'));
      expect(getDescricaoCargaMock(213.8, isLibra: true, entradaEmLibra: true), equals('213.8'));
      expect(getDescricaoCargaMock(97.0, isLibra: true, entradaEmLibra: true), equals('97'));
    });

    test('deve tratar valores com casas decimais pequenas (kg)', () {
      expect(getDescricaoCargaMock(100.01), equals('100'));
      expect(getDescricaoCargaMock(100.049), equals('100'));
      expect(getDescricaoCargaMock(100.051), equals('100.1'));
      expect(getDescricaoCargaMock(100.099), equals('100.1'));
      expect(getDescricaoCargaMock(100.19), equals('100.2'));
      expect(getDescricaoCargaMock(100.11), equals('100.1'));
    });

    test('deve tratar valores com casas decimais pequenas (lbs)', () {
      expect(getDescricaoCargaMock(45.359237, isLibra: true), equals('100'));
      expect(getDescricaoCargaMock(45.4, isLibra: true), equals('100.1'));
      expect(getDescricaoCargaMock(45.49, isLibra: true), equals('100.3'));
      expect(getDescricaoCargaMock(45.45, isLibra: true), equals('100.2'));
    });
  });

  group('Testes de conversão de quilo para libra', () {
    test('deve converter corretamente de kg para lbs', () {
      expect(converterQuiloEmLibra(1, isLibra: true), closeTo(2.20462262, 0.00001));
      expect(converterQuiloEmLibra(0, isLibra: true), equals(0));
      expect(converterQuiloEmLibra(100, isLibra: true), closeTo(220.462262, 0.00001));
    });

    test('deve retornar o mesmo valor quando não é libra', () {
      expect(converterQuiloEmLibra(1, isLibra: false), equals(1));
      expect(converterQuiloEmLibra(100, isLibra: false), equals(100));
    });
  });
}
