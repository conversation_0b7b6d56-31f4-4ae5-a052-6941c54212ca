import 'package:flutter_test/flutter_test.dart';
import 'package:app_treino/model/personal/EventoTimeLineAluno.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';

void main() {
  group('TelaHistoricoDeTreinos lógica', () {
    test('Chip título para TREINO_EXTRA deve mostrar apenas dia e mês', () {
      final evento = EventoTimeLineAluno(
        evento: 'TREINO_EXTRA',
        data: DateTime(2025, 9, 9, 15, 30),
      );
      final titulo = evento.data != null
          ? evento.evento == 'TREINO_EXTRA'
              ? UtilDataHora.getDiaMes(dateTime: evento.data!)
              : '${UtilDataHora.getDiaMes(dateTime: evento.data!)} ${UtilDataHora.getApenasHoraHistorico(dateTime: evento.data!)}'
          : 'data_nao_disponivel';
      expect(titulo, 'Sep 9'); // Ajuste conforme formato real
    });

    test('Chip título para outros eventos deve mostrar dia, mês e hora', () {
      final evento = EventoTimeLineAluno(
        evento: 'TREINOU',
        data: DateTime(2025, 9, 9, 15, 30),
      );
      final titulo = evento.data != null
          ? evento.evento == 'TREINO_EXTRA'
              ? UtilDataHora.getDiaMes(dateTime: evento.data!)
              : '${UtilDataHora.getDiaMes(dateTime: evento.data!)} ${UtilDataHora.getApenasHoraHistorico(dateTime: evento.data!)}'
          : 'data_nao_disponivel';
      expect(titulo, 'Sep 9 às 15:30'); // Ajuste conforme formato real
    });

    test('Chip título deve ser "data_nao_disponivel" se data for nula', () {
      final evento = EventoTimeLineAluno(
        evento: 'TREINOU',
        data: null,
      );
      final titulo = evento.data != null
          ? evento.evento == 'TREINO_EXTRA'
              ? UtilDataHora.getDiaMes(dateTime: evento.data!)
              : '${UtilDataHora.getDiaMes(dateTime: evento.data!)} ${UtilDataHora.getApenasHoraHistorico(dateTime: evento.data!)}'
          : 'data_nao_disponivel';
      expect(titulo, 'data_nao_disponivel');
    });

    test('deve calcular total de eventos realizados', () {
      final eventos = [
        EventoTimeLineAluno(evento: 'TREINOU', data: DateTime(2025, 9, 9)),
        EventoTimeLineAluno(evento: 'TREINO_EXTRA', data: DateTime(2025, 9, 10)),
        EventoTimeLineAluno(evento: 'FEZ_AULA', data: DateTime(2025, 9, 11)),
      ];
      final total = eventos.length;
      expect(total, equals(3));
    });

    test('deve filtrar eventos por tipo', () {
      final eventos = [
        EventoTimeLineAluno(evento: 'TREINOU', data: DateTime(2025, 9, 9)),
        EventoTimeLineAluno(evento: 'TREINO_EXTRA', data: DateTime(2025, 9, 10)),
        EventoTimeLineAluno(evento: 'FEZ_AULA', data: DateTime(2025, 9, 11)),
      ];
      final extras = eventos.where((e) => e.evento == 'TREINO_EXTRA').toList();
      expect(extras.length, equals(1));
      expect(extras.first.evento, equals('TREINO_EXTRA'));
    });

    test('deve lidar com eventos com data nula', () {
      final eventos = [
        EventoTimeLineAluno(evento: 'TREINOU', data: DateTime(2025, 9, 9)),
        EventoTimeLineAluno(evento: 'TREINO_EXTRA', data: null),
        EventoTimeLineAluno(evento: 'FEZ_AULA', data: DateTime(2025, 9, 11)),
      ];
      final nulos = eventos.where((e) => e.data == null).toList();
      expect(nulos.length, equals(1));
      expect(nulos.first.evento, equals('TREINO_EXTRA'));
    });

    test('deve validar propriedades do evento', () {
      final evento = EventoTimeLineAluno(
        evento: 'TREINOU',
        descricao: 'Treino de força',
        data: DateTime(2025, 9, 9, 15, 30),
      );
      expect(evento.evento, equals('TREINOU'));
      expect(evento.descricao, equals('Treino de força'));
      expect(evento.data, isNotNull);
    });

    test('deve contar eventos por mês', () {
      final eventos = [
        EventoTimeLineAluno(evento: 'TREINOU', data: DateTime(2025, 9, 9)),
        EventoTimeLineAluno(evento: 'TREINO_EXTRA', data: DateTime(2025, 9, 10)),
        EventoTimeLineAluno(evento: 'FEZ_AULA', data: DateTime(2025, 8, 11)),
      ];
      final setembro = eventos.where((e) => e.data?.month == 9).length;
      expect(setembro, equals(2));
    });

    test('deve simular seleção de evento principal', () {
      final eventos = [
        EventoTimeLineAluno(evento: 'TREINOU', data: DateTime(2025, 9, 9)),
        EventoTimeLineAluno(evento: 'TREINO_EXTRA', data: DateTime(2025, 9, 10)),
        EventoTimeLineAluno(evento: 'FEZ_AULA', data: DateTime(2025, 9, 11)),
      ];
      // Simula seleção do primeiro evento do tipo 'TREINOU'
      final selecionado = eventos.firstWhere((e) => e.evento == 'TREINOU', orElse: () => EventoTimeLineAluno(evento: 'NENHUM'));
      expect(selecionado.evento, equals('TREINOU'));
    });
  });
}
