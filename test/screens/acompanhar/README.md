# Testes Unitários - TelaSelecionarFicha

Este diretório contém os testes unitários simples para a tela `TelaSelecionarFicha`.

## Estrutura dos Testes

### `tela_selecionar_ficha_test.dart`
Arquivo principal com os testes unitários organizados em grupos:

- **Testes Básicos**: Verificam instanciação, parâmetros e estrutura da classe
- **Documentação**: Testam a estrutura e comportamento básico da tela

Os testes são **simples e focados na estrutura**, não testando dependências externas ou GetIt para evitar complexidade desnecessária.

## Como Executar os Testes

### Executar todos os testes desta tela:
```bash
flutter test test/screens/acompanhar/tela_selecionar_ficha_test.dart
```

### Executar com verbose para mais detalhes:
```bash
flutter test test/screens/acompanhar/tela_selecionar_ficha_test.dart --verbose
```

### Executar um grupo específico de testes:
```bash
flutter test test/screens/acompanhar/tela_selecionar_ficha_test.dart --name "Testes Básicos"
```

### Executar um teste específico:
```bash
flutter test test/screens/acompanhar/tela_selecionar_ficha_test.dart --name "deve criar instância da tela"
```



## Cobertura dos Testes

Os testes cobrem:

✅ **Estrutura da Classe**
- Verificação se a tela é um StatefulWidget
- Instanciação correta da classe
- Implementação da interface Widget

✅ **Parâmetros do Construtor**
- Parâmetro `programa` opcional (pode ser null)
- Parâmetro `key` opcional
- Diferentes combinações de parâmetros

✅ **Comportamento Básico**
- toString() retorna string válida
- runtimeType correto
- Estrutura de classe adequada

✅ **Validações de Tipo**
- Herança correta de StatefulWidget
- Implementação de interfaces Flutter

## Dependências de Teste

As seguintes dependências são necessárias no `pubspec.yaml`:

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
```

## Notas Importantes

- Os testes são **simples e estruturais** - não testam dependências externas
- **Não usam mocks** para evitar complexidade desnecessária
- **Não testam GetIt** ou injeção de dependências
- Focam apenas na estrutura básica da classe e parâmetros
- Ideais para validar refatorações e mudanças estruturais
- **Execução rápida** sem dependências externas

## Por que Testes Simples?

Esta abordagem foi escolhida porque:
- ✅ **Execução rápida** - sem setup complexo
- ✅ **Sem dependências** - não quebram com mudanças em controladores
- ✅ **Fácil manutenção** - código simples e direto
- ✅ **Validação estrutural** - garantem que a classe está bem formada
- ✅ **CI/CD friendly** - executam em qualquer ambiente
