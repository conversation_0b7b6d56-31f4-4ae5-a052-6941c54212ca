import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:app_treino/screens/acompanhar/TelaSelecionarFicha.dart';

void main() {
  group('TelaSelecionarFicha - Testes Básicos', () {
    testWidgets('deve criar instância da tela', (WidgetTester tester) async {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget, isA<TelaSelecionarFicha>());
      expect(widget, isA<StatefulWidget>());
    });

    test('deve ter programa como parâmetro opcional', () {
      // Arrange & Act
      final widget1 = TelaSelecionarFicha();
      final widget2 = TelaSelecionarFicha(programa: null);

      // Assert
      expect(widget1, isNotNull);
      expect(widget2, isNotNull);
      expect(widget1.programa, isNull);
      expect(widget2.programa, isNull);
    });

    test('deve ter key opcional', () {
      // Arrange & Act
      final widget1 = TelaSelecionarFicha();
      final widget2 = TelaSelecionarFicha(key: const Key('test'));

      // Assert
      expect(widget1.key, isNull);
      expect(widget2.key, isNotNull);
      expect(widget2.key.toString(), contains('test'));
    });

    test('deve ser um StatefulWidget', () {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget, isA<StatefulWidget>());
    });

    test('deve ter runtimeType correto', () {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget.runtimeType.toString(), equals('TelaSelecionarFicha'));
    });

    test('deve implementar interface Widget', () {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget, isA<Widget>());
    });

    test('deve ter toString() válido', () {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget.toString(), isNotNull);
      expect(widget.toString(), isA<String>());
      expect(widget.toString().length, greaterThan(0));
    });
  });

  group('TelaSelecionarFicha - Documentação', () {
    test('deve ter estrutura de classe correta', () {
      // Arrange & Act
      final widget = TelaSelecionarFicha();

      // Assert
      expect(widget, isA<StatefulWidget>());
      expect(widget.runtimeType.toString(), contains('TelaSelecionarFicha'));
    });

    test('deve aceitar parâmetros no construtor', () {
      // Arrange & Act & Assert
      expect(() => TelaSelecionarFicha(), returnsNormally);
      expect(() => TelaSelecionarFicha(programa: null), returnsNormally);
      expect(() => TelaSelecionarFicha(key: const Key('test')), returnsNormally);
      expect(() => TelaSelecionarFicha(key: const Key('test'), programa: null), returnsNormally);
    });
  });
}