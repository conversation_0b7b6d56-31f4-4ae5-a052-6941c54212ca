# TelaAulasTurmas Unit Tests

This directory contains focused unit tests for the `TelaAulasTurmas` screen, specifically testing the null safety improvements that were implemented.

## Test Files Overview

### 1. `tela_aulas_turmas_null_safety_test.dart`
- **Purpose**: Core null safety tests for the main components
- **Coverage**:
  - `ModalidadeSelecionada` class with null values
  - `duracaoAula()` function with null parameters
  - Time parsing and filtering with null values
  - Model null safety tests (ClienteApp, EmpresaRede, HistoricoAula, AulaTurma)
  - Complex null safety edge cases

### 2. `tela_aulas_turmas_methods_test.dart`
- **Purpose**: Method-specific null safety tests
- **Coverage**:
  - String null safety operations
  - Boolean null safety operations
  - Numeric null safety operations
  - Date and time null safety operations
  - List operations with null values
  - Complex null safety scenarios

### 3. `test_runner.dart`
- **Purpose**: Simple test suite runner
- **Usage**: Runs all tests in an organized manner

## Null Safety Improvements Tested

The tests cover all the null safety improvements made to the `TelaAulasTurmas.dart` file:

1. **Nullable User Access**: Tests safe handling of `_controladorCliente.mUsuarioLogado?.nomeEmpresa`
2. **Nullable List Declarations**: Tests proper handling of non-nullable event lists
3. **Date Parsing Null Safety**: Tests null checks for date parsing methods
4. **Configuration Access**: Tests safe handling of nullable configuration values
5. **String Handling**: Tests null coalescing operators for string values
6. **Function Parameter Handling**: Tests safe call operators and fallback values
7. **Boolean Operations**: Tests safe handling of nullable boolean values
8. **Integer Parsing**: Tests `int.tryParse()` with fallback values
9. **Method Parameters**: Tests null safety checks and fallback values

## How to Run Tests

### Prerequisites
1. Ensure you have Flutter installed
2. Make sure all dependencies are installed: `flutter pub get`

### Running Individual Test Files
```bash
# Run core null safety tests
flutter test test/screens/aulas_turmas/tela_aulas_turmas_null_safety_test.dart

# Run methods null safety tests
flutter test test/screens/aulas_turmas/tela_aulas_turmas_methods_test.dart
```

### Running All Tests
```bash
# Run the complete test suite
flutter test test/screens/aulas_turmas/test_runner.dart

# Run all tests in the directory
flutter test test/screens/aulas_turmas/
```

### Running Tests with Coverage
```bash
# Generate coverage report
flutter test --coverage test/screens/aulas_turmas/

# View coverage report (requires lcov)
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## Test Structure

Each test file follows this structure:
- **Test Groups**: Organized by functionality (null safety scenarios)
- **Individual Tests**: Specific null safety edge cases
- **Assertions**: Verify null safety handling works correctly

## Key Testing Patterns

1. **Null Safety Testing**: Every nullable field is tested with null values
2. **Edge Case Coverage**: Invalid data, empty strings, malformed inputs
3. **Error Handling**: Graceful handling of null pointer scenarios
4. **Pure Unit Testing**: No external dependencies or complex mocking

## Expected Outcomes

All tests should pass, demonstrating that:
- The application handles null values gracefully
- No null pointer exceptions are thrown
- Business logic works with missing or invalid data
- Null coalescing operators work correctly
- Time parsing and filtering handle null values safely

## Maintenance

When modifying the `TelaAulasTurmas.dart` file:
1. Update corresponding tests if null safety patterns change
2. Add new tests for new nullable fields or methods
3. Ensure all null safety patterns are tested
4. Run the complete test suite before committing changes

## Troubleshooting

If tests fail:
1. Check for missing null safety operators in the main code
2. Verify that all nullable fields are properly handled
3. Ensure time parsing uses `int.tryParse()` instead of `int.parse()`
4. Check that boolean operations use null coalescing (`??`)
5. Verify that string operations handle null values safely
