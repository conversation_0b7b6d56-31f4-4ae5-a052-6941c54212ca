import 'package:flutter_test/flutter_test.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/Utilitario.dart';

void main() {
  group('Testes Unitários - TelaValidarLoginUsuario', () {
    group('Lógica do nome do usuário', () {
      test('deve extrair primeiro nome quando usuário tem nome completo', () {
        final usuario = Usuario(nome: '<PERSON>');
        final primeiroNome = (usuario.nome ?? '').split(' ')[0].toUpperCase();
        expect(primeiroNome, equals('GABRIEL'));
      });

      test('deve extrair primeiro nome quando usuário tem apenas um nome', () {
        final usuario = Usuario(nome: 'Lucas');
        final primeiroNome = (usuario.nome ?? '').split(' ')[0].toUpperCase();
        expect(primeiroNome, equals('LUCAS'));
      });

      test('deve retornar string vazia quando nome é nulo', () {
        final usuario = Usuario(nome: null);
        final primeiroNome = (usuario.nome ?? '').split(' ')[0].toUpperCase();
        expect(primeiroNome, equals(''));
      });

      test('deve tratar nome com espaços extras', () {
        final usuario = Usuario(nome: '  Ana   Paula  ');
        final primeiroNome = (usuario.nome ?? '').split(' ')[0].toUpperCase();
        expect(primeiroNome, equals(''));
      });
    });

    group('Validação de email do usuário', () {
      test('deve validar email correto', () {
        final usuario = Usuario(email: '<EMAIL>');
        final emailValido = (usuario.email ?? '').contains('@');
        expect(emailValido, isTrue);
      });

      test('deve validar email incorreto', () {
        final usuario = Usuario(email: 'testeemail.com');
        final emailValido = (usuario.email ?? '').contains('@');
        expect(emailValido, isFalse);
      });

      test('deve retornar falso para email nulo', () {
        final usuario = Usuario(email: null);
        final emailValido = (usuario.email ?? '').contains('@');
        expect(emailValido, isFalse);
      });
    });

    group('Validação de estrutura de Column', () {
      test('deve validar estrutura da Column com children', () {
        final children = ['DStextHeadline', 'DStextCaption1', 'DStextfield', 'DSbotaoPadrao'];
        expect(children.length, equals(4));
        expect(children[0], equals('DStextHeadline'));
        expect(children[1], equals('DStextCaption1'));
        expect(children[2], equals('DStextfield'));
        expect(children[3], equals('DSbotaoPadrao'));
      });
    });

    group('Testes de edge cases', () {
      test('deve tratar nome com apenas espaços', () {
        final usuario = Usuario(nome: '   ');
        final primeiroNome = (usuario.nome ?? '').split(' ')[0].toUpperCase();
        expect(primeiroNome, equals(''));
      });

      test('deve tratar email vazio', () {
        final usuario = Usuario(email: '');
        final emailValido = (usuario.email ?? '').contains('@');
        expect(emailValido, isFalse);
      });
    });
  });
}
