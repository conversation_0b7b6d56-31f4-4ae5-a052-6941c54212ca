import 'package:flutter_test/flutter_test.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/contrato/FiltroParcelas.dart';

void main() {
  group('TelaHistoricoDeParcela - Testes de Lógica', () {
    test('deve filtrar parcelas por status TODOS', () {
      // Arrange
      final parcelas = [
        ParcelaContrato(codigo: 1, situacao: 'Aberto'),
        ParcelaContrato(codigo: 2, situacao: 'Pago'),
        ParcelaContrato(codigo: 3, situacao: 'Vencido'),
      ];
      final filtro = EnumFiltrarParcelas.TODOS;

      // Act
      final parcelasFiltradas = _filtrarParcelas(parcelas, filtro);

      // Assert
      expect(parcelasFiltradas.length, equals(3));
    });

    test('deve filtrar parcelas por status ABERTO', () {
      // Arrange
      final parcelas = [
        ParcelaContrato(codigo: 1, situacao: 'Aberto'),
        ParcelaContrato(codigo: 2, situacao: 'Pago'),
        ParcelaContrato(codigo: 3, situacao: 'Aberto'),
      ];
      final filtro = EnumFiltrarParcelas.ABERTO;

      // Act
      final parcelasFiltradas = _filtrarParcelas(parcelas, filtro);

      // Assert
      expect(parcelasFiltradas.length, equals(2));
      expect(parcelasFiltradas.every((p) => p.situacao!.contains('Aberto')), isTrue);
    });

    test('deve filtrar parcelas por status PAGO', () {
      // Arrange
      final parcelas = [
        ParcelaContrato(codigo: 1, situacao: 'Aberto'),
        ParcelaContrato(codigo: 2, situacao: 'Pago'),
        ParcelaContrato(codigo: 3, situacao: 'Pago'),
      ];
      final filtro = EnumFiltrarParcelas.PAGO;

      // Act
      final parcelasFiltradas = _filtrarParcelas(parcelas, filtro);

      // Assert
      expect(parcelasFiltradas.length, equals(2));
      expect(parcelasFiltradas.every((p) => p.situacao!.toUpperCase().contains('PAGO')), isTrue);
    });

    test('deve verificar se parcela está em aberto para seleção', () {
      // Arrange
      final parcelaAberta = ParcelaContrato(codigo: 1, situacao: 'Aberto');
      final parcelaPaga = ParcelaContrato(codigo: 2, situacao: 'Pago');

      // Act & Assert
      expect(_podeSelecionar(parcelaAberta), isTrue);
      expect(_podeSelecionar(parcelaPaga), isFalse);
    });

    test('deve calcular valor total das parcelas selecionadas', () {
      // Arrange
      final parcelasSelecionadas = [
        ParcelaContrato(codigo: 1, valor: 100.0),
        ParcelaContrato(codigo: 2, valor: 150.0),
        ParcelaContrato(codigo: 3, valor: 200.0),
      ];

      // Act
      final valorTotal = parcelasSelecionadas.fold<double>(
        0.0, 
        (sum, parcela) => sum + (parcela.valor ?? 0.0)
      );

      // Assert
      expect(valorTotal, equals(450.0));
    });

    test('deve retornar lista vazia quando não há parcelas', () {
      // Arrange
      final parcelas = <ParcelaContrato>[];
      final filtro = EnumFiltrarParcelas.TODOS;

      // Act
      final parcelasFiltradas = _filtrarParcelas(parcelas, filtro);

      // Assert
      expect(parcelasFiltradas, isEmpty);
    });

    test('deve simular seleção e deseleção de parcelas', () {
      // Arrange
      final parcela = ParcelaContrato(codigo: 1, situacao: 'Aberto', valor: 100.0);
      List<ParcelaContrato> parcelasSelecionadas = [];

      // Act - Selecionar parcela
      if (_podeSelecionar(parcela) && !parcelasSelecionadas.contains(parcela)) {
        parcelasSelecionadas.add(parcela);
      }

      // Assert - Parcela foi selecionada
      expect(parcelasSelecionadas.length, equals(1));
      expect(parcelasSelecionadas.contains(parcela), isTrue);

      // Act - Desselecionar parcela
      if (parcelasSelecionadas.contains(parcela)) {
        parcelasSelecionadas.remove(parcela);
      }

      // Assert - Parcela foi removida
      expect(parcelasSelecionadas.length, equals(0));
      expect(parcelasSelecionadas.contains(parcela), isFalse);
    });

    test('deve verificar se deve exibir botão de pagamento', () {
      // Arrange
      final parcelasSelecionadas = [
        ParcelaContrato(codigo: 1, situacao: 'Aberto'),
      ];

      // Act
      final deveExibirBotao = parcelasSelecionadas.isNotEmpty;

      // Assert
      expect(deveExibirBotao, isTrue);
    });

    test('não deve permitir seleção de parcelas pagas', () {
      // Arrange
      final parcelaPaga = ParcelaContrato(codigo: 1, situacao: 'Pago');
      List<ParcelaContrato> parcelasSelecionadas = [];

      // Act - Tentar selecionar parcela paga
      if (_podeSelecionar(parcelaPaga)) {
        parcelasSelecionadas.add(parcelaPaga);
      }

      // Assert - Parcela não foi selecionada
      expect(parcelasSelecionadas.length, equals(0));
    });

    test('deve filtrar parcelas canceladas', () {
      // Arrange
      final parcelas = [
        ParcelaContrato(codigo: 1, situacao: 'Aberto'),
        ParcelaContrato(codigo: 2, situacao: 'Cancelado'),
        ParcelaContrato(codigo: 3, situacao: 'Pago'),
      ];
      final filtro = EnumFiltrarParcelas.CANCELADO;

      // Act
      final parcelasFiltradas = _filtrarParcelas(parcelas, filtro);

      // Assert
      expect(parcelasFiltradas.length, equals(1));
      expect(parcelasFiltradas.first.situacao, equals('Cancelado'));
    });
  });
}

// Funções auxiliares para simular a lógica da tela
List<ParcelaContrato> _filtrarParcelas(List<ParcelaContrato> parcelas, EnumFiltrarParcelas filtro) {
  switch (filtro) {
    case EnumFiltrarParcelas.TODOS:
      return parcelas;
    case EnumFiltrarParcelas.ABERTO:
      return parcelas.where((p) => p.situacao!.contains('Aberto')).toList();
    case EnumFiltrarParcelas.PAGO:
      return parcelas.where((p) => p.situacao!.toUpperCase().contains('PAGO')).toList();
    case EnumFiltrarParcelas.CANCELADO:
      return parcelas.where((p) => p.situacao!.contains('Cancelado')).toList();
  }
}

bool _podeSelecionar(ParcelaContrato parcela) {
  return parcela.situacao != null && parcela.situacao!.contains('Aberto');
}
