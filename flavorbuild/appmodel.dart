class AppModel {
  final String name;
  final String documentNumber;
  final String p8base64;
  final String issuerID;
  final String p8id;
  final String googleServicesJson;
  final String color;
  final String icon;
  final String bundleIDIOS;
  final String bundleIDAndroid;
  final String registroFeitoPor;
 
  String get flavorName => name.toLowerCase().replaceAll(' ', '_');

  AppModel(
      {required this.name,
      required this.documentNumber,
      required this.p8base64,
      required this.issuerID,
      required this.p8id,
      required this.googleServicesJson,
      required this.color,
      required this.icon,
      required this.bundleIDIOS,
      required this.bundleIDAndroid,
      required this.registroFeitoPor});
}
