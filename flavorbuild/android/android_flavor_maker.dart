import 'dart:convert';

import '../appmodel.dart';
import 'dart:io';

import '../util.dart';
import 'model/gradle_flavor.dart';

class AndroidFlavorMaker {
  final AppModel appModel;
  AndroidFlavorMaker(this.appModel);

  // Esse metodo cria a flavor
  Future<bool> criarFlavor() {
    return insertOrUpdateBuildGradle().then((onValue) {
      return insertImages();
    }).then((onValue) {
      return createGoogleServicesJson();
    }).then((onValue) => true);
  }

  // Esse metodo insere os dados da flavor no andoird/app/src/build.gradle
  Future<bool> insertOrUpdateBuildGradle() async {
    final flavorGradle = GladleFlavor(
      flavorName: appModel.flavorName,
      desktopName: appModel.name,
      applicationId: appModel.bundleIDAndroid,
    );
    final filePath = '/Users/<USER>/Documents/projetos/pacto/apps/app_treinox/android/app/build.gradle';
    final file = File(filePath);

    if (await file.exists()) {
      final content = await file.readAsLinesSync();
      final indexExistis = content.indexWhere((element) => element.contains(flavorGradle.flavorName));
      if (indexExistis != -1) {
        print('Flavor ${flavorGradle.flavorName} already exists');
        content.removeRange(indexExistis, indexExistis + 7);
      }
      final index = content.indexWhere((element) => element.contains('productFlavors {'));
      flavorGradle.toGradleFormat().split('\n').reversed.forEach((element) {
        content.insert(index + 1, element);
      });
      await file.writeAsString(content.join('\n'));
      return true;
    } else {
      print('File not found: $filePath');
      return false;
    }
  }

  // Esse metodo insere as imagens da flavor no andoird/app/src/main/res
  Future<bool> insertImages() async {
    final images = [
      'drawable',
      'mipmap-hdpi',
      'mipmap-mdpi',
      'mipmap-xhdpi',
      'mipmap-xxhdpi',
      'mipmap-xxxhdpi',
    ];

    for (final image in images) {
      for (final imageFile in [this.appModel.icon]) {
        final file = File('$imageFile');
        if (await file.exists()) {
          print('File ${file.path} already exists');
        } else {
          print('Creating file ${file.path}');
          File iconFile = File(FlavorUtil.androidPath + '$image' + '/ic_launcher.png');
          iconFile.writeAsBytesSync(file.readAsBytesSync());
          await file.create(recursive: true);
        }
      }
    }
    return true;
  }

  // Cria um fake do google-services.json
  Future<bool> createGoogleServicesJson() async {
    final file = File(FlavorUtil.androidPath + 'app/src/main/assets/google-services.json').readAsStringSync();
    final json = JsonCodec().decode(file);
    (json['client'] as List<Map<String, dynamic>>).add({
      'client_info': {
        'mobilesdk_app_id': '1:977449544580:android:c14fd61b5021acde',
        'android_client_info': {'package_name': '${appModel.bundleIDAndroid}'}
      },
      'oauth_client': [
        {
          'client_id': '977449544580-en05mt90k901jp29vhujivqnpn2r7cko.apps.googleusercontent.com',
          'client_type': 1,
          'android_info': {'package_name': '${appModel.bundleIDAndroid}', 'certificate_hash': 'f58363ab7bb3d59db8350f1564c9d5802d3da849'}
        },
        {
          'client_id': '977449544580-f2too0ge42e947u20s74tp2a1mg5njbb.apps.googleusercontent.com',
          'client_type': 1,
          'android_info': {'package_name': '${appModel.bundleIDAndroid}', 'certificate_hash': 'b27fa5ac9a117937ec778bc4045e7f98f8a20302'}
        },
        {'client_id': '977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com', 'client_type': 3}
      ],
      'api_key': [
        {'current_key': 'AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk'}
      ],
      'services': {
        'appinvite_service': {
          'other_platform_oauth_client': [
            {'client_id': '977449544580-c2jgirv6fpof7mfa1jqh5gab6dt83a48.apps.googleusercontent.com', 'client_type': 3},
            {
              'client_id': '977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com',
              'client_type': 2,
              'ios_info': {'bundle_id': '${appModel.bundleIDAndroid}', 'app_store_id': '862662527'}
            }
          ]
        }
      }
    });
    return true;
  }
}
