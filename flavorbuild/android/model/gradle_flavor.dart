class GladleFlavor {
  String flavorName;
  String desktopName;
  Map<String, String> manifestPlaceholders;
  String dimension;
  String applicationId;
  String signingConfig;

  GladleFlavor({
    required this.flavorName,
    required this.desktopName,
    required this.applicationId,
    this.manifestPlaceholders = const {},
    this.dimension = 'flavor-type',
    this.signingConfig = 'signingConfigs.release_treino',
  });

  String toGradleFormat() {
    if (manifestPlaceholders.isEmpty) {
      manifestPlaceholders = {
        'appID': 'ca-app-pub-3678266089524496~6348268772',
        'googleFitOauthClientId': '977449544580-598i2vp5ul2qoljc7tle09njm9u0gntj.apps.googleusercontent.com',
      };
    }
    return '''
        $flavorName {
            manifestPlaceholders = [${manifestPlaceholders.entries.map((e) => '${e.key}: "${e.value}"').join(', ')}]
            dimension "$dimension"
            applicationId "$applicationId"
            resValue "string", "app_name", "$desktopName"
            signingConfig signingConfigs.$signingConfig
        }
    ''';
  }
}
