require 'json'
require 'uri'
require 'net/http'
require 'base64'
require 'fileutils'
require 'tempfile'

module AppPersonalizadoHelper
  API_URL = "https://app-do-aluno-unificado.web.app/clienteApp/consultarAppPersonalizado"
  
  # Consulta a API para obter as configurações do app personalizado
  def self.fetch_app_config(document_key)
    begin
      # Constrói a URL com o document_key
      uri = URI("#{API_URL}?documentKey=#{document_key}")
      
      # Configura os headers da requisição
      headers = {
        'Authorization' => 'functionAlunoAntigo',
        'mc' => 'Mobile Center BuildNumber'
      }
      
      # Realiza a requisição
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      request = Net::HTTP::Get.new(uri.request_uri, headers)
      response = http.request(request)
      
      # Verifica se a resposta foi bem-sucedida
      if response.code != '200'
        UI.error("Falha ao consultar a API: #{response.code} - #{response.body}")
        return nil
      end
      
      # Processa o JSON da resposta
      json_response = JSON.parse(response.body)
      
      # Verifica se a resposta contém o campo 'sucesso'
      unless json_response['sucesso']
        UI.error("Resposta da API não contém o campo 'sucesso': #{response.body}")
        return nil
      end
      
      # Extrai as informações relevantes
      app_data = json_response['sucesso']
      
      # Monta o hash com as configurações do app
      config = {
        label: app_data['googlePlayLabel'] || "App Personalizado",
        package_name: app_data['googlePlayPackageName'] || "com.example.app_personalizado",
        bundle_id: app_data['iosBundleId'] || "com.example.appPersonalizado",
        bundle_id_watch: app_data['iosBundleIdWatch'] || "com.example.appPersonalizado.watch",
        app_name_android: app_data['googlePlayLabel'] || "App Personalizado",
        app_name_ios: app_data['iosLabel'] || "App Personalizado iOS",
        icon_path_android: app_data['googlePlayIcon'] || "",
        icon_path_ios: app_data['iosIcon'] || "",
        firebase_android_b64: app_data['firebaseGoogleServicesJsonAndroid'] || "",
        firebase_ios_b64: app_data['firebaseGoogleServicesJsonIOS'] || "",
        version: app_data['googlePlayVersion'] || "1.0.0",
        ios_p8_file: app_data['iosP8File'] || "",
        ios_p8_password: app_data['iosP8Password'] || "",
        ios_team_id: app_data['IOSTeamID'] || "",
        ios_issuer_id: app_data['iosIssuerId'] || "",
        document_key: document_key
      }
      
      return config
    rescue => e
      UI.error("Erro ao processar a requisição: #{e.message}")
      UI.error(e.backtrace.join("\n"))
      return nil
    end
  end
  
  # Configura o arquivo google-services.json para Android
  def self.setup_firebase_android(app_config)
    return unless app_config[:firebase_android_b64] && !app_config[:firebase_android_b64].empty?
    
    begin
      # Diretório do flavor personalizado
      flavor_dir = "../android/app/src/apppersonalizado"
      FileUtils.mkdir_p(flavor_dir) unless Dir.exist?(flavor_dir)
      
      # Caminho para o arquivo google-services.json
      firebase_file = "#{flavor_dir}/google-services.json"
      
      # Decodifica o conteúdo base64 e salva no arquivo
      File.open(firebase_file, 'wb') do |file|
        file.write(Base64.decode64(app_config[:firebase_android_b64]))
      end
      
      # Verifica se o arquivo foi criado corretamente
      if File.exist?(firebase_file) && !File.zero?(firebase_file)
        UI.success("Arquivo google-services.json criado com sucesso em #{firebase_file}")
      else
        UI.error("Falha ao criar o arquivo google-services.json")
      end
    rescue => e
      UI.error("Erro ao configurar Firebase para Android: #{e.message}")
      UI.error(e.backtrace.join("\n"))
    end
  end
  
  # Configura o arquivo GoogleService-Info.plist para iOS
  def self.setup_firebase_ios(app_config)
    return unless app_config[:firebase_ios_b64] && !app_config[:firebase_ios_b64].empty?
    
    begin
      # Diretório para o arquivo do Firebase iOS
      ios_dir = "../ios/Runner"
      FileUtils.mkdir_p(ios_dir) unless Dir.exist?(ios_dir)
      
      # Caminho para o arquivo GoogleService-Info.plist
      firebase_file = "#{ios_dir}/GoogleService-Info.plist"
      
      # Decodifica o conteúdo base64 e salva no arquivo
      File.open(firebase_file, 'wb') do |file|
        file.write(Base64.decode64(app_config[:firebase_ios_b64]))
      end
      
      # Verifica se o arquivo foi criado corretamente
      if File.exist?(firebase_file) && !File.zero?(firebase_file)
        UI.success("Arquivo GoogleService-Info.plist criado com sucesso em #{firebase_file}")
      else
        UI.error("Falha ao criar o arquivo GoogleService-Info.plist")
      end
    rescue => e
      UI.error("Erro ao configurar Firebase para iOS: #{e.message}")
      UI.error(e.backtrace.join("\n"))
    end
  end
  
  # Configura os certificados para iOS
  def self.setup_ios_certificates(app_config)
    return unless app_config[:ios_p8_file] && !app_config[:ios_p8_file].empty?
    
    begin
      # Diretório para os certificados
      cert_dir = "../ios/certs"
      FileUtils.mkdir_p(cert_dir) unless Dir.exist?(cert_dir)
      
      # Caminho para o arquivo .p8
      p8_file = "#{cert_dir}/AuthKey.p8"
      
      # Decodifica o conteúdo base64 e salva no arquivo
      File.open(p8_file, 'wb') do |file|
        file.write(Base64.decode64(app_config[:ios_p8_file]))
      end
      
      # Configura o App Store Connect API
      app_store_connect_api_key(
        key_id: app_config[:ios_p8_password],
        issuer_id: app_config[:ios_issuer_id],
        key_filepath: p8_file,
        in_house: false
      )
      
      UI.success("Certificados iOS configurados com sucesso")
    rescue => e
      UI.error("Erro ao configurar certificados iOS: #{e.message}")
      UI.error(e.backtrace.join("\n"))
    end
  end
end
