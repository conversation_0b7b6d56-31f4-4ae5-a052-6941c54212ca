# O app_identifier e package_name serão substituídos dinamicamente com base na resposta da API
app_identifier ENV["APP_IDENTIFIER"] # para iOS
package_name ENV["PACKAGE_NAME"] # para Android

# As credenciais de acesso serão configuradas através de variáveis de ambiente
apple_id ENV["APPLE_ID"]
team_id ENV["TEAM_ID"]
itc_team_id ENV["ITC_TEAM_ID"]

# As credenciais do Google Play serão fornecidas por um arquivo JSON
json_key_file ENV["GOOGLE_PLAY_JSON_KEY_FILE"]
