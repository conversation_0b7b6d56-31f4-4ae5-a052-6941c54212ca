#!/bin/bash
# exemplo de uso ./flavor.sh AueZMA6GQEXGXhRKGYZ5

# --- Passo 1: Verificações Iniciais e Validação de Entrada ---
echo "🚀 Passo 1: Verificações Iniciais e Validação de Entrada"
# Store the document key from command line argument
DOCUMENT_KEY="$1"
echo "🔑 Chave do Documento: $DOCUMENT_KEY"
echo "🏁 Passo 1 concluído."
echo "---"

# --- Passo 2: Buscar Configuração da API ---
echo "🚀 Passo 2: Buscar Configuração da API"
# Make the curl request and store response in a variable
RESPONSE_RAW=$(curl -s --location "https://app-do-aluno-unificado.web.app/clienteApp/consultarAppPersonalizado?documentKey=${DOCUMENT_KEY}" \
--header 'Authorization: functionAlunoAntigo' \
--header 'mc: Mobile Center BuildNumber')

# Check curl exit status
if [ $? -ne 0 ]; then
    echo "❌ Erro: Falha ao buscar configuração da API."
    exit 1
fi

# Check if response is empty or not valid JSON
if [ -z "$RESPONSE_RAW" ] || ! echo "$RESPONSE_RAW" | jq empty; then
    echo "❌ Erro: Resposta da API vazia ou inválida."
    echo "Resposta recebida: $RESPONSE_RAW"
    exit 1
fi

RESPONSE=$(echo "$RESPONSE_RAW" | jq -r '.sucesso')
# Check if .sucesso exists and is not null
if [ -z "$RESPONSE" ] || [ "$RESPONSE" == "null" ]; then
   echo "❌ Erro: Campo '.sucesso' não encontrado ou nulo na resposta da API."
   echo "Resposta completa: $RESPONSE_RAW"
   exit 1
fi

echo "✅ Configuração da API buscada com sucesso."
echo "🏁 Passo 2 concluído."
echo "---"

# --- Passo 3: Parsear Configuração JSON ---
echo "🚀 Passo 3: Parsear Configuração JSON"
# Parse the JSON response
LABEL=$(echo $RESPONSE | jq -r '.googlePlayLabel // "App Personalizado"')
PACKAGE_NAME=$(echo $RESPONSE | jq -r '.googlePlayPackageName // "com.example.app_personalizado"')
BUNDLE_ID_IOS=$(echo $RESPONSE | jq -r '.iosBundleId // "com.example.appPersonalizado"')
BUNDLE_ID_IOS_APPLEWATCH=$(echo $RESPONSE | jq -r '.iosBundleIdWatch // "com.example.appPersonalizado.watch"')
APP_NAME_ANDROID=$(echo $RESPONSE | jq -r '.googlePlayLabel // "App Personalizado"')
APP_NAME_IOS=$(echo $RESPONSE | jq -r '.iosLabel // "App Personalizado iOS"')

echo "📱 Package Name (Android): $PACKAGE_NAME"
echo "📱 Bundle ID (iOS): $BUNDLE_ID_IOS"
echo "🏁 Passo 3 concluído."
echo "---"

# --- Passo 4: Verificar se os apps estão vinculados ao Firebase ---
echo "🚀 Passo 4: Verificar vinculação com Firebase"

# Verificar e instalar dependências necessárias
echo "🔍 Verificando dependências..."

# Verificar se jq está instalado
if ! command -v jq &> /dev/null; then
    echo "⚠️ jq não encontrado. Tentando instalar..."
    
    # Detectar o sistema operacional
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install jq
        else
            echo "❌ Homebrew não encontrado. Por favor, instale o jq manualmente: https://stedolan.github.io/jq/download/"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum &> /dev/null; then
            sudo yum install -y jq
        else
            echo "❌ Gerenciador de pacotes não reconhecido. Por favor, instale o jq manualmente: https://stedolan.github.io/jq/download/"
            exit 1
        fi
    else
        echo "❌ Sistema operacional não suportado. Por favor, instale o jq manualmente: https://stedolan.github.io/jq/download/"
        exit 1
    fi
    
    # Verificar novamente se a instalação foi bem-sucedida
    if ! command -v jq &> /dev/null; then
        echo "❌ Falha ao instalar jq. Por favor, instale manualmente."
        exit 1
    fi
    echo "✅ jq instalado com sucesso."
fi

# Verificar se openssl está instalado
if ! command -v openssl &> /dev/null; then
    echo "❌ openssl não encontrado. Por favor, instale o openssl para continuar."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "No macOS: brew install openssl"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "No Linux (Ubuntu/Debian): sudo apt-get install openssl"
        echo "No Linux (CentOS/RHEL): sudo yum install openssl"
    fi
    exit 1
fi

# Configuração do Firebase via Service Account
FIREBASE_PROJECT_ID="app-aluno-custom"
SERVICE_ACCOUNT_FILE="./.cert/app-aluno-custom-1b2b327efe5f.json"

# Verificar se o arquivo de service account existe
if [ ! -f "$SERVICE_ACCOUNT_FILE" ]; then
    echo "❌ Erro: Arquivo de credenciais do Firebase não encontrado: $SERVICE_ACCOUNT_FILE"
    exit 1
fi

# Obter token de acesso usando JWT diretamente sem gcloud
echo "🔐 Obtendo token de acesso ao Firebase..."

# Extrair valores necessários do arquivo de credenciais
CLIENT_EMAIL=$(jq -r '.client_email' "$SERVICE_ACCOUNT_FILE")
PRIVATE_KEY=$(jq -r '.private_key' "$SERVICE_ACCOUNT_FILE" | sed 's/\\n/\n/g')

# Criar cabeçalho JWT
JWT_HEADER='{"alg":"RS256","typ":"JWT"}'
JWT_HEADER_BASE64=$(echo -n "$JWT_HEADER" | openssl base64 -e | tr '+/' '-_' | tr -d '=\n')

# Tempo atual e de expiração (1 hora)
CURRENT_TIME=$(date +%s)
EXPIRATION_TIME=$((CURRENT_TIME + 3600))

# Criar payload JWT
JWT_PAYLOAD="{\"iss\":\"$CLIENT_EMAIL\",\"sub\":\"$CLIENT_EMAIL\",\"aud\":\"https://oauth2.googleapis.com/token\",\"iat\":$CURRENT_TIME,\"exp\":$EXPIRATION_TIME,\"scope\":\"https://www.googleapis.com/auth/firebase https://www.googleapis.com/auth/cloud-platform\"}"
JWT_PAYLOAD_BASE64=$(echo -n "$JWT_PAYLOAD" | openssl base64 -e | tr '+/' '-_' | tr -d '=\n')

# Criar assinatura
JWT_UNSIGNED="$JWT_HEADER_BASE64.$JWT_PAYLOAD_BASE64"
JWT_SIGNATURE=$(echo -n "$JWT_UNSIGNED" | openssl dgst -sha256 -sign <(echo -n "$PRIVATE_KEY") -binary | openssl base64 -e | tr '+/' '-_' | tr -d '=\n')

# JWT completo
JWT="$JWT_UNSIGNED.$JWT_SIGNATURE"

# Obter token de acesso
TOKEN_RESPONSE=$(curl -s -X POST "https://oauth2.googleapis.com/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=$JWT")

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')

if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
    echo "❌ Erro: Falha ao obter token de acesso ao Firebase."
    echo "Resposta: $TOKEN_RESPONSE"
    exit 1
fi

echo "✅ Token de acesso obtido com sucesso."

# Função para verificar se o app já está vinculado a outro projeto
check_app_exists_elsewhere() {
    local error_response="$1"
    local app_type="$2"
    local identifier="$3"
    
    # Verificar se a mensagem de erro indica que o app já está registrado em outro projeto
    if [[ "$error_response" == *"already exists in another Firebase project"* ]] || 
       [[ "$error_response" == *"already exists in project"* ]] || 
       [[ "$error_response" == *"is already registered"* ]]; then
        
        echo "⚠️ O $app_type com identificador '$identifier' já está registrado em outro projeto Firebase."
        
        # Extrair informações adicionais se disponíveis
        local project_id=$(echo "$error_response" | grep -o "project [^ ]*" | sed 's/project //' || echo "desconhecido")
        
        if [[ "$project_id" != "desconhecido" ]]; then
            echo "🔍 Projeto Firebase: $project_id"
        fi
        
        echo "ℹ️ Você precisa remover o app do outro projeto primeiro ou usar um identificador diferente."
        return 0
    fi
    
    return 1
}

# Verificar app Android
echo "🔍 Verificando app Android no Firebase..."
ANDROID_APP_RESPONSE=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/androidApps" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

# Verificar se a resposta está vazia ou contém erro
if [ -z "$ANDROID_APP_RESPONSE" ] || [[ "$ANDROID_APP_RESPONSE" == *"error"* ]]; then
    echo "❌ Erro ao obter apps Android do Firebase:"
    echo "$ANDROID_APP_RESPONSE" | jq
    exit 1
fi

# Verificar se o campo apps existe na resposta
if ! echo "$ANDROID_APP_RESPONSE" | jq -e '.apps' > /dev/null 2>&1; then
    echo "⚠️ Não há apps Android registrados no Firebase."
    ANDROID_APP_EXISTS=""
else
    ANDROID_APP_EXISTS=$(echo "$ANDROID_APP_RESPONSE" | jq -r ".apps[] | select(.packageName == \"$PACKAGE_NAME\") | .appId")
fi

if [ -z "$ANDROID_APP_EXISTS" ]; then
    echo "⚠️ App Android não encontrado no Firebase. Vinculando..."
    
    # Criar app Android no Firebase
    ANDROID_CREATE_RESPONSE=$(curl -s -X POST "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/androidApps" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: application/json" \
      -d "{\"packageName\":\"$PACKAGE_NAME\",\"displayName\":\"$APP_NAME_ANDROID\"}")
    
    # Verificar se a operação foi iniciada corretamente
    if [[ "$ANDROID_CREATE_RESPONSE" == *"operations/workflows"* ]]; then
        OPERATION_NAME=$(echo "$ANDROID_CREATE_RESPONSE" | jq -r '.name')
        echo "⏳ Operação iniciada: $OPERATION_NAME"
        
        # Aguardar que a operação seja concluída
        echo "⏳ Aguardando a conclusão da operação..."
        MAX_ATTEMPTS=5
        ATTEMPTS=0
        OPERATION_DONE=false
        
        while [ $ATTEMPTS -lt $MAX_ATTEMPTS ] && [ "$OPERATION_DONE" == "false" ]; do
            sleep 3
            OPERATION_STATUS=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/$OPERATION_NAME" \
              -H "Authorization: Bearer $ACCESS_TOKEN")
            
            if [[ "$OPERATION_STATUS" == *"\"done\":true"* ]]; then
                OPERATION_DONE=true
                echo "✅ Operação concluída."
                
                # Verificar se a operação foi bem-sucedida
                if [[ "$OPERATION_STATUS" == *"error"* ]]; then
                    echo "❌ Erro na operação:"
                    echo "$OPERATION_STATUS" | jq
                    break
                else
                    # Buscar o appId após a criação bem-sucedida
                    sleep 2
                    ANDROID_APPS=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/androidApps" \
                      -H "Authorization: Bearer $ACCESS_TOKEN")
                    
                    NEW_APP_ID=$(echo "$ANDROID_APPS" | jq -r ".apps[] | select(.packageName == \"$PACKAGE_NAME\") | .appId")
                    
                    if [ -n "$NEW_APP_ID" ]; then
                        echo "✅ App Android vinculado com sucesso! App ID: $NEW_APP_ID"
                    else
                        echo "⚠️ App criado, mas não foi possível obter o App ID."
                    fi
                fi
            else
                ATTEMPTS=$((ATTEMPTS+1))
                echo "⏳ Aguardando... Tentativa $ATTEMPTS de $MAX_ATTEMPTS"
            fi
        done
        
        if [ "$OPERATION_DONE" == "false" ]; then
            echo "⚠️ Tempo limite excedido aguardando a operação. O app pode ter sido criado, mas não foi possível confirmar."
        fi
    else
        # Verificar se é um erro de app já existente em outro projeto
        if check_app_exists_elsewhere "$ANDROID_CREATE_RESPONSE" "app Android" "$PACKAGE_NAME"; then
            # Não é necessário fazer mais nada, a função já exibiu as mensagens relevantes
            echo "🔍 Tente usar o comando: firebase projects:list"
            echo "🔍 Para ver todos os projetos da sua conta Firebase."
        else
            # Tentar extrair o appId diretamente se disponível
            ANDROID_APP_ID=$(echo "$ANDROID_CREATE_RESPONSE" | jq -r '.appId')
            
            if [ -z "$ANDROID_APP_ID" ] || [ "$ANDROID_APP_ID" == "null" ]; then
                echo "❌ Erro ao vincular app Android ao Firebase:"
                echo "$ANDROID_CREATE_RESPONSE" | jq
            else
                echo "✅ App Android vinculado com sucesso! App ID: $ANDROID_APP_ID"
            fi
        fi
    fi
else
    echo "✅ App Android já está vinculado ao Firebase. App ID: $ANDROID_APP_EXISTS"
fi

# Verificar app iOS
echo "🔍 Verificando app iOS no Firebase..."
IOS_APP_RESPONSE=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/iosApps" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

# Verificar se a resposta está vazia ou contém erro
if [ -z "$IOS_APP_RESPONSE" ] || [[ "$IOS_APP_RESPONSE" == *"error"* ]]; then
    echo "❌ Erro ao obter apps iOS do Firebase:"
    echo "$IOS_APP_RESPONSE" | jq
    exit 1
fi

# Verificar se o campo apps existe na resposta
if ! echo "$IOS_APP_RESPONSE" | jq -e '.apps' > /dev/null 2>&1; then
    echo "⚠️ Não há apps iOS registrados no Firebase."
    IOS_APP_EXISTS=""
else
    IOS_APP_EXISTS=$(echo "$IOS_APP_RESPONSE" | jq -r ".apps[] | select(.bundleId == \"$BUNDLE_ID_IOS\") | .appId")
fi

if [ -z "$IOS_APP_EXISTS" ]; then
    echo "⚠️ App iOS não encontrado no Firebase. Vinculando..."
    
    # Criar app iOS no Firebase
    IOS_CREATE_RESPONSE=$(curl -s -X POST "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/iosApps" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: application/json" \
      -d "{\"bundleId\":\"$BUNDLE_ID_IOS\",\"displayName\":\"$APP_NAME_IOS\"}")
    
    # Verificar se a operação foi iniciada corretamente
    if [[ "$IOS_CREATE_RESPONSE" == *"operations/workflows"* ]]; then
        OPERATION_NAME=$(echo "$IOS_CREATE_RESPONSE" | jq -r '.name')
        echo "⏳ Operação iniciada: $OPERATION_NAME"
        
        # Aguardar que a operação seja concluída
        echo "⏳ Aguardando a conclusão da operação..."
        MAX_ATTEMPTS=5
        ATTEMPTS=0
        OPERATION_DONE=false
        
        while [ $ATTEMPTS -lt $MAX_ATTEMPTS ] && [ "$OPERATION_DONE" == "false" ]; do
            sleep 3
            OPERATION_STATUS=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/$OPERATION_NAME" \
              -H "Authorization: Bearer $ACCESS_TOKEN")
            
            if [[ "$OPERATION_STATUS" == *"\"done\":true"* ]]; then
                OPERATION_DONE=true
                echo "✅ Operação concluída."
                
                # Verificar se a operação foi bem-sucedida
                if [[ "$OPERATION_STATUS" == *"error"* ]]; then
                    echo "❌ Erro na operação:"
                    echo "$OPERATION_STATUS" | jq
                    break
                else
                    # Buscar o appId após a criação bem-sucedida
                    sleep 2
                    IOS_APPS=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$FIREBASE_PROJECT_ID/iosApps" \
                      -H "Authorization: Bearer $ACCESS_TOKEN")
                    
                    NEW_APP_ID=$(echo "$IOS_APPS" | jq -r ".apps[] | select(.bundleId == \"$BUNDLE_ID_IOS\") | .appId")
                    
                    if [ -n "$NEW_APP_ID" ]; then
                        echo "✅ App iOS vinculado com sucesso! App ID: $NEW_APP_ID"
                    else
                        echo "⚠️ App criado, mas não foi possível obter o App ID."
                    fi
                fi
            else
                ATTEMPTS=$((ATTEMPTS+1))
                echo "⏳ Aguardando... Tentativa $ATTEMPTS de $MAX_ATTEMPTS"
            fi
        done
        
        if [ "$OPERATION_DONE" == "false" ]; then
            echo "⚠️ Tempo limite excedido aguardando a operação. O app pode ter sido criado, mas não foi possível confirmar."
        fi
    else
        # Verificar se é um erro de app já existente em outro projeto
        if check_app_exists_elsewhere "$IOS_CREATE_RESPONSE" "app iOS" "$BUNDLE_ID_IOS"; then
            # Não é necessário fazer mais nada, a função já exibiu as mensagens relevantes
            echo "🔍 Tente usar o comando: firebase projects:list"
            echo "🔍 Para ver todos os projetos da sua conta Firebase."
        else
            # Tentar extrair o appId diretamente se disponível
            IOS_APP_ID=$(echo "$IOS_CREATE_RESPONSE" | jq -r '.appId')
            
            if [ -z "$IOS_APP_ID" ] || [ "$IOS_APP_ID" == "null" ]; then
                echo "❌ Erro ao vincular app iOS ao Firebase:"
                echo "$IOS_CREATE_RESPONSE" | jq
            else
                echo "✅ App iOS vinculado com sucesso! App ID: $IOS_APP_ID"
            fi
        fi
    fi
else
    echo "✅ App iOS já está vinculado ao Firebase. App ID: $IOS_APP_EXISTS"
fi

echo "🏁 Passo 4 concluído."
echo "---"

# # --- Passo 5: Verificação Adicional em Outros Projetos (Opcional) ---
# echo "🚀 Passo 5: Verificação Adicional em Outros Projetos"
# echo "ℹ️ Esta verificação é limitada aos projetos aos quais sua conta de serviço tem acesso."

# # Verificar se firebase-tools está instalado para verificações adicionais
# if command -v firebase &> /dev/null; then
#     echo "📋 Listando projetos Firebase disponíveis:"
#     firebase projects:list
    
#     echo "ℹ️ Para verificar apps em um projeto específico, você pode usar:"
#     echo "   firebase apps:list --project=<project-id>"
#     echo "   firebase apps:android:list --project=<project-id>"
#     echo "   firebase apps:ios:list --project=<project-id>"
# else
#     echo "ℹ️ Para verificações adicionais, instale firebase-tools:"
#     echo "   npm install -g firebase-tools"
#     echo "   firebase login"
#     echo "   firebase projects:list"
# fi

# Tentar listar todos os projetos acessíveis via API
echo "🔍 Tentando listar projetos Firebase via API..."
PROJECTS_RESPONSE=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

# Verificar se a resposta contém projetos
if [[ "$PROJECTS_RESPONSE" == *"projects"* ]]; then
    echo "✅ Projetos Firebase encontrados:"
    echo "$PROJECTS_RESPONSE" | jq -r '.projects[] | "- \(.projectId): \(.displayName)"'
    
    # Para cada projeto, verificar se contém os apps
    echo "🔍 Verificando apps em cada projeto..."
    PROJECT_IDS=$(echo "$PROJECTS_RESPONSE" | jq -r '.projects[].projectId')
    
    for PROJECT in $PROJECT_IDS; do
        echo "📱 Verificando projeto: $PROJECT"
        
        # Verificar apps Android
        ANDROID_CHECK=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$PROJECT/androidApps" \
          -H "Authorization: Bearer $ACCESS_TOKEN")
        
        if [[ "$ANDROID_CHECK" == *"$PACKAGE_NAME"* ]]; then
            echo "⚠️ App Android com package '$PACKAGE_NAME' encontrado no projeto $PROJECT"
            APP_ID=$(echo "$ANDROID_CHECK" | jq -r ".apps[] | select(.packageName == \"$PACKAGE_NAME\") | .appId")
            if [ -n "$APP_ID" ]; then
                echo "   App ID: $APP_ID"
            fi
        fi
        
        # Verificar apps iOS
        IOS_CHECK=$(curl -s -X GET "https://firebase.googleapis.com/v1beta1/projects/$PROJECT/iosApps" \
          -H "Authorization: Bearer $ACCESS_TOKEN")
        
        if [[ "$IOS_CHECK" == *"$BUNDLE_ID_IOS"* ]]; then
            echo "⚠️ App iOS com bundle ID '$BUNDLE_ID_IOS' encontrado no projeto $PROJECT"
            APP_ID=$(echo "$IOS_CHECK" | jq -r ".apps[] | select(.bundleId == \"$BUNDLE_ID_IOS\") | .appId")
            if [ -n "$APP_ID" ]; then
                echo "   App ID: $APP_ID"
            fi
        fi
    done
else
    echo "ℹ️ Não foi possível listar todos os projetos Firebase. Acesso pode ser limitado."
fi

echo "🏁 Passo 5 concluído."
echo "---"

echo "✨ Processo finalizado com sucesso! ✨"

