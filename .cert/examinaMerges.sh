#!/bin/bash

# Sin<PERSON>roniza as tags do servidor

# Lista as últimas 20 tags (mais recentes primeiro)
tags=$(git tag --sort=-creatordate | head -n 20)

echo "Selecione a tag de início (mais recentes primeiro):"
echo "0) Tag atual"
select tag_inicio in $tags; do
    if [ "$REPLY" -eq 0 ]; then
        tag_inicio=$(git describe --tags --abbrev=0)
    fi
    break
done

echo "Selecione a tag de fim (mais recentes primeiro):"
echo "0) Tag atual"
select tag_fim in $tags; do
    if [ "$REPLY" -eq 0 ]; then
        tag_fim=$(git describe --tags --abbrev=0)
    fi
    break
done

# Expressão regular para capturar o padrão APPS-XXXX
pattern="APPS-[0-9]{4,}" # Ajustado para pegar 4 ou mais dígitos

echo "" # Linha em branco para separar
echo "Merges APPS-XXXX entre ${tag_inicio} e ${tag_fim}:"

# Listar todos os commits de merge entre tag_inicio e tag_fim com o padrão APPS-XXXX
git log "${tag_inicio}".."${tag_fim}" --merges --pretty=format:"%h - %s" | while read -r line; do
  if [[ $line =~ $pattern ]]; then
    # Extrair o ID APPS-XXXX do log
    id="${BASH_REMATCH[0]}"
    # Concatenar o link com o ID
    jira_link="https://pacto.atlassian.net/browse/$id"
    # Exibir o commit e o link do JIRA
    echo "$line -> $jira_link"
  fi
done

# Verifica se algum commit foi encontrado (opcional, mas útil)
commit_count=$(git log "${tag_inicio}".."${tag_fim}" --merges --pretty=format:"%h - %s" | grep -c -E "$pattern")
if [ "$commit_count" -eq 0 ]; then
    echo "Nenhum merge APPS-XXXX encontrado entre ${tag_inicio} e ${tag_fim}."
fi
