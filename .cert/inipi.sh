#!/bin/bash
set -e

# Nome do app e versão da tag -release
APP_NAME=$(grep "name:" pubspec.yaml | head -1 | awk '{print $2}')
VERSION=$(git tag --list "*-release*" --sort=-creatordate | head -1)

if [ -z "$VERSION" ]; then
  VERSION="versão não encontrada"
fi

# Arquivo compactado ignorando pastas desnecessárias do Flutter
ZIP_FILE="codigo_fonte.zip"
zip -r "$ZIP_FILE" . \
  -x "build/*" \
  -x ".dart_tool/*" \
  -x ".idea/*" \
  -x ".git/*" \
  -x "*.iml"

# Geração do hash SHA-512 no formato INPI
HASH=$(sha512sum "$ZIP_FILE" | awk '{print toupper($1)}')

# Salvar hash no formato exigido
echo "$HASH" > resumo_hash.txt

# Criar descrição para o formulário e-Software
cat > descricao.txt <<EOL
Título: $APP_NAME
Versão: $VERSION
Algoritmo: SHA-512
Descrição: Hash gerado a partir do código-fonte compactado do aplicativo Flutter, excluindo arquivos de build e cache.
EOL

echo "✅ Hash gerado e salvo em resumo_hash.txt"
echo "📄 Descrição salva em descricao.txt"
