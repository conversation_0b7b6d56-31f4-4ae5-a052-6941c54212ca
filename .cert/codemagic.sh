# Store the document key from command line argument
DOCUMENT_KEY="$1"
echo "🔑 Chave do Documento: $DOCUMENT_KEY"
echo "🏁 Passo 1 concluído."
echo "---"
# --- Passo 2: Buscar Configuração da API ---
echo "🚀 Passo 2: Buscar Configuração da API"
# Make the curl request and store response in a variable
RESPONSE_RAW=$(curl -s --location "https://app-do-aluno-unificado.web.app/clienteApp/consultarAppPersonalizado?documentKey=${DOCUMENT_KEY}" \
--header 'Authorization: functionAlunoAntigo' \
--header 'mc: Mobile Center BuildNumber')

# Check curl exit status
if [ $? -ne 0 ]; then
    echo "❌ Erro: Falha ao buscar configuração da API."
    exit 1
fi
# --- Passo 3: Carregar informações do arquivo JSON ---
echo "🚀 Passo 3: Carregar informações do arquivo JSON"
# Carrega o conteúdo do arquivo JSON
APP_INFO_CONTENT=$(echo "$RESPONSE_RAW" | jq -r '.sucesso'

# --- Passo 4: <PERSON><PERSON> as variáveis de assinatura do app ---
echo "🚀 Passo 4: Exportar variáveis para o Codemagic"

# Variáveis Android
CM_KEYSTORE_PATH="$(pwd)/Treino.key"
CM_KEYSTORE_PASSWORD="310800"
CM_KEY_ALIAS="pactotreinokey"
CM_KEY_PASSWORD="123456"

# Exporta variáveis Android
export CM_KEYSTORE_PATH
export CM_KEYSTORE_PASSWORD
export CM_KEY_ALIAS
export CM_KEY_PASSWORD

# Extrai valores do JSON e exporta variáveis
export CM_GOOGLE_PLAY_PACKAGE_NAME=$(echo "$APP_INFO_CONTENT" | jq -r '.googlePlayPackageName')
export CM_GOOGLE_PLAY_LABEL=$(echo "$APP_INFO_CONTENT" | jq -r '.googlePlayLabel')
export CM_GOOGLE_PLAY_ICON=$(echo "$APP_INFO_CONTENT" | jq -r '.googlePlayIcon')
export CM_FIREBASE_GOOGLE_SERVICES_JSON=$(echo "$APP_INFO_CONTENT" | jq -r '.firebaseGoogleServicesJsonAndroid')

# Variáveis iOS
export CM_IOS_BUNDLE_ID=$(echo "$APP_INFO_CONTENT" | jq -r '.iosBundleId')
export CM_IOS_BUNDLE_ID_WATCH=$(echo "$APP_INFO_CONTENT" | jq -r '.iosBundleIdWatch')
export CM_IOS_LABEL=$(echo "$APP_INFO_CONTENT" | jq -r '.iosLabel')
export CM_IOS_ICON=$(echo "$APP_INFO_CONTENT" | jq -r '.iosIcon')

# Variáveis de assinatura iOS
export CM_ISSUER_ID=$(echo "$APP_INFO_CONTENT" | jq -r '.iosIssuerId')
export APPLE_KEY_ALIAS=$(echo "$APP_INFO_CONTENT" | jq -r '.iosP8Password')
export APPLE_KEYSTORE_PASSWORD=$(echo "$APP_INFO_CONTENT" | jq -r '.iosP8Password')
export APPLE_P8_BASE64=$(echo "$APP_INFO_CONTENT" | jq -r '.iosP8File')

# Salva o arquivo P8 no disco se necessário
P8_FILE_PATH="$(pwd)/ios_distribution.p8"
echo "$APPLE_P8_BASE64" | base64 --decode > "$P8_FILE_PATH"
export CM_PRIVATE_KEY_PATH="$P8_FILE_PATH"

# Exporta o Firebase iOS
export CM_FIREBASE_GOOGLE_SERVICES_PLIST=$(echo "$APP_INFO_CONTENT" | jq -r '.firebaseGoogleServicesJsonIOS')

# --- Passo 5: Exibir variáveis exportadas ---
echo "✅ Variáveis Android exportadas:"
echo "📱 Package Name: $CM_GOOGLE_PLAY_PACKAGE_NAME"
echo "🏷️ App Label: $CM_GOOGLE_PLAY_LABEL"
echo "🔑 Keystore Path: $CM_KEYSTORE_PATH"

echo "✅ Variáveis iOS exportadas:"
echo "📱 Bundle ID: $CM_IOS_BUNDLE_ID"
echo "🏷️ App Label: $CM_IOS_LABEL"
echo "🔑 Issuer ID: $CM_ISSUER_ID"
echo "🔑 Key ID: $APPLE_KEY_ALIAS"
echo "🔑 P8 Path: $CM_PRIVATE_KEY_PATH"

echo "🎉 Exportação de variáveis concluída com sucesso!"