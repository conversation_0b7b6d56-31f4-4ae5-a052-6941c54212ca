#!/bin/bash
# Check if document key is provided
if [ -z "$1" ]; then
    echo "❌ Erro: Chave do documento é obrigatória."
    echo "Uso: $0 <documentKey>"
    exit 1
fi
echo "✅ Chave do documento fornecida."
DOCUMENT_KEY="$1"


# Function to get user selection
select_option() {
    local prompt=$1
    shift
    local options=("$@")
    select opt in "${options[@]}"; do
        if [[ -n $opt ]]; then
            echo "$opt"
            return
        else
            echo "❌ Opção inválida. Tente novamente."
        fi
    done
}

# Ask user if they want to use FVM
echo "🔍 Deseja utilizar FVM para gerar o build?"
use_fvm=$(select_option "Selecione uma opção:" "Sim" "Não")
echo "🔄 Usando FVM: $use_fvm"
# Ask user what type of build they want
echo "📦 Que tipo de build deseja gerar?"
build_type=$(select_option "Selecione uma opção:" "APK" "App Bundle")
# Check if flavor.sh exists and is executable
if [ -f "./.cert/flavor.sh" ]; then
    chmod +x ./.cert/flavor.sh
    ./.cert/flavor.sh "$1"
else
    echo "❌ Erro: flavor.sh não encontrado."
    exit 1
fi
echo "🔑 Chave do Documento: $DOCUMENT_KEY"
echo "🔄 Gerando..."
if [[ "$use_fvm" == "Sim" ]]; then
    if [[ "$build_type" == "APK" ]]; then
        echo "🔍 Gerando APK com FVM..."
        fvm flutter build apk --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
    else
        echo "🔍 Gerando App Bundle com FVM..."
        fvm flutter build appbundle --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
    fi
else
    if [[ "$build_type" == "APK" ]]; then
        echo "🔍 Gerando APK com Flutter diretamente..."
        flutter build apk --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
    else
        echo "🔍 Gerando App Bundle com Flutter diretamente..."
        flutter build appbundle --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
    fi
fi
echo "✅ APK gerado com sucesso!"
echo "🔄 Descartando alterações..."

if [ -f "./.cert/flavordiscart.sh" ]; then
    chmod +x ./.cert/flavordiscart.sh
    ./.cert/flavordiscart.sh "$1"
else
    echo "❌ Erro: flavordiscart.sh não encontrado."
    exit 1
fi
echo "✅ Alterações descartadas com sucesso!"