#!/bin/bash
# exemplo de uso ./flavor.sh AueZMA6GQEXGXhRKGYZ5
echo 'pj1p8WsmmVZrJbDC' | dart pub token add https://dart.cloudsmith.io/n2b-brasil/packages/
echo 'e6WwAWiAl1SzGU6Q' | dart pub token add https://dart.cloudsmith.io/n2b-brasil/patient-app-core/
# --- Passo 1: Verificações Iniciais e Validação de Entrada ---
./.cert/setup_n2b.sh
echo "🚀 Passo 1: Verificações Iniciais e Validação de Entrada"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "❌ Erro: jq é necessário mas não está instalado. Por favor, instale jq."
    # Check the OS and install jq accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "MacOS detected. Attempting to install jq with Homebrew..."
        brew install jq || echo "Please install Homebrew first: https://brew.sh/"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Check for common Linux package managers
        if command -v apt-get &> /dev/null; then
            echo "Debian/Ubuntu detected. Installing jq..."
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v dnf &> /dev/null; then
            echo "Fedora detected. Installing jq..."
            sudo dnf install -y jq
        elif command -v yum &> /dev/null; then
            echo "RHEL/CentOS detected. Installing jq..."
            sudo yum install -y jq
        elif command -v pacman &> /dev/null; then
            echo "Arch Linux detected. Installing jq..."
            sudo pacman -S --noconfirm jq
        elif command -v zypper &> /dev/null; then
            echo "openSUSE detected. Installing jq..."
            sudo zypper install -y jq
        else
            echo "Could not detect package manager. Please install jq manually."
        fi
    else
        echo "Unsupported OS. Please install jq manually: https://stedolan.github.io/jq/download/"
    fi
    exit 1
fi
echo "✅ jq encontrado."

# Check if magick is installed
if ! command -v magick &> /dev/null; then
    echo "❌ Erro: magick (ImageMagick) é necessário mas não está instalado. Por favor, instale ImageMagick."
    # Check the OS and install ImageMagick accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "MacOS detected. Attempting to install ImageMagick with Homebrew..."
        brew install imagemagick || echo "Please install Homebrew first: https://brew.sh/"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Check for common Linux package managers
        if command -v apt-get &> /dev/null; then
            echo "Debian/Ubuntu detected. Installing ImageMagick..."
            sudo apt-get update && sudo apt-get install -y imagemagick
        elif command -v dnf &> /dev/null; then
            echo "Fedora detected. Installing ImageMagick..."
            sudo dnf install -y ImageMagick
        elif command -v yum &> /dev/null; then
            echo "RHEL/CentOS detected. Installing ImageMagick..."
            sudo yum install -y ImageMagick
        elif command -v pacman &> /dev/null; then
            echo "Arch Linux detected. Installing ImageMagick..."
            sudo pacman -S --noconfirm imagemagick
        elif command -v zypper &> /dev/null; then
            echo "openSUSE detected. Installing ImageMagick..."
            sudo zypper install -y ImageMagick
        else
            echo "Could not detect package manager. Please install ImageMagick manually."
        fi
    else
        echo "Unsupported OS. Please install ImageMagick manually: https://imagemagick.org/script/download.php"
    fi
    exit 1
fi
echo "✅ magick encontrado."

# Check if document key is provided
if [ -z "$1" ]; then
    echo "❌ Erro: Chave do documento é obrigatória."
    echo "Uso: $0 <documentKey>"
    exit 1
fi
echo "✅ Chave do documento fornecida."

# Store the document key from command line argument
DOCUMENT_KEY="$1"
echo "🔑 Chave do Documento: $DOCUMENT_KEY"
echo "🏁 Passo 1 concluído."
echo "---"

# --- Passo 2: Buscar Configuração da API ---
echo "🚀 Passo 2: Buscar Configuração da API"
# Make the curl request and store response in a variable
RESPONSE_RAW=$(curl -s --location "https://app-do-aluno-unificado.web.app/clienteApp/consultarAppPersonalizado?documentKey=${DOCUMENT_KEY}" \
--header 'Authorization: functionAlunoAntigo' \
--header 'mc: Mobile Center BuildNumber')

# Check curl exit status
if [ $? -ne 0 ]; then
    echo "❌ Erro: Falha ao buscar configuração da API."
    exit 1
fi

# Check if response is empty or not valid JSON
if [ -z "$RESPONSE_RAW" ] || ! echo "$RESPONSE_RAW" | jq empty; then
    echo "❌ Erro: Resposta da API vazia ou inválida."
    echo "Resposta recebida: $RESPONSE_RAW"
    exit 1
fi

RESPONSE=$(echo "$RESPONSE_RAW" | jq -r '.sucesso')
# Check if .sucesso exists and is not null
if [ -z "$RESPONSE" ] || [ "$RESPONSE" == "null" ]; then
   echo "❌ Erro: Campo '.sucesso' não encontrado ou nulo na resposta da API."
   echo "Resposta completa: $RESPONSE_RAW"
   exit 1
fi

echo "✅ Configuração da API buscada com sucesso."
echo "🏁 Passo 2 concluído."
echo "---"

# --- Passo 3: Parsear Configuração JSON ---
echo "🚀 Passo 3: Parsear Configuração JSON"
# Parse the JSON response
LABEL=$(echo $RESPONSE | jq -r '.googlePlayLabel // "App Personalizado"')
PACKAGE_NAME=$(echo $RESPONSE | jq -r '.googlePlayPackageName // "com.example.app_personalizado"')
BUNDLE_ID_IOS=$(echo $RESPONSE | jq -r '.iosBundleId // "com.example.appPersonalizado"')
BUNDLE_ID_IOS_APPLEWATCH=$(echo $RESPONSE | jq -r '.iosBundleIdWatch // "com.example.appPersonalizado.watch"')
APP_NAME_ANDROID=$(echo $RESPONSE | jq -r '.googlePlayLabel // "App Personalizado"')
APP_NAME_IOS=$(echo $RESPONSE | jq -r '.iosLabel // "App Personalizado iOS"')
ICON_PATH=$(echo $RESPONSE | jq -r '.googlePlayIcon // ""')
ICON_PATH_IOS=$(echo $RESPONSE | jq -r '.iosIcon // ""')
SERVICES_JSON=$(echo $RESPONSE | jq -r '.firebaseGoogleServicesJsonAndroid // ""') # Renomeado para evitar conflito
FIREBASE_ANDROID_B64=$(echo $RESPONSE | jq -r '.firebaseGoogleServicesJsonAndroid // ""') # Usar este para google-services.json
VERSION=$(echo $RESPONSE | jq -r '.googlePlayVersion // "1.0.0"')
# Run vinculafb.sh only if SERVICES_JSON is null or empty
if [ -z "$SERVICES_JSON" ] || [ "$SERVICES_JSON" == "null" ]; then
    echo "⚠️ Aviso: firebaseGoogleServicesJsonAndroid não encontrado ou nulo. Tentando vincular Firebase..."
    ./.cert/vinculafb.sh "$1"
    if [ $? -ne 0 ]; then
        echo "⚠️ Aviso: Falha ao vincular Firebase. Continuando sem este recurso."
    else
        echo "✅ Firebase vinculado com sucesso."
    fi
else
    echo "✅ Configuração Firebase já presente no JSON de resposta. Pulando vinculação."
fi
# Basic validation of parsed values (check if essential ones are not empty/null)
if [ -z "$PACKAGE_NAME" ] || [ "$PACKAGE_NAME" == "null" ] || \
   [ -z "$BUNDLE_ID_IOS" ] || [ "$BUNDLE_ID_IOS" == "null" ] || \
   [ -z "$APP_NAME_ANDROID" ] || [ "$APP_NAME_ANDROID" == "null" ] || \
   [ -z "$APP_NAME_IOS" ] || [ "$APP_NAME_IOS" == "null" ]; then
    echo "❌ Erro: Falha ao parsear valores essenciais da configuração JSON."
    echo "PACKAGE_NAME: $PACKAGE_NAME, BUNDLE_ID_IOS: $BUNDLE_ID_IOS, APP_NAME_ANDROID: $APP_NAME_ANDROID, APP_NAME_IOS: $APP_NAME_IOS"
    exit 1
fi

echo "✅ Configuração JSON parseada com sucesso."
echo "   - Label: $LABEL"
echo "   - Package Name (Android): $PACKAGE_NAME"
echo "   - Bundle ID (iOS): $BUNDLE_ID_IOS"
echo "   - App Name (Android): $APP_NAME_ANDROID"
echo "   - App Name (iOS): $APP_NAME_IOS"
echo "🏁 Passo 3 concluído."
echo "---"

# --- Passo 4: Configurar Estrutura do Flavor Android ---
echo "🚀 Passo 4: Configurar Estrutura do Flavor Android"
# Default flavor editor
DEFAULT_FLAVOR='apppersonalizado';
# Create flavor directory structure
FLAVOR_DIR="android/app/src/${DEFAULT_FLAVOR}"

mkdir -p "$FLAVOR_DIR/res"
if [ $? -ne 0 ]; then echo "❌ Erro ao criar $FLAVOR_DIR/res"; exit 1; fi
echo "✅ Diretório base do flavor criado: $FLAVOR_DIR/res"

# Create the mipmap directories for different resolutions
mkdir -p "$FLAVOR_DIR/res/mipmap-mdpi" "$FLAVOR_DIR/res/mipmap-hdpi" "$FLAVOR_DIR/res/mipmap-xhdpi" "$FLAVOR_DIR/res/mipmap-xxhdpi" "$FLAVOR_DIR/res/mipmap-xxxhdpi"
if [ $? -ne 0 ]; then echo "❌ Erro ao criar diretórios mipmap"; exit 1; fi
echo "✅ Diretórios mipmap criados."

# copie a pasta android/app/src/main/res para a pasta do flavor
# Assuming 'flynow' is the template flavor to copy resources from
TEMPLATE_FLAVOR_RES="android/app/src/flynow/res/values"
if [ -d "$TEMPLATE_FLAVOR_RES" ]; then
    cp -r "$TEMPLATE_FLAVOR_RES" "$FLAVOR_DIR/res/"
    if [ $? -ne 0 ]; then echo "❌ Erro ao copiar $TEMPLATE_FLAVOR_RES para $FLAVOR_DIR/res/"; exit 1; fi
    echo "✅ Recursos de values copiados de $TEMPLATE_FLAVOR_RES."
else
    echo "⚠️ Aviso: Diretório de template $TEMPLATE_FLAVOR_RES não encontrado. Pulando cópia de resources/values."
    mkdir -p "$FLAVOR_DIR/res/values" # Ensure values directory exists
    # Create a basic strings.xml if needed
    echo '<?xml version="1.0" encoding="utf-8"?><resources></resources>' > "$FLAVOR_DIR/res/values/strings.xml"
fi
echo "🏁 Passo 4 concluído."
echo "---"

# --- Passo 5: Processar Ícone Android ---
echo "🚀 Passo 5: Processar Ícone Android"
TEMP_ICON="icon_android_${DOCUMENT_KEY}.png"
ICON_DATA=$(echo $RESPONSE | jq -r '.googlePlayIcon // ""') # Use ICON_DATA defined earlier

if [ -z "$ICON_DATA" ] || [ "$ICON_DATA" == "null" ]; then
    echo "❌ Erro: 'googlePlayIcon' não encontrado ou nulo na resposta."
    exit 1
fi

# Verificar se o valor é uma URL
if [[ $ICON_DATA == http* ]]; then
    echo "   - Ícone é uma URL. Baixando..."
    curl -sL "$ICON_DATA" -o "$TEMP_ICON" # Use -L to follow redirects
    if [ $? -ne 0 ]; then echo "❌ Erro ao baixar o ícone da URL: $ICON_DATA"; exit 1; fi
# Verificar se é um caminho local (considerando caminhos relativos ou absolutos)
elif [[ -f "$ICON_DATA" ]]; then
    echo "   - Ícone é um arquivo local. Copiando..."
    cp "$ICON_DATA" "$TEMP_ICON"
    if [ $? -ne 0 ]; then echo "❌ Erro ao copiar o ícone local: $ICON_DATA"; exit 1; fi
# Tentar como conteúdo Base64
else
    echo "   - Tentando decodificar ícone como base64..."
    echo "$ICON_DATA" | base64 -d > "$TEMP_ICON" 2>/dev/null # Redirect stderr for cleaner output
    # Check if base64 decode created a non-empty file
    if [ $? -ne 0 ] || [ ! -s "$TEMP_ICON" ]; then
        echo "❌ Erro: Falha ao decodificar o ícone base64 ou formato inválido."
        rm -f "$TEMP_ICON" # Clean up potentially corrupted file
        exit 1
    fi
fi

# Verificar se o arquivo temporário foi criado
if [ ! -f "$TEMP_ICON" ]; then
    echo "❌ Erro: Falha ao processar o ícone. O arquivo temporário não foi criado."
    exit 1
fi

# Validar se o arquivo é uma imagem
if ! file "$TEMP_ICON" | grep -q -E 'image|bitmap'; then
    echo "❌ Erro: O arquivo processado não é uma imagem válida."
    file "$TEMP_ICON" # Show file type for debugging
    rm -f "$TEMP_ICON"
    exit 1
fi

echo "✅ Ícone Android processado com sucesso e salvo temporariamente em $TEMP_ICON."
echo "🏁 Passo 5 concluído."
echo "---"

# --- Passo 6: Gerar Ícones Android em Diferentes Resoluções ---
echo "🚀 Passo 6: Gerar Ícones Android em Diferentes Resoluções"
echo "   - Gerando ícones quadrados..."
magick "$TEMP_ICON" -resize 48x48 "$FLAVOR_DIR/res/mipmap-mdpi/ic_launcher.png" && \
magick "$TEMP_ICON" -resize 72x72 "$FLAVOR_DIR/res/mipmap-hdpi/ic_launcher.png" && \
magick "$TEMP_ICON" -resize 96x96 "$FLAVOR_DIR/res/mipmap-xhdpi/ic_launcher.png" && \
magick "$TEMP_ICON" -resize 144x144 "$FLAVOR_DIR/res/mipmap-xxhdpi/ic_launcher.png" && \
magick "$TEMP_ICON" -resize 192x192 "$FLAVOR_DIR/res/mipmap-xxxhdpi/ic_launcher.png"

if [ $? -ne 0 ]; then echo "❌ Erro ao gerar ícones quadrados."; rm -f "$TEMP_ICON"; exit 1; fi
echo "   ✅ Ícones quadrados gerados."

echo "   - Gerando ícones redondos..."
magick "$TEMP_ICON" -resize 48x48 \( +clone -alpha extract -draw 'fill black circle 24,24 24,0' -alpha shape \) -composite "$FLAVOR_DIR/res/mipmap-mdpi/ic_launcher_round.png" && \
magick "$TEMP_ICON" -resize 72x72 \( +clone -alpha extract -draw 'fill black circle 36,36 36,0' -alpha shape \) -composite "$FLAVOR_DIR/res/mipmap-hdpi/ic_launcher_round.png" && \
magick "$TEMP_ICON" -resize 96x96 \( +clone -alpha extract -draw 'fill black circle 48,48 48,0' -alpha shape \) -composite "$FLAVOR_DIR/res/mipmap-xhdpi/ic_launcher_round.png" && \
magick "$TEMP_ICON" -resize 144x144 \( +clone -alpha extract -draw 'fill black circle 72,72 72,0' -alpha shape \) -composite "$FLAVOR_DIR/res/mipmap-xxhdpi/ic_launcher_round.png" && \
magick "$TEMP_ICON" -resize 192x192 \( +clone -alpha extract -draw 'fill black circle 96,96 96,0' -alpha shape \) -composite "$FLAVOR_DIR/res/mipmap-xxxhdpi/ic_launcher_round.png"

if [ $? -ne 0 ]; then echo "❌ Erro ao gerar ícones redondos."; rm -f "$TEMP_ICON"; exit 1; fi
echo "   ✅ Ícones redondos gerados."

echo "✅ Ícones Android gerados com sucesso."
# Android icon processed, temp file can be removed later or reused for iOS if needed
# rm -f "$TEMP_ICON" # Keep it for now, might be needed for iOS fallback
echo "🏁 Passo 6 concluído."
echo "---"

# --- Passo 7: Processar Configuração Firebase Android ---
echo "🚀 Passo 7: Processar Configuração Firebase Android"
FIREBASE_CONFIG_FILE="$FLAVOR_DIR/google-services.json"
# Use FIREBASE_ANDROID_B64 parsed earlier
if [[ -n $FIREBASE_ANDROID_B64 && $FIREBASE_ANDROID_B64 != "null" ]]; then
    echo "   - Decodificando e salvando google-services.json..."
    echo "$FIREBASE_ANDROID_B64" | base64 --decode > "$FIREBASE_CONFIG_FILE"

    # Check if file was properly decoded and is valid JSON
    if [ ! -s "$FIREBASE_CONFIG_FILE" ] || ! jq empty "$FIREBASE_CONFIG_FILE"; then
        echo "❌ Erro: Falha ao decodificar ou validar o google-services.json a partir do base64."
        rm -f "$FIREBASE_CONFIG_FILE" # Remove invalid file
        # Decide if this is a fatal error or just a warning
        # exit 1 # Uncomment if google-services.json is mandatory
        echo "⚠️ Aviso: google-services.json não pôde ser processado."
    else
        echo "✅ google-services.json salvo e validado com sucesso em $FIREBASE_CONFIG_FILE."
    fi
else
    echo "⚠️ Aviso: 'firebaseGoogleServicesJsonAndroid' (base64) não fornecido na resposta da API. Pulando criação do google-services.json."
fi
echo "🏁 Passo 7 concluído."
echo "---"

# --- Passo 8: Salvar Configuração do Flavor Android ---
echo "🚀 Passo 8: Salvar Configuração do Flavor Android"
cat > "$FLAVOR_DIR/config.json" << EOF
{
  "label": "$LABEL",
  "packageName": "$PACKAGE_NAME",
  "version": "$VERSION",
  "documentKey": "$DOCUMENT_KEY"
}
EOF
if [ $? -ne 0 ]; then echo "❌ Erro ao salvar $FLAVOR_DIR/config.json"; exit 1; fi
echo "✅ Informações do flavor salvas em $FLAVOR_DIR/config.json."
echo "🏁 Passo 8 concluído."
echo "---"

# --- Passo 9: Atualizar Arquivo de Build Android (build.gradle.kts) ---
echo "🚀 Passo 9: Atualizar Arquivo de Build Android (build.gradle.kts)"
GRADLE_FILE="android/app/build.gradle.kts"

if [ ! -f "$GRADLE_FILE" ]; then
    echo "❌ Erro: Arquivo $GRADLE_FILE não encontrado."
    exit 1
fi

# Fazer backup do arquivo original
cp "$GRADLE_FILE" "${GRADLE_FILE}.bak"
if [ $? -ne 0 ]; then echo "❌ Erro ao criar backup de $GRADLE_FILE"; exit 1; fi
echo "   - Backup criado: ${GRADLE_FILE}.bak"

# Modifica o app_name e applicationId para o flavor apppersonalizado usando sed
# Use uma abordagem mais robusta se a estrutura do arquivo for complexa
# Esta abordagem assume que a definição do flavor 'apppersonalizado' já existe
sed -i.sedbak -E "/create\(\"apppersonalizado\"\)/,/\}/s/resValue\(\"string\", \"app_name\", \".*\"\)/resValue(\"string\", \"app_name\", \"$APP_NAME_ANDROID\")/" "$GRADLE_FILE"
if [ $? -ne 0 ]; then echo "❌ Erro ao atualizar app_name em $GRADLE_FILE"; mv "${GRADLE_FILE}.bak" "$GRADLE_FILE"; exit 1; fi
sed -i.sedbak -E "/create\(\"apppersonalizado\"\)/,/\}/s/applicationId = \".*\"/applicationId = \"$PACKAGE_NAME\"/" "$GRADLE_FILE"
if [ $? -ne 0 ]; then echo "❌ Erro ao atualizar applicationId em $GRADLE_FILE"; mv "${GRADLE_FILE}.bak" "$GRADLE_FILE"; exit 1; fi

# Limpar backup do sed se tudo correu bem
rm -f "${GRADLE_FILE}.sedbak"

# Verifica se as alterações foram feitas
if grep -A6 'create("apppersonalizado")' "$GRADLE_FILE" | grep -q "applicationId = \"$PACKAGE_NAME\"" && \
   grep -A6 'create("apppersonalizado")' "$GRADLE_FILE" | grep -q "resValue(\"string\", \"app_name\", \"$APP_NAME_ANDROID\")"; then
    echo "✅ Configuração do flavor 'apppersonalizado' atualizada com sucesso em $GRADLE_FILE!"
    echo "   - Novo applicationId: $PACKAGE_NAME"
    echo "   - Novo app_name: $APP_NAME_ANDROID"
else
    echo "❌ Erro: Falha ao verificar as atualizações no $GRADLE_FILE. Restaurando backup..."
    mv "${GRADLE_FILE}.bak" "$GRADLE_FILE"
    exit 1
fi
echo "🏁 Passo 9 concluído."
echo "---"

# --- Passo 10: Preparar Arquivo Dart (flavors.dart) ---
echo "🚀 Passo 10: Preparar Arquivo Dart (flavors.dart)"
BASE64FILE="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"
FLAVORS_FILE="lib/flavors.dart"
echo "$BASE64FILE" | base64 --decode > "$FLAVORS_FILE"
if [ $? -ne 0 ]; then echo "❌ Erro ao decodificar e salvar $FLAVORS_FILE"; exit 1; fi
echo "✅ Arquivo $FLAVORS_FILE decodificado e salvo com sucesso."
echo "🏁 Passo 10 concluído."
echo "---"

# --- Passo 11: Atualizar Arquivo Dart (flavors.dart) com Configs Firebase ---
echo "🚀 Passo 11: Atualizar Arquivo Dart (flavors.dart) com Configs Firebase"
# Check if FLAVORS_FILE exists
if [ ! -f "$FLAVORS_FILE" ]; then
    echo "❌ Erro: Arquivo $FLAVORS_FILE não encontrado após a decodificação."
    exit 1
fi

# Check if google-services.json exists and is valid before extracting
if [ -f "$FIREBASE_CONFIG_FILE" ] && jq empty "$FIREBASE_CONFIG_FILE" > /dev/null 2>&1; then
    echo "   - Extraindo configurações do $FIREBASE_CONFIG_FILE..."
    # Extract values from the google-services.json file
    # Add error handling for jq extractions
    API_KEY=$(jq -r '.client[0].api_key[0].current_key // ""' "$FIREBASE_CONFIG_FILE")
    APP_ID_ANDROID=$(jq -r '.client[0].client_info.mobilesdk_app_id // ""' "$FIREBASE_CONFIG_FILE")
    # Assuming iOS uses the same App ID for this structure, adjust if needed
    APP_ID_IOS=$(jq -r '.client[0].client_info.mobilesdk_app_id // ""' "$FIREBASE_CONFIG_FILE") # Or parse specific iOS client if available
    MESSAGING_SENDER_ID=$(jq -r '.project_info.project_number // ""' "$FIREBASE_CONFIG_FILE")
    PROJECT_ID=$(jq -r '.project_info.project_id // ""' "$FIREBASE_CONFIG_FILE")
    DATABASE_URL=$(jq -r '.project_info.firebase_url // "https://'$PROJECT_ID'.firebaseio.com"' "$FIREBASE_CONFIG_FILE")
    STORAGE_BUCKET=$(jq -r '.project_info.storage_bucket // ""' "$FIREBASE_CONFIG_FILE")
    # Extract iOS specific client ID if present (often used for Google Sign-In)
    IOS_CLIENT_ID=$(jq -r '(.client[0].oauth_client[]? | select(.client_type == 3) | .client_id) // ""' "$FIREBASE_CONFIG_FILE")
    # Measurement ID is usually not in google-services.json, use a placeholder or fetch elsewhere
    MEASUREMENT_ID="G-PLACEHOLDER" # Placeholder

    # Validate essential Firebase config values
     if [ -z "$API_KEY" ] || [ "$API_KEY" == "null" ] || \
        [ -z "$APP_ID_ANDROID" ] || [ "$APP_ID_ANDROID" == "null" ] || \
        [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" == "null" ]; then
         echo "❌ Erro: Falha ao extrair configurações essenciais do Firebase do $FIREBASE_CONFIG_FILE."
         # exit 1 # Decide if this is fatal
         echo "⚠️ Aviso: Não foi possível extrair todas as configurações do Firebase. O arquivo Dart pode não ser atualizado corretamente."
         # Set flags or variables to skip sed replacements if needed
     else
        echo "   ✅ Configurações Firebase extraídas com sucesso."

        # Create a backup of the original file
        cp "$FLAVORS_FILE" "${FLAVORS_FILE}.bak"
        if [ $? -ne 0 ]; then echo "❌ Erro ao criar backup de $FLAVORS_FILE"; exit 1; fi
        echo "   - Backup criado: ${FLAVORS_FILE}.bak"

        # Update the Firebase options in the Dart file using sed
        # Escape special characters in variables for sed
        API_KEY_SED=$(printf '%s\n' "$API_KEY" | sed 's:[][\\/.^$*]:\\&:g')
        APP_ID_ANDROID_SED=$(printf '%s\n' "$APP_ID_ANDROID" | sed 's:[][\\/.^$*]:\\&:g')
        APP_ID_IOS_SED=$(printf '%s\n' "$APP_ID_IOS" | sed 's:[][\\/.^$*]:\\&:g') # Use the same or specific iOS ID
        MESSAGING_SENDER_ID_SED=$(printf '%s\n' "$MESSAGING_SENDER_ID" | sed 's:[][\\/.^$*]:\\&:g')
        PROJECT_ID_SED=$(printf '%s\n' "$PROJECT_ID" | sed 's:[][\\/.^$*]:\\&:g')
        DATABASE_URL_SED=$(printf '%s\n' "$DATABASE_URL" | sed 's:[][\\/.^$*]:\\&:g')
        STORAGE_BUCKET_SED=$(printf '%s\n' "$STORAGE_BUCKET" | sed 's:[][\\/.^$*]:\\&:g')
        MEASUREMENT_ID_SED=$(printf '%s\n' "$MEASUREMENT_ID" | sed 's:[][\\/.^$*]:\\&:g')
        BUNDLE_ID_IOS_SED=$(printf '%s\n' "$BUNDLE_ID_IOS" | sed 's:[][\\/.^$*]:\\&:g')
        DOCUMENT_KEY_SED=$(printf '%s\n' "$DOCUMENT_KEY" | sed 's:[][\\/.^$*]:\\&:g')
        IOS_CLIENT_ID_SED=$(printf '%s\n' "$IOS_CLIENT_ID" | sed 's:[][\\/.^$*]:\\&:g')

        echo "   - Atualizando placeholders no $FLAVORS_FILE..."
        # Use different delimiters for sed if URLs contain slashes
        sed -i.sedbak \
            -e "s/{firebase_api_key}/$API_KEY_SED/g" \
            -e "s/{firebase_app_id_android}/$APP_ID_ANDROID_SED/g" \
            -e "s/{firebase_app_id_ios}/$APP_ID_IOS_SED/g" \
            -e "s/{firebase_messaging_sender_id}/$MESSAGING_SENDER_ID_SED/g" \
            -e "s/{firebase_project_id}/$PROJECT_ID_SED/g" \
            -e "s|{firebase_database_url}|$DATABASE_URL_SED|g" \
            -e "s/{firebase_storage_bucket}/$STORAGE_BUCKET_SED/g" \
            -e "s/{firebase_measurement_id}/$MEASUREMENT_ID_SED/g" \
            -e "s/{firebase_app_bundle_id}/$BUNDLE_ID_IOS_SED/g" \
            -e "s/{documentKey}/$DOCUMENT_KEY_SED/g" \
            -e "s/{firebase_ios_client_id}/$IOS_CLIENT_ID_SED/g" \
            "$FLAVORS_FILE"

        if [ $? -ne 0 ]; then
            echo "❌ Erro durante a substituição com sed em $FLAVORS_FILE. Restaurando backup..."
            mv "${FLAVORS_FILE}.bak" "$FLAVORS_FILE"
            exit 1
        fi
        rm -f "${FLAVORS_FILE}.sedbak" # Clean up sed backup
        echo "✅ Placeholders atualizados em $FLAVORS_FILE."
     fi
else
    echo "⚠️ Aviso: $FIREBASE_CONFIG_FILE não encontrado ou inválido. Pulando atualização das configurações Firebase em $FLAVORS_FILE."
fi
echo "🏁 Passo 11 concluído."
echo "---"

# --- Passo 12: Atualizar Arquivo de Projeto iOS (project.pbxproj) ---
echo "🚀 Passo 12: Atualizar Arquivo de Projeto iOS (project.pbxproj)"
# Assuming the template/backup is at .cert/pbxproj.bak
PBXPROJ_TEMPLATE=".cert/pbxproj.bak"
PBXPROJ_TARGET="ios/Runner.xcodeproj/project.pbxproj"

if [ ! -f "$PBXPROJ_TEMPLATE" ]; then
    echo "❌ Erro: Arquivo de template $PBXPROJ_TEMPLATE não encontrado."
    exit 1
fi
if [ ! -f "$PBXPROJ_TARGET" ]; then
    echo "❌ Erro: Arquivo de destino $PBXPROJ_TARGET não encontrado."
    exit 1
fi

# Backup do arquivo de projeto atual
cp "$PBXPROJ_TARGET" "${PBXPROJ_TARGET}.bak"
if [ $? -ne 0 ]; then echo "❌ Erro ao criar backup de $PBXPROJ_TARGET"; exit 1; fi
echo "   - Backup criado: ${PBXPROJ_TARGET}.bak"

# Copia o template para o destino antes de modificar
cp "$PBXPROJ_TEMPLATE" "$PBXPROJ_TARGET"
if [ $? -ne 0 ]; then echo "❌ Erro ao copiar template $PBXPROJ_TEMPLATE para $PBXPROJ_TARGET"; mv "${PBXPROJ_TARGET}.bak" "$PBXPROJ_TARGET"; exit 1; fi

# Escape special characters for sed
APP_NAME_IOS_SED=$(printf '%s\n' "$APP_NAME_IOS" | sed 's:[][\\/.^$*]:\\&:g')
BUNDLE_ID_IOS_SED=$(printf '%s\n' "$BUNDLE_ID_IOS" | sed 's:[][\\/.^$*]:\\&:g')
BUNDLE_ID_IOS_APPLEWATCH_SED=$(printf '%s\n' "$BUNDLE_ID_IOS_APPLEWATCH" | sed 's:[][\\/.^$*]:\\&:g')

echo "   - Atualizando placeholders no $PBXPROJ_TARGET..."
sed -i.sedbak \
    -e "s/{appDisplayName}/$APP_NAME_IOS_SED/g" \
    -e "s/br\.com\.pactosolucoes\.apppersonalizado\.watchkitapp/$BUNDLE_ID_IOS_APPLEWATCH_SED/g" \
    -e "s/br\.com\.pactosolucoes\.apppersonalizado/$BUNDLE_ID_IOS_SED/g" \
    "$PBXPROJ_TARGET"

if [ $? -ne 0 ]; then
    echo "❌ Erro durante a substituição com sed em $PBXPROJ_TARGET. Restaurando backup..."
    mv "${PBXPROJ_TARGET}.bak" "$PBXPROJ_TARGET"
    exit 1
fi
rm -f "${PBXPROJ_TARGET}.sedbak" # Clean up sed backup
echo "✅ Arquivo $PBXPROJ_TARGET atualizado com sucesso."
echo "🏁 Passo 12 concluído."
echo "---"

# --- Passo 13: Processar Ícone iOS ---
echo "🚀 Passo 13: Processar Ícone iOS"
TEMP_ICON_IOS="icon_ios_${DOCUMENT_KEY}.png"
ICON_DATA_IOS=$(echo $RESPONSE | jq -r '.iosIcon // ""') # Use ICON_PATH_IOS defined earlier

FALLBACK_TO_ANDROID_ICON=false
if [ -z "$ICON_DATA_IOS" ] || [ "$ICON_DATA_IOS" == "null" ]; then
    echo "⚠️ Aviso: 'iosIcon' não encontrado ou nulo. Tentando usar o ícone Android como fallback."
    FALLBACK_TO_ANDROID_ICON=true
else
    # Verificar se o valor é uma URL
    if [[ $ICON_DATA_IOS == http* ]]; then
        echo "   - Ícone iOS é uma URL. Baixando..."
        curl -sL "$ICON_DATA_IOS" -o "$TEMP_ICON_IOS"
        if [ $? -ne 0 ]; then
             echo "⚠️ Aviso: Falha ao baixar o ícone iOS da URL: $ICON_DATA_IOS. Tentando usar o ícone Android."
             FALLBACK_TO_ANDROID_ICON=true
        fi
    # Verificar se é um caminho local
    elif [[ -f "$ICON_DATA_IOS" ]]; then
        echo "   - Ícone iOS é um arquivo local. Copiando..."
        cp "$ICON_DATA_IOS" "$TEMP_ICON_IOS"
         if [ $? -ne 0 ]; then
             echo "⚠️ Aviso: Falha ao copiar o ícone iOS local: $ICON_DATA_IOS. Tentando usar o ícone Android."
             FALLBACK_TO_ANDROID_ICON=true
        fi
    # Tentar como conteúdo Base64
    else
        echo "   - Tentando decodificar ícone iOS como base64..."
        echo "$ICON_DATA_IOS" | base64 -d > "$TEMP_ICON_IOS" 2>/dev/null
        if [ $? -ne 0 ] || [ ! -s "$TEMP_ICON_IOS" ]; then
            echo "⚠️ Aviso: Falha ao decodificar o ícone iOS base64. Tentando usar o ícone Android."
            rm -f "$TEMP_ICON_IOS" # Clean up potentially corrupted file
            FALLBACK_TO_ANDROID_ICON=true
        fi
    fi
fi

# Fallback para ícone Android se necessário e se o ícone Android existe
if $FALLBACK_TO_ANDROID_ICON; then
    if [ -f "$TEMP_ICON" ]; then
        echo "   - Usando ícone Android ($TEMP_ICON) como fallback para iOS."
        cp "$TEMP_ICON" "$TEMP_ICON_IOS"
        if [ $? -ne 0 ]; then echo "❌ Erro ao copiar ícone Android para fallback iOS."; exit 1; fi
    else
        echo "❌ Erro: Falha ao processar ícone iOS e nenhum ícone Android de fallback ($TEMP_ICON) disponível."
        exit 1
    fi
fi

# Verificar se o arquivo temporário iOS existe
if [ ! -f "$TEMP_ICON_IOS" ]; then
    echo "❌ Erro: Falha ao obter o arquivo de ícone temporário iOS ($TEMP_ICON_IOS)."
    exit 1
fi

# Validar se o arquivo é uma imagem
if ! file "$TEMP_ICON_IOS" | grep -q -E 'image|bitmap'; then
    echo "❌ Erro: O arquivo de ícone iOS processado ($TEMP_ICON_IOS) não é uma imagem válida."
    file "$TEMP_ICON_IOS"
    rm -f "$TEMP_ICON_IOS"
    exit 1
fi

echo "✅ Ícone iOS processado com sucesso e salvo temporariamente em $TEMP_ICON_IOS."
echo "🏁 Passo 13 concluído."
echo "---"

# --- Passo 14: Gerar Ícones iOS ---
echo "🚀 Passo 14: Gerar Ícones iOS"
IOS_ICON_TARGET_DIR="ios/Runner/Assets.xcassets/AppIcon.apppersonalizado.appiconset"
WATCHOS_ICON_TARGET_DIR="ios/testing Watch App/Assets.xcassets/AppIcon.apppersonalizado.appiconset" # Ajuste o path se necessário

# Criar diretórios se não existirem
mkdir -p "$IOS_ICON_TARGET_DIR"
mkdir -p "$WATCHOS_ICON_TARGET_DIR"

echo "   - Gerando ícone para App iOS (1024x1024)..."
magick "$TEMP_ICON_IOS" -resize 1024x1024 -background white -alpha remove -flatten "$IOS_ICON_TARGET_DIR/logo.png"
if [ $? -ne 0 ]; then echo "❌ Erro ao gerar ícone para App iOS."; rm -f "$TEMP_ICON_IOS"; exit 1; fi
echo "   ✅ Ícone App iOS gerado."

echo "   - Gerando ícone para WatchOS App (1024x1024)..."
magick "$TEMP_ICON_IOS" -resize 1024x1024 -background white -alpha remove -flatten "$WATCHOS_ICON_TARGET_DIR/logo.png"
if [ $? -ne 0 ]; then echo "❌ Erro ao gerar ícone para WatchOS App."; rm -f "$TEMP_ICON_IOS"; exit 1; fi
echo "   ✅ Ícone WatchOS App gerado."

echo "✅ Ícones iOS e WatchOS gerados com sucesso."
echo "🏁 Passo 14 concluído."
echo "---"

# --- Passo 15: Limpeza ---
echo "🚀 Passo 15: Limpeza"
rm -f "$TEMP_ICON"
rm -f "$TEMP_ICON_IOS"
# rm -f "${GRADLE_FILE}.bak" # Manter backups por segurança?
# rm -f "${FLAVORS_FILE}.bak"
# rm -f "${PBXPROJ_TARGET}.bak"
echo "✅ Arquivos temporários de ícone removidos."
echo "🏁 Passo 15 concluído."
echo "---"

echo "🎉 Processo de configuração do flavor concluído com sucesso para $DOCUMENT_KEY! 🎉"
echo "   Android flavor criado em: $FLAVOR_DIR"
echo "   Configurações atualizadas em:"
echo "     - $GRADLE_FILE"
echo "     - $FLAVORS_FILE"
echo "     - $PBXPROJ_TARGET"
echo "   Ícones atualizados para Android e iOS."