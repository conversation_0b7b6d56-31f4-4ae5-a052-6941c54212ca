// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		77CB91502751D953E440508A /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3307BAF434C532583E2C6ABC /* Pods_Runner.framework */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		B4122CE72A09458C00997839 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B4122CE62A09458C00997839 /* StoreKit.framework */; };
		B4122CE82A09475000997839 /* Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = C9D291B529176AA000727501 /* Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		B429F6B92B6998D80046F3ED /* DetalhesSerieComponent.swift in Sources */ = {isa = PBXBuildFile; fileRef = B429F6B82B6998D80046F3ED /* DetalhesSerieComponent.swift */; };
		B43270182B17FBB60048E790 /* flame.json in Resources */ = {isa = PBXBuildFile; fileRef = B43270172B17FBB60048E790 /* flame.json */; };
		B476E4E92A0AC0D400756908 /* TelaPrincipal.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4E82A0AC0D400756908 /* TelaPrincipal.swift */; };
		B476E4EB2A0AC0FF00756908 /* Aulas.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4EA2A0AC0FF00756908 /* Aulas.swift */; };
		B476E4ED2A0AC10A00756908 /* Wod.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4EC2A0AC10A00756908 /* Wod.swift */; };
		B476E4EF2A0AC12D00756908 /* Treino.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4EE2A0AC12D00756908 /* Treino.swift */; };
		B476E4F22A0AC1AD00756908 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4F12A0AC1AD00756908 /* Extensions.swift */; };
		B476E4F42A0AC33500756908 /* FichaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4F32A0AC33500756908 /* FichaModel.swift */; };
		B476E4F82A0AC54000756908 /* WorkoutManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E4F72A0AC54000756908 /* WorkoutManager.swift */; };
		B476E52E2A0AC7AD00756908 /* ElapsedTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E51B2A0AC7AB00756908 /* ElapsedTimeView.swift */; };
		B476E52F2A0AC7AD00756908 /* ControlesPlayPauseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E51C2A0AC7AB00756908 /* ControlesPlayPauseView.swift */; };
		B476E5312A0AC7AD00756908 /* TabBarExecucao.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E51F2A0AC7AC00756908 /* TabBarExecucao.swift */; };
		B476E5322A0AC7AD00756908 /* SummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E5202A0AC7AC00756908 /* SummaryView.swift */; };
		B476E5342A0AC7AD00756908 /* MetricasExecucaoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E5232A0AC7AC00756908 /* MetricasExecucaoView.swift */; };
		B476E5372A0AC7AD00756908 /* ActivityRingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B476E5262A0AC7AC00756908 /* ActivityRingsView.swift */; };
		B47BA4342A0BC8A600436BB7 /* WodModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B47BA4332A0BC8A600436BB7 /* WodModel.swift */; };
		B4A9CCCC2B61493200D8F8EC /* Playground.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4A9CCCB2B61493200D8F8EC /* Playground.swift */; };
		B4B07BA32A94FA410079EB5A /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = B4B07BA52A94FA410079EB5A /* InfoPlist.strings */; };
		B4B7FC0A2C220D3B00101BE0 /* MeuPerfil.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4B7FC092C220D3B00101BE0 /* MeuPerfil.swift */; };
		B4BDD6D92A165BA9001DCF7F /* DadosUsuarioModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4BDD6D82A165BA9001DCF7F /* DadosUsuarioModel.swift */; };
		B4E03A742A1544DA00AD0B26 /* TelaBemVindo.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4E03A732A1544DA00AD0B26 /* TelaBemVindo.swift */; };
		B4E03A892A1548F300AD0B26 /* Poppins-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A762A1548F200AD0B26 /* Poppins-ExtraLight.ttf */; };
		B4E03A8A2A1548F300AD0B26 /* Poppins-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A772A1548F200AD0B26 /* Poppins-ThinItalic.ttf */; };
		B4E03A8B2A1548F300AD0B26 /* Poppins-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A782A1548F200AD0B26 /* Poppins-ExtraLightItalic.ttf */; };
		B4E03A8C2A1548F300AD0B26 /* Poppins-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A792A1548F200AD0B26 /* Poppins-BoldItalic.ttf */; };
		B4E03A8D2A1548F300AD0B26 /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7A2A1548F200AD0B26 /* Poppins-Light.ttf */; };
		B4E03A8E2A1548F300AD0B26 /* Poppins-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7B2A1548F200AD0B26 /* Poppins-Medium.ttf */; };
		B4E03A8F2A1548F300AD0B26 /* Poppins-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7C2A1548F200AD0B26 /* Poppins-SemiBoldItalic.ttf */; };
		B4E03A902A1548F300AD0B26 /* Poppins-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7D2A1548F200AD0B26 /* Poppins-ExtraBoldItalic.ttf */; };
		B4E03A912A1548F300AD0B26 /* Poppins-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7E2A1548F200AD0B26 /* Poppins-ExtraBold.ttf */; };
		B4E03A922A1548F300AD0B26 /* Poppins-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A7F2A1548F200AD0B26 /* Poppins-BlackItalic.ttf */; };
		B4E03A942A1548F300AD0B26 /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A812A1548F200AD0B26 /* Poppins-Regular.ttf */; };
		B4E03A952A1548F300AD0B26 /* Poppins-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A822A1548F200AD0B26 /* Poppins-LightItalic.ttf */; };
		B4E03A962A1548F300AD0B26 /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A832A1548F200AD0B26 /* Poppins-Bold.ttf */; };
		B4E03A972A1548F300AD0B26 /* Poppins-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A842A1548F200AD0B26 /* Poppins-Black.ttf */; };
		B4E03A982A1548F300AD0B26 /* Poppins-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A852A1548F200AD0B26 /* Poppins-Thin.ttf */; };
		B4E03A992A1548F300AD0B26 /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A862A1548F200AD0B26 /* Poppins-SemiBold.ttf */; };
		B4E03A9A2A1548F300AD0B26 /* Poppins-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A872A1548F200AD0B26 /* Poppins-Italic.ttf */; };
		B4E03A9B2A1548F300AD0B26 /* Poppins-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B4E03A882A1548F200AD0B26 /* Poppins-MediumItalic.ttf */; };
		B4E03AA82A15604200AD0B26 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = B4E03AA72A15604200AD0B26 /* SDWebImageSwiftUI */; };
		B4E03AAA2A15605500AD0B26 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = B4E03AA92A15605500AD0B26 /* SDWebImageSwiftUI */; };
		B4E03AB12A15694800AD0B26 /* SDWebImageLottieCoder in Frameworks */ = {isa = PBXBuildFile; productRef = B4E03AB02A15694800AD0B26 /* SDWebImageLottieCoder */; };
		B4E03AB32A15696B00AD0B26 /* LottieViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4E03AB22A15696B00AD0B26 /* LottieViewModel.swift */; };
		B4E03AB92A15791E00AD0B26 /* run_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = B4E03AB82A15791E00AD0B26 /* run_animation.json */; };
		B4F14B262A0C3901000FBEFF /* AulasModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4F14B252A0C3901000FBEFF /* AulasModel.swift */; };
		C9D291B829176AA000727501 /* testingApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9D291B729176AA000727501 /* testingApp.swift */; };
		C9D291BA29176AA000727501 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9D291B929176AA000727501 /* ContentView.swift */; };
		C9D291BC29176AA100727501 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C9D291BB29176AA100727501 /* Assets.xcassets */; };
		C9D291BF29176AA100727501 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C9D291BE29176AA100727501 /* Preview Assets.xcassets */; };
		C9D291C629176AB600727501 /* WatchViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9D291C529176AB600727501 /* WatchViewModel.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C9D291C929176AE000727501 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C9D291B429176AA000727501;
			remoteInfo = "testing Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9D291CB29176AE000727501 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				B4122CE82A09475000997839 /* Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		248EBCE3B06F2E83E82A5D44 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		3307BAF434C532583E2C6ABC /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3C90381C01AB2C2EA8A30060 /* Pods-Runner.liveacademia-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.liveacademia-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.liveacademia-debug.xcconfig"; sourceTree = "<group>"; };
		55972A456648780E06EB9ED4 /* Pods-Runner.intensefit-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.intensefit-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.intensefit-release.xcconfig"; sourceTree = "<group>"; };
		559CB1917F2EC6B6FEF523CD /* Pods-Runner.fitstream-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.fitstream-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.fitstream-release.xcconfig"; sourceTree = "<group>"; };
		5C84920D527FCD9395506912 /* Pods-Runner.engenhariadocorpo-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.engenhariadocorpo-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.engenhariadocorpo-debug.xcconfig"; sourceTree = "<group>"; };
		69265ABEC28EA6734CD637D4 /* Pods-Runner.intensefit-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.intensefit-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.intensefit-debug.xcconfig"; sourceTree = "<group>"; };
		699CA96F3BC20ECDF03DEE20 /* Pods-Runner.maybootcamp-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.maybootcamp-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.maybootcamp-release.xcconfig"; sourceTree = "<group>"; };
		6D9F4B982D42D8CC1C6EF44D /* Pods-Runner.treino-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.treino-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.treino-release.xcconfig"; sourceTree = "<group>"; };
		6DBC0DBB318D83D3493EEF65 /* Pods-Runner.flynow-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.flynow-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.flynow-release.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		8446E19F2D5E6E2F0047C058 /* Watch Appapppersonalizado-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Watch Appapppersonalizado-debug.entitlements"; sourceTree = "<group>"; };
		8446E1A02D5E6E380047C058 /* Runnerapppersonalizado-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerapppersonalizado-debug.entitlements"; sourceTree = "<group>"; };
		8446E1A12D5E6E700047C058 /* Watch Appintensefit-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Watch Appintensefit-release.entitlements"; sourceTree = "<group>"; };
		84891AC12D8C51B80093B896 /* RunnerengenhariaDoCorpo-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "RunnerengenhariaDoCorpo-debug.entitlements"; sourceTree = "<group>"; };
		84896D7D2BB24202001610AB /* RunnerProfile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerProfile.entitlements; sourceTree = "<group>"; };
		84BB8A932D51056F0077FBA0 /* Watch Appliveacademia-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Watch Appliveacademia-debug.entitlements"; sourceTree = "<group>"; };
		84BB8A942D5105900077FBA0 /* Runnerliveacademia-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerliveacademia-debug.entitlements"; sourceTree = "<group>"; };
		84BB8A952D51085A0077FBA0 /* Watch Appliveacademia-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Watch Appliveacademia-release.entitlements"; sourceTree = "<group>"; };
		84BB8A962D5108680077FBA0 /* Runnerliveacademia-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerliveacademia-release.entitlements"; sourceTree = "<group>"; };
		84C2B7C12D4BB4DB0049B625 /* Runnerapppersonalizado-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerapppersonalizado-release.entitlements"; sourceTree = "<group>"; };
		84EC86462BA0ED0F00EC8666 /* Runnertreino-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnertreino-release.entitlements"; sourceTree = "<group>"; };
		84EC86472BA0F62100EC8666 /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		84EC86482BA0F62700EC8666 /* Runnertreino-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnertreino-debug.entitlements"; sourceTree = "<group>"; };
		84EC86492BA0F63B00EC8666 /* RunnerRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerRelease.entitlements; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		AE21988635F44DB36EBC7943 /* Pods-Runner.fitstream-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.fitstream-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.fitstream-debug.xcconfig"; sourceTree = "<group>"; };
		AFCA0C6ECA86E2C5CE2E3764 /* Pods-Runner.liveacademia-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.liveacademia-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.liveacademia-release.xcconfig"; sourceTree = "<group>"; };
		B4122CE52A0944D000997839 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		B4122CE62A09458C00997839 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		B429F6B82B6998D80046F3ED /* DetalhesSerieComponent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetalhesSerieComponent.swift; sourceTree = "<group>"; };
		B43270172B17FBB60048E790 /* flame.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = flame.json; sourceTree = "<group>"; };
		B476E4E62A0AB76D00756908 /* testing-Watch-App-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "testing-Watch-App-Info.plist"; sourceTree = SOURCE_ROOT; };
		B476E4E82A0AC0D400756908 /* TelaPrincipal.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelaPrincipal.swift; sourceTree = "<group>"; };
		B476E4EA2A0AC0FF00756908 /* Aulas.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Aulas.swift; sourceTree = "<group>"; };
		B476E4EC2A0AC10A00756908 /* Wod.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Wod.swift; sourceTree = "<group>"; };
		B476E4EE2A0AC12D00756908 /* Treino.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Treino.swift; sourceTree = "<group>"; };
		B476E4F12A0AC1AD00756908 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		B476E4F32A0AC33500756908 /* FichaModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FichaModel.swift; sourceTree = "<group>"; };
		B476E4F72A0AC54000756908 /* WorkoutManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WorkoutManager.swift; sourceTree = "<group>"; };
		B476E51B2A0AC7AB00756908 /* ElapsedTimeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ElapsedTimeView.swift; sourceTree = "<group>"; };
		B476E51C2A0AC7AB00756908 /* ControlesPlayPauseView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ControlesPlayPauseView.swift; sourceTree = "<group>"; };
		B476E51F2A0AC7AC00756908 /* TabBarExecucao.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TabBarExecucao.swift; sourceTree = "<group>"; };
		B476E5202A0AC7AC00756908 /* SummaryView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SummaryView.swift; sourceTree = "<group>"; };
		B476E5232A0AC7AC00756908 /* MetricasExecucaoView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MetricasExecucaoView.swift; sourceTree = "<group>"; };
		B476E5262A0AC7AC00756908 /* ActivityRingsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ActivityRingsView.swift; sourceTree = "<group>"; };
		B476E5432A0AD2CF00756908 /* testing Watch App.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "testing Watch App.entitlements"; sourceTree = "<group>"; };
		B47BA4332A0BC8A600436BB7 /* WodModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WodModel.swift; sourceTree = "<group>"; };
		B4A9CCCB2B61493200D8F8EC /* Playground.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Playground.swift; sourceTree = "<group>"; };
		B4B07BA42A94FA410079EB5A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		B4B07BA62A94FA6C0079EB5A /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/Main.strings"; sourceTree = "<group>"; };
		B4B07BA72A94FA6C0079EB5A /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		B4B07BA82A94FA770079EB5A /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		B4B07BA92A94FA980079EB5A /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Main.strings; sourceTree = "<group>"; };
		B4B07BAA2A94FA980079EB5A /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		B4B07BAB2A94FA990079EB5A /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		B4B7FC092C220D3B00101BE0 /* MeuPerfil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeuPerfil.swift; sourceTree = "<group>"; };
		B4BDD6D82A165BA9001DCF7F /* DadosUsuarioModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DadosUsuarioModel.swift; sourceTree = "<group>"; };
		B4E03A732A1544DA00AD0B26 /* TelaBemVindo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelaBemVindo.swift; sourceTree = "<group>"; };
		B4E03A762A1548F200AD0B26 /* Poppins-ExtraLight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-ExtraLight.ttf"; sourceTree = "<group>"; };
		B4E03A772A1548F200AD0B26 /* Poppins-ThinItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-ThinItalic.ttf"; sourceTree = "<group>"; };
		B4E03A782A1548F200AD0B26 /* Poppins-ExtraLightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		B4E03A792A1548F200AD0B26 /* Poppins-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-BoldItalic.ttf"; sourceTree = "<group>"; };
		B4E03A7A2A1548F200AD0B26 /* Poppins-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Light.ttf"; sourceTree = "<group>"; };
		B4E03A7B2A1548F200AD0B26 /* Poppins-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Medium.ttf"; sourceTree = "<group>"; };
		B4E03A7C2A1548F200AD0B26 /* Poppins-SemiBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		B4E03A7D2A1548F200AD0B26 /* Poppins-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		B4E03A7E2A1548F200AD0B26 /* Poppins-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-ExtraBold.ttf"; sourceTree = "<group>"; };
		B4E03A7F2A1548F200AD0B26 /* Poppins-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-BlackItalic.ttf"; sourceTree = "<group>"; };
		B4E03A812A1548F200AD0B26 /* Poppins-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Regular.ttf"; sourceTree = "<group>"; };
		B4E03A822A1548F200AD0B26 /* Poppins-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-LightItalic.ttf"; sourceTree = "<group>"; };
		B4E03A832A1548F200AD0B26 /* Poppins-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Bold.ttf"; sourceTree = "<group>"; };
		B4E03A842A1548F200AD0B26 /* Poppins-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Black.ttf"; sourceTree = "<group>"; };
		B4E03A852A1548F200AD0B26 /* Poppins-Thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Thin.ttf"; sourceTree = "<group>"; };
		B4E03A862A1548F200AD0B26 /* Poppins-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-SemiBold.ttf"; sourceTree = "<group>"; };
		B4E03A872A1548F200AD0B26 /* Poppins-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Italic.ttf"; sourceTree = "<group>"; };
		B4E03A882A1548F200AD0B26 /* Poppins-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-MediumItalic.ttf"; sourceTree = "<group>"; };
		B4E03AB22A15696B00AD0B26 /* LottieViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieViewModel.swift; sourceTree = "<group>"; };
		B4E03AB82A15791E00AD0B26 /* run_animation.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = run_animation.json; sourceTree = "<group>"; };
		B4F14B252A0C3901000FBEFF /* AulasModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AulasModel.swift; sourceTree = "<group>"; };
		C04FE3C16E19F90199189AD6 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		C4185A97D59A2D7833DD46EC /* Pods-Runner.box-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.box-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.box-debug.xcconfig"; sourceTree = "<group>"; };
		C477E49558F8672A3B727BCE /* Pods-Runner.apppersonalizado-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.apppersonalizado-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.apppersonalizado-release.xcconfig"; sourceTree = "<group>"; };
		C9D291B529176AA000727501 /* Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C9D291B729176AA000727501 /* testingApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = testingApp.swift; sourceTree = "<group>"; };
		C9D291B929176AA000727501 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		C9D291BB29176AA100727501 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		C9D291BE29176AA100727501 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		C9D291C529176AB600727501 /* WatchViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WatchViewModel.swift; sourceTree = "<group>"; };
		CBFA56AC8219935BC10649DF /* Pods-Runner.apppersonalizado-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.apppersonalizado-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.apppersonalizado-debug.xcconfig"; sourceTree = "<group>"; };
		CCD35F902D5E6B8200FDE1DA /* Runnerintensefit-debug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerintensefit-debug.entitlements"; sourceTree = "<group>"; };
		CCD35F912D5E6B8C00FDE1DA /* Runnerintensefit-release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Runnerintensefit-release.entitlements"; sourceTree = "<group>"; };
		D006D8509FD6157AF25D2253 /* Pods-Runner.engenhariadocorpo-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.engenhariadocorpo-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.engenhariadocorpo-release.xcconfig"; sourceTree = "<group>"; };
		D8D5C7CB88FDAB3B4097E219 /* Pods-Runner.maybootcamp-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.maybootcamp-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.maybootcamp-debug.xcconfig"; sourceTree = "<group>"; };
		D90A02175E9A8A7B3E6FA31B /* Pods-Runner.apppersonalizado-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.apppersonalizado-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.apppersonalizado-release.xcconfig"; sourceTree = "<group>"; };
		D9D6858E2445B5676B2E688C /* Pods-Runner.apppersonalizado-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.apppersonalizado-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.apppersonalizado-debug.xcconfig"; sourceTree = "<group>"; };
		DB26083150FBA32EEA3CB13E /* Pods-Runner.flynow-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.flynow-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.flynow-debug.xcconfig"; sourceTree = "<group>"; };
		F150D5FBD2DED2ED8459AEFA /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		FC6DB010F1D73F73C4026B18 /* Pods-Runner.treino-debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.treino-debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.treino-debug.xcconfig"; sourceTree = "<group>"; };
		FDD4AF2DBDCDF2B797071FED /* Pods-Runner.box-release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.box-release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.box-release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B4E03AA82A15604200AD0B26 /* SDWebImageSwiftUI in Frameworks */,
				B4122CE72A09458C00997839 /* StoreKit.framework in Frameworks */,
				77CB91502751D953E440508A /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9D291B229176AA000727501 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B4E03AAA2A15605500AD0B26 /* SDWebImageSwiftUI in Frameworks */,
				B4E03AB12A15694800AD0B26 /* SDWebImageLottieCoder in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				8446E1A12D5E6E700047C058 /* Watch Appintensefit-release.entitlements */,
				8446E19F2D5E6E2F0047C058 /* Watch Appapppersonalizado-debug.entitlements */,
				84BB8A952D51085A0077FBA0 /* Watch Appliveacademia-release.entitlements */,
				84BB8A932D51056F0077FBA0 /* Watch Appliveacademia-debug.entitlements */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				C9D291B629176AA000727501 /* testing Watch App */,
				97C146EF1CF9000F007C117D /* Products */,
				C9D291C729176AE000727501 /* Frameworks */,
				F09CAA7EA8AB569B7FDC8435 /* Pods */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				C9D291B529176AA000727501 /* Watch App.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				84891AC12D8C51B80093B896 /* RunnerengenhariaDoCorpo-debug.entitlements */,
				8446E1A02D5E6E380047C058 /* Runnerapppersonalizado-debug.entitlements */,
				CCD35F912D5E6B8C00FDE1DA /* Runnerintensefit-release.entitlements */,
				CCD35F902D5E6B8200FDE1DA /* Runnerintensefit-debug.entitlements */,
				84BB8A962D5108680077FBA0 /* Runnerliveacademia-release.entitlements */,
				84BB8A942D5105900077FBA0 /* Runnerliveacademia-debug.entitlements */,
				84C2B7C12D4BB4DB0049B625 /* Runnerapppersonalizado-release.entitlements */,
				84896D7D2BB24202001610AB /* RunnerProfile.entitlements */,
				84EC86492BA0F63B00EC8666 /* RunnerRelease.entitlements */,
				84EC86482BA0F62700EC8666 /* Runnertreino-debug.entitlements */,
				84EC86472BA0F62100EC8666 /* RunnerDebug.entitlements */,
				84EC86462BA0ED0F00EC8666 /* Runnertreino-release.entitlements */,
				B4B07BA52A94FA410079EB5A /* InfoPlist.strings */,
				97C147021CF9000F007C117D /* Info.plist */,
				B4122CE52A0944D000997839 /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		B476E4E72A0AC0BC00756908 /* View */ = {
			isa = PBXGroup;
			children = (
				B4D698F42A0ADDC200CBD393 /* ExecucaoTreino */,
				B476E5262A0AC7AC00756908 /* ActivityRingsView.swift */,
				B476E51B2A0AC7AB00756908 /* ElapsedTimeView.swift */,
				B476E5202A0AC7AC00756908 /* SummaryView.swift */,
				B476E4E82A0AC0D400756908 /* TelaPrincipal.swift */,
				B476E4EA2A0AC0FF00756908 /* Aulas.swift */,
				B476E4EC2A0AC10A00756908 /* Wod.swift */,
				B4B7FC092C220D3B00101BE0 /* MeuPerfil.swift */,
				B4E03A732A1544DA00AD0B26 /* TelaBemVindo.swift */,
				B4E03A752A1548F200AD0B26 /* Poppins */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B476E4F02A0AC1A200756908 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				B476E4F12A0AC1AD00756908 /* Extensions.swift */,
				B4E03AB22A15696B00AD0B26 /* LottieViewModel.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		B476E4F92A0AC59200756908 /* Controller */ = {
			isa = PBXGroup;
			children = (
				B476E4F72A0AC54000756908 /* WorkoutManager.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B4D698F42A0ADDC200CBD393 /* ExecucaoTreino */ = {
			isa = PBXGroup;
			children = (
				B476E4EE2A0AC12D00756908 /* Treino.swift */,
				B476E51C2A0AC7AB00756908 /* ControlesPlayPauseView.swift */,
				B476E5232A0AC7AC00756908 /* MetricasExecucaoView.swift */,
				B476E51F2A0AC7AC00756908 /* TabBarExecucao.swift */,
			);
			path = ExecucaoTreino;
			sourceTree = "<group>";
		};
		B4E03A752A1548F200AD0B26 /* Poppins */ = {
			isa = PBXGroup;
			children = (
				B4E03A762A1548F200AD0B26 /* Poppins-ExtraLight.ttf */,
				B4E03A772A1548F200AD0B26 /* Poppins-ThinItalic.ttf */,
				B4E03A782A1548F200AD0B26 /* Poppins-ExtraLightItalic.ttf */,
				B4E03A792A1548F200AD0B26 /* Poppins-BoldItalic.ttf */,
				B4E03A7A2A1548F200AD0B26 /* Poppins-Light.ttf */,
				B4E03A7B2A1548F200AD0B26 /* Poppins-Medium.ttf */,
				B4E03A7C2A1548F200AD0B26 /* Poppins-SemiBoldItalic.ttf */,
				B4E03A7D2A1548F200AD0B26 /* Poppins-ExtraBoldItalic.ttf */,
				B4E03A7E2A1548F200AD0B26 /* Poppins-ExtraBold.ttf */,
				B4E03A7F2A1548F200AD0B26 /* Poppins-BlackItalic.ttf */,
				B4E03A812A1548F200AD0B26 /* Poppins-Regular.ttf */,
				B4E03A822A1548F200AD0B26 /* Poppins-LightItalic.ttf */,
				B4E03A832A1548F200AD0B26 /* Poppins-Bold.ttf */,
				B4E03A842A1548F200AD0B26 /* Poppins-Black.ttf */,
				B4E03A852A1548F200AD0B26 /* Poppins-Thin.ttf */,
				B4E03A862A1548F200AD0B26 /* Poppins-SemiBold.ttf */,
				B4E03A872A1548F200AD0B26 /* Poppins-Italic.ttf */,
				B4E03A882A1548F200AD0B26 /* Poppins-MediumItalic.ttf */,
			);
			path = Poppins;
			sourceTree = "<group>";
		};
		C9D291B629176AA000727501 /* testing Watch App */ = {
			isa = PBXGroup;
			children = (
				B43270172B17FBB60048E790 /* flame.json */,
				B476E5432A0AD2CF00756908 /* testing Watch App.entitlements */,
				B4E03AB82A15791E00AD0B26 /* run_animation.json */,
				B476E4F92A0AC59200756908 /* Controller */,
				B476E4E72A0AC0BC00756908 /* View */,
				C9D291C429176AA900727501 /* Model */,
				B476E4F02A0AC1A200756908 /* Extensions */,
				B476E4E62A0AB76D00756908 /* testing-Watch-App-Info.plist */,
				C9D291B729176AA000727501 /* testingApp.swift */,
				C9D291B929176AA000727501 /* ContentView.swift */,
				C9D291BB29176AA100727501 /* Assets.xcassets */,
				C9D291BD29176AA100727501 /* Preview Content */,
				B4A9CCCB2B61493200D8F8EC /* Playground.swift */,
				B429F6B82B6998D80046F3ED /* DetalhesSerieComponent.swift */,
			);
			path = "testing Watch App";
			sourceTree = "<group>";
		};
		C9D291BD29176AA100727501 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				C9D291BE29176AA100727501 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		C9D291C429176AA900727501 /* Model */ = {
			isa = PBXGroup;
			children = (
				C9D291C529176AB600727501 /* WatchViewModel.swift */,
				B476E4F32A0AC33500756908 /* FichaModel.swift */,
				B47BA4332A0BC8A600436BB7 /* WodModel.swift */,
				B4F14B252A0C3901000FBEFF /* AulasModel.swift */,
				B4BDD6D82A165BA9001DCF7F /* DadosUsuarioModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		C9D291C729176AE000727501 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B4122CE62A09458C00997839 /* StoreKit.framework */,
				3307BAF434C532583E2C6ABC /* Pods_Runner.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F09CAA7EA8AB569B7FDC8435 /* Pods */ = {
			isa = PBXGroup;
			children = (
				248EBCE3B06F2E83E82A5D44 /* Pods-Runner.debug.xcconfig */,
				FC6DB010F1D73F73C4026B18 /* Pods-Runner.treino-debug.xcconfig */,
				C4185A97D59A2D7833DD46EC /* Pods-Runner.box-debug.xcconfig */,
				C04FE3C16E19F90199189AD6 /* Pods-Runner.release.xcconfig */,
				6D9F4B982D42D8CC1C6EF44D /* Pods-Runner.treino-release.xcconfig */,
				FDD4AF2DBDCDF2B797071FED /* Pods-Runner.box-release.xcconfig */,
				F150D5FBD2DED2ED8459AEFA /* Pods-Runner.profile.xcconfig */,
				CBFA56AC8219935BC10649DF /* Pods-Runner.apppersonalizado-debug.xcconfig */,
				D90A02175E9A8A7B3E6FA31B /* Pods-Runner.apppersonalizado-release.xcconfig */,
				3C90381C01AB2C2EA8A30060 /* Pods-Runner.liveacademia-debug.xcconfig */,
				AFCA0C6ECA86E2C5CE2E3764 /* Pods-Runner.liveacademia-release.xcconfig */,
				D8D5C7CB88FDAB3B4097E219 /* Pods-Runner.maybootcamp-debug.xcconfig */,
				699CA96F3BC20ECDF03DEE20 /* Pods-Runner.maybootcamp-release.xcconfig */,
				69265ABEC28EA6734CD637D4 /* Pods-Runner.intensefit-debug.xcconfig */,
				55972A456648780E06EB9ED4 /* Pods-Runner.intensefit-release.xcconfig */,
				AE21988635F44DB36EBC7943 /* Pods-Runner.fitstream-debug.xcconfig */,
				559CB1917F2EC6B6FEF523CD /* Pods-Runner.fitstream-release.xcconfig */,
				5C84920D527FCD9395506912 /* Pods-Runner.engenhariadocorpo-debug.xcconfig */,
				D006D8509FD6157AF25D2253 /* Pods-Runner.engenhariadocorpo-release.xcconfig */,
				DB26083150FBA32EEA3CB13E /* Pods-Runner.flynow-debug.xcconfig */,
				6DBC0DBB318D83D3493EEF65 /* Pods-Runner.flynow-release.xcconfig */,
				D9D6858E2445B5676B2E688C /* Pods-Runner.apppersonalizado-debug.xcconfig */,
				C477E49558F8672A3B727BCE /* Pods-Runner.apppersonalizado-release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				4C2252C2F6EF40E58B62EF0F /* [CP] Check Pods Manifest.lock */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				84E10EB62D490BB90066DB35 /* Copy Google Services file */,
				97C146EC1CF9000F007C117D /* Resources */,
				C9D291CB29176AE000727501 /* Embed Watch Content */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				A0FEAA4F4639F6529B8200EC /* [CP] Embed Pods Frameworks */,
				C61B198D6BB79E509F8F846D /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C9D291CA29176AE000727501 /* PBXTargetDependency */,
			);
			name = Runner;
			packageProductDependencies = (
				B4E03AA72A15604200AD0B26 /* SDWebImageSwiftUI */,
			);
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
		C9D291B429176AA000727501 /* Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9D291C329176AA100727501 /* Build configuration list for PBXNativeTarget "Watch App" */;
			buildPhases = (
				B4E03AAC2A1561CF00AD0B26 /* ShellScript */,
				C9D291B129176AA000727501 /* Sources */,
				C9D291B229176AA000727501 /* Frameworks */,
				C9D291B329176AA000727501 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Watch App";
			packageProductDependencies = (
				B4E03AA92A15605500AD0B26 /* SDWebImageSwiftUI */,
				B4E03AB02A15694800AD0B26 /* SDWebImageLottieCoder */,
			);
			productName = "testing Watch App";
			productReference = C9D291B529176AA000727501 /* Watch App.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1410;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
					C9D291B429176AA000727501 = {
						CreatedOnToolsVersion = 14.1;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"pt-BR",
				es,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			packageReferences = (
				B4E03AA62A15604200AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				B4E03AAF2A15692800AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageLottieCoder" */,
			);
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				C9D291B429176AA000727501 /* Watch App */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				B4B07BA32A94FA410079EB5A /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9D291B329176AA000727501 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B4E03AB92A15791E00AD0B26 /* run_animation.json in Resources */,
				B4E03A8D2A1548F300AD0B26 /* Poppins-Light.ttf in Resources */,
				B4E03A972A1548F300AD0B26 /* Poppins-Black.ttf in Resources */,
				B4E03A952A1548F300AD0B26 /* Poppins-LightItalic.ttf in Resources */,
				B4E03A982A1548F300AD0B26 /* Poppins-Thin.ttf in Resources */,
				B4E03A892A1548F300AD0B26 /* Poppins-ExtraLight.ttf in Resources */,
				B4E03A992A1548F300AD0B26 /* Poppins-SemiBold.ttf in Resources */,
				B43270182B17FBB60048E790 /* flame.json in Resources */,
				B4E03A962A1548F300AD0B26 /* Poppins-Bold.ttf in Resources */,
				B4E03A8A2A1548F300AD0B26 /* Poppins-ThinItalic.ttf in Resources */,
				B4E03A8B2A1548F300AD0B26 /* Poppins-ExtraLightItalic.ttf in Resources */,
				B4E03A922A1548F300AD0B26 /* Poppins-BlackItalic.ttf in Resources */,
				B4E03A902A1548F300AD0B26 /* Poppins-ExtraBoldItalic.ttf in Resources */,
				B4E03A8C2A1548F300AD0B26 /* Poppins-BoldItalic.ttf in Resources */,
				B4E03A8F2A1548F300AD0B26 /* Poppins-SemiBoldItalic.ttf in Resources */,
				B4E03A942A1548F300AD0B26 /* Poppins-Regular.ttf in Resources */,
				B4E03A912A1548F300AD0B26 /* Poppins-ExtraBold.ttf in Resources */,
				B4E03A8E2A1548F300AD0B26 /* Poppins-Medium.ttf in Resources */,
				C9D291BF29176AA100727501 /* Preview Assets.xcassets in Resources */,
				B4E03A9A2A1548F300AD0B26 /* Poppins-Italic.ttf in Resources */,
				B4E03A9B2A1548F300AD0B26 /* Poppins-MediumItalic.ttf in Resources */,
				C9D291BC29176AA100727501 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		4C2252C2F6EF40E58B62EF0F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		84E10EB62D490BB90066DB35 /* Copy Google Services file */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Copy Google Services file";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Caminho da pasta principal de configuração\nCONFIG_DIR=\"${PROJECT_DIR}/config\"\n\n# Caminho do arquivo plist de destino no app bundle\nTARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\n\n# Caminho do arquivo correspondente à configuração atual\nSOURCE_PLIST=\"${CONFIG_DIR}/${CONFIGURATION%%-*}/GoogleService-Info.plist\"\necho \"Sourcex: ${SOURCE_PLIST}\"\n# Verifica se o arquivo plist existe\nif [ -f \"${SOURCE_PLIST}\" ]; then\n  # Copia o arquivo plist para o destino\n  cp \"${SOURCE_PLIST}\" \"${TARGET_PLIST}\"\n  echo \"Copiado: ${SOURCE_PLIST} para ${TARGET_PLIST}\"\nelse\n  echo \"Erro: ${SOURCE_PLIST} não encontrado.\"\n  exit 1\nfi\n";
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		A0FEAA4F4639F6529B8200EC /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B4E03AAC2A1561CF00AD0B26 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nif [ -d \"${SYMROOT}/Release${EFFECTIVE_PLATFORM_NAME}/\" ] && [ \"${SYMROOT}/Release${EFFECTIVE_PLATFORM_NAME}/\" != \"${SYMROOT}/${CONFIGURATION}${EFFECTIVE_PLATFORM_NAME}/\" ] \nthen\n  cp -f -R \"${SYMROOT}/Release${EFFECTIVE_PLATFORM_NAME}/\" \"${SYMROOT}/${CONFIGURATION}${EFFECTIVE_PLATFORM_NAME}/\"\nfi\n";
		};
		C61B198D6BB79E509F8F846D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9D291B129176AA000727501 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B4BDD6D92A165BA9001DCF7F /* DadosUsuarioModel.swift in Sources */,
				C9D291BA29176AA000727501 /* ContentView.swift in Sources */,
				C9D291B829176AA000727501 /* testingApp.swift in Sources */,
				B476E5322A0AC7AD00756908 /* SummaryView.swift in Sources */,
				B476E5342A0AC7AD00756908 /* MetricasExecucaoView.swift in Sources */,
				B47BA4342A0BC8A600436BB7 /* WodModel.swift in Sources */,
				B476E4EF2A0AC12D00756908 /* Treino.swift in Sources */,
				B476E4F22A0AC1AD00756908 /* Extensions.swift in Sources */,
				B476E52F2A0AC7AD00756908 /* ControlesPlayPauseView.swift in Sources */,
				B4F14B262A0C3901000FBEFF /* AulasModel.swift in Sources */,
				B476E52E2A0AC7AD00756908 /* ElapsedTimeView.swift in Sources */,
				B4B7FC0A2C220D3B00101BE0 /* MeuPerfil.swift in Sources */,
				B429F6B92B6998D80046F3ED /* DetalhesSerieComponent.swift in Sources */,
				B4E03A742A1544DA00AD0B26 /* TelaBemVindo.swift in Sources */,
				B476E4ED2A0AC10A00756908 /* Wod.swift in Sources */,
				B476E5312A0AC7AD00756908 /* TabBarExecucao.swift in Sources */,
				B476E4E92A0AC0D400756908 /* TelaPrincipal.swift in Sources */,
				B476E4EB2A0AC0FF00756908 /* Aulas.swift in Sources */,
				B476E5372A0AC7AD00756908 /* ActivityRingsView.swift in Sources */,
				B476E4F82A0AC54000756908 /* WorkoutManager.swift in Sources */,
				B4E03AB32A15696B00AD0B26 /* LottieViewModel.swift in Sources */,
				B4A9CCCC2B61493200D8F8EC /* Playground.swift in Sources */,
				C9D291C629176AB600727501 /* WatchViewModel.swift in Sources */,
				B476E4F42A0AC33500756908 /* FichaModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C9D291CA29176AE000727501 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C9D291B429176AA000727501 /* Watch App */;
			targetProxy = C9D291C929176AE000727501 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
				B4B07BA62A94FA6C0079EB5A /* pt-BR */,
				B4B07BA92A94FA980079EB5A /* es */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
				B4B07BA72A94FA6C0079EB5A /* pt-BR */,
				B4B07BAA2A94FA980079EB5A /* es */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		B4B07BA52A94FA410079EB5A /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				B4B07BA42A94FA410079EB5A /* en */,
				B4B07BA82A94FA770079EB5A /* pt-BR */,
				B4B07BAB2A94FA990079EB5A /* es */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerProfile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = Treino;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		8433A54C2D4807AF00180A3C /* apppersonalizado-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.apppersonalizado;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "apppersonalizado-debug";
		};
		8433A54D2D4807AF00180A3C /* apppersonalizado-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.apppersonalizado;
				BUNDLE_ID_SUFFIX = .apppersonalizado;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "{appDisplayName}";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = "${BUNDLE_IDENTIFIER}";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.apppersonalizado;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "apppersonalizado-debug";
		};
		8433A54E2D4807AF00180A3C /* apppersonalizado-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .apppersonalizado;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appapppersonalizado-debug.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "{appDisplayName}";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.apppersonalizado;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.apppersonalizado.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "apppersonalizado-debug";
		};
		8433A54F2D4807CB00180A3C /* apppersonalizado-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.apppersonalizado;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "apppersonalizado-release";
		};
		8433A5502D4807CB00180A3C /* apppersonalizado-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.apppersonalizado;
				BUNDLE_ID_SUFFIX = .apppersonalizado;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "{appDisplayName}";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.apppersonalizado;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "apppersonalizado-release";
		};
		8433A5512D4807CB00180A3C /* apppersonalizado-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .apppersonalizado;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "{appDisplayName}";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.apppersonalizado;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.apppersonalizado.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "apppersonalizado-release";
		};
		84891ABB2D8C4CC00093B896 /* engenhariaDoCorpo-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.engenhariadocorpo.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "engenhariaDoCorpo-release";
		};
		84891ABC2D8C4CC00093B896 /* engenhariaDoCorpo-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.engenhariadocorpo.ios;
				BUNDLE_ID_SUFFIX = .engenhariaDoCorpo;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "EC PRO";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.engenhariadocorpo.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.engenhariadocorpo.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "engenhariaDoCorpo-release";
		};
		84891ABD2D8C4CC00093B896 /* engenhariaDoCorpo-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .engenhariaDoCorpo;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "EC PRO";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.engenhariadocorpo.ios;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.engenhariadocorpo.ios.watchkitappedc;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "engenhariaDoCorpo-release";
		};
		84891ABE2D8C4CD30093B896 /* engenhariaDoCorpo-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.apppersonalizado;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "engenhariaDoCorpo-debug";
		};
		84891ABF2D8C4CD30093B896 /* engenhariaDoCorpo-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.engenhariadocorpo.ios;
				BUNDLE_ID_SUFFIX = .engenhariaDoCorpo;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/RunnerengenhariaDoCorpo-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "EC PRO";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.engenhariadocorpo.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "engenhariaDoCorpo-debug";
		};
		84891AC02D8C4CD30093B896 /* engenhariaDoCorpo-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .engenhariaDoCorpo;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appapppersonalizado-debug.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "EC PRO";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.engenhariadocorpo.ios;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.engenhariadocorpo.ios.watchkitappedc;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "engenhariaDoCorpo-debug";
		};
		84D58F0E2D8DE0C100C1EDF6 /* flynow-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.flaynowacademia;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "flynow-release";
		};
		84D58F0F2D8DE0C100C1EDF6 /* flynow-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.sistemapacto.flaynowacademia;
				BUNDLE_ID_SUFFIX = .flynow;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 8DTF26T3J9;
				DISPLAY_NAME = "Fly Now";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.sistemapacto.flaynowacademia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "flynow-release";
		};
		84D58F102D8DE0C100C1EDF6 /* flynow-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .flynow;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 8DTF26T3J9;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fly Now";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.flaynowacademia;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.sistemapacto.flaynowacademia.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "flynow-release";
		};
		84D58F112D8DE0D700C1EDF6 /* flynow-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.flaynowacademia;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "flynow-debug";
		};
		84D58F122D8DE0D700C1EDF6 /* flynow-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.sistemapacto.flaynowacademia;
				BUNDLE_ID_SUFFIX = .flynow;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 8DTF26T3J9;
				DISPLAY_NAME = "Fly Now";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.flaynowacademia;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.sistemapacto.flaynowacademia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "flynow-debug";
		};
		84D58F132D8DE0D700C1EDF6 /* flynow-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .flynow;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appapppersonalizado-debug.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fly Now";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.flaynowacademia;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.sistemapacto.flaynowacademia.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "flynow-debug";
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = Treino;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = Treino;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		B4B07BAC2A94FFA00079EB5A /* treino-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "treino-debug";
		};
		B4B07BAD2A94FFA00079EB5A /* treino-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnertreino-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = Treino;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "treino-debug";
		};
		B4B07BAE2A94FFA00079EB5A /* treino-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.treino;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Treino;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "treino-debug";
		};
		B4B07BAF2A94FFB00079EB5A /* treino-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "treino-release";
		};
		B4B07BB02A94FFB00079EB5A /* treino-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnertreino-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = Treino;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "treino-release";
		};
		B4B07BB12A94FFB00079EB5A /* treino-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Treino;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.treino.ios;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "treino-release";
		};
		B4C161CE2BB3129E001E7AB3 /* box-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.zwcross;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "box-debug";
		};
		B4C161CF2BB3129E001E7AB3 /* box-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.zwcross;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Meu Box";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "box-debug";
		};
		B4C161D02BB3129E001E7AB3 /* box-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.box;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fábrica de Monstros CT";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.zwcross;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.zwcross.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "box-debug";
		};
		B4C161D12BB312B2001E7AB3 /* box-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.zwcross;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "box-release";
		};
		B4C161D22BB312B2001E7AB3 /* box-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.zwcross;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Meu Box";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "box-release";
		};
		B4C161D32BB312B2001E7AB3 /* box-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.box;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .box;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fábrica de Monstros CT";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.zwcross;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.zwcross.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "box-release";
		};
		C9D291C029176AA100727501 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.treino;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fábrica de Monstros CT";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		C9D291C129176AA100727501 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.treino;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fábrica de Monstros CT";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		C9D291C229176AA100727501 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.treino;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .treino;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fábrica de Monstros CT";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.treino.ios.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Profile;
		};
		CC12CA6E2D4CF84100C155B1 /* liveacademia-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.liveacademia;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "liveacademia-debug";
		};
		CC12CA6F2D4CF84100C155B1 /* liveacademia-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.liveacademia;
				BUNDLE_ID_SUFFIX = .liveacademia;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerliveacademia-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Live Academia";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.liveacademia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "liveacademia-debug";
		};
		CC12CA702D4CF84100C155B1 /* liveacademia-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .liveacademia;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appliveacademia-debug.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Live Academia";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.liveacademia;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.liveacademia.ioswatchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "liveacademia-debug";
		};
		CC12CA712D4CF85F00C155B1 /* liveacademia-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.liveacademia;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "liveacademia-release";
		};
		CC12CA722D4CF85F00C155B1 /* liveacademia-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.liveacademia;
				BUNDLE_ID_SUFFIX = .liveacademia;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerliveacademia-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Live Academia";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.liveacademia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "liveacademia-release";
		};
		CC12CA732D4CF85F00C155B1 /* liveacademia-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .liveacademia;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appliveacademia-release.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Live Academia";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.liveacademia;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.liveacademia.ioswatchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "liveacademia-release";
		};
		CC1A85752D5B8D2E006967D8 /* intensefit-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.intensefit;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "intensefit-debug";
		};
		CC1A85762D5B8D2E006967D8 /* intensefit-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.sistemapacto.intensefit;
				BUNDLE_ID_SUFFIX = .intensefit;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerintensefit-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = intensefit;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.sistemapacto.intensefit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "intensefit-debug";
		};
		CC1A85772D5B8D2E006967D8 /* intensefit-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .intensefit;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appliveacademia-debug.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Intense Fit";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.intensefit;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.sistemapacto.intensefit.ioswatchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "intensefit-debug";
		};
		CC1A85782D5B8D41006967D8 /* intensefit-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.intensefit;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "intensefit-release";
		};
		CC1A85792D5B8D41006967D8 /* intensefit-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.sistemapacto.intensefit;
				BUNDLE_ID_SUFFIX = .intensefit;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerintensefit-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = V5MASRDAR5;
				DISPLAY_NAME = intensefit;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.sistemapacto.intensefit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "intensefit-release";
		};
		CC1A857A2D5B8D41006967D8 /* intensefit-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .intensefit;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "Watch Appintensefit-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = V5MASRDAR5;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Intense Fit";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.sistemapacto.intensefit;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.sistemapacto.intensefit.ioswatchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "intensefit-release";
		};
		CC637A4C2D52ADC5001F6073 /* maybootcamp-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.maybootcamp;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "maybootcamp-debug";
		};
		CC637A4D2D52ADC5001F6073 /* maybootcamp-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.maybootcamp;
				BUNDLE_ID_SUFFIX = .maybootcamp;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnertreino-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 869QJQ68C3;
				DISPLAY_NAME = "May Bootcamp";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.maybootcamp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "maybootcamp-debug";
		};
		CC637A4E2D52ADC5001F6073 /* maybootcamp-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .maybootcamp;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "May Bootcamp";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.maybootcamp;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.maybootcamp.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "maybootcamp-debug";
		};
		CC637A4F2D52ADD9001F6073 /* maybootcamp-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.maybootcamp;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "maybootcamp-release";
		};
		CC637A502D52ADD9001F6073 /* maybootcamp-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.maybootcamp;
				BUNDLE_ID_SUFFIX = .maybootcamp;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnerapppersonalizado-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "May Bootcamp";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.maybootcamp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "maybootcamp-release";
		};
		CC637A512D52ADD9001F6073 /* maybootcamp-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .maybootcamp;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "May Bootcamp";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.maybootcamp;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.maybootcamp.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "maybootcamp-release";
		};
		CCF3F0E72D5D351200FFD759 /* fitstream-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.fitstream;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "fitstream-debug";
		};
		CCF3F0E82D5D351200FFD759 /* fitstream-debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.fitstream;
				BUNDLE_ID_SUFFIX = .fitstream;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnertreino-debug.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Fit Stream BR";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.fitstream;
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "fitstream-debug";
		};
		CCF3F0E92D5D351200FFD759 /* fitstream-debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.fitstream;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .fitstream;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fit Stream";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.fitstream;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.fitstream.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "fitstream-debug";
		};
		CCF3F0EA2D5D352300FFD759 /* fitstream-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.fitstream;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "fitstream-release";
		};
		CCF3F0EB2D5D352300FFD759 /* fitstream-release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon${BUNDLE_ID_SUFFIX}";
				BUNDLE_IDENTIFIER = br.com.pactosolucoes.fitstream;
				BUNDLE_ID_SUFFIX = .fitstream;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/Runnertreino-release.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				DISPLAY_NAME = "Fit Stream BR";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 6.0.0;
				FLUTTER_BUILD_NUMBER = 2;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = "${BUNDLE_IDENTIFIER}";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = br.com.pactosolucoes.fitstream;
				PRODUCT_NAME = Treino;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "fitstream-release";
		};
		CCF3F0EC2D5D352300FFD759 /* fitstream-release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon$(BUNDLE_ID_SUFFIX)";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BUNDLE_ID_SUFFIX = .fitstream;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "testing Watch App/testing Watch App.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_ASSET_PATHS = "\"testing Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 43J75W8CL6;
				ENABLE_PREVIEWS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "testing-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Fit Stream";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = br.com.pactosolucoes.fitstream;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = "$(FLUTTER_BUILD_NAME)";
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = br.com.pactosolucoes.fitstream.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = "fitstream-release";
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				B4B07BAC2A94FFA00079EB5A /* treino-debug */,
				CCF3F0E72D5D351200FFD759 /* fitstream-debug */,
				8433A54C2D4807AF00180A3C /* apppersonalizado-debug */,
				84D58F112D8DE0D700C1EDF6 /* flynow-debug */,
				84891ABE2D8C4CD30093B896 /* engenhariaDoCorpo-debug */,
				CC637A4C2D52ADC5001F6073 /* maybootcamp-debug */,
				CC12CA6E2D4CF84100C155B1 /* liveacademia-debug */,
				CC1A85752D5B8D2E006967D8 /* intensefit-debug */,
				B4C161CE2BB3129E001E7AB3 /* box-debug */,
				97C147041CF9000F007C117D /* Release */,
				B4B07BAF2A94FFB00079EB5A /* treino-release */,
				CCF3F0EA2D5D352300FFD759 /* fitstream-release */,
				B4C161D12BB312B2001E7AB3 /* box-release */,
				8433A54F2D4807CB00180A3C /* apppersonalizado-release */,
				84D58F0E2D8DE0C100C1EDF6 /* flynow-release */,
				84891ABB2D8C4CC00093B896 /* engenhariaDoCorpo-release */,
				CC637A4F2D52ADD9001F6073 /* maybootcamp-release */,
				CC12CA712D4CF85F00C155B1 /* liveacademia-release */,
				CC1A85782D5B8D41006967D8 /* intensefit-release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				B4B07BAD2A94FFA00079EB5A /* treino-debug */,
				CCF3F0E82D5D351200FFD759 /* fitstream-debug */,
				8433A54D2D4807AF00180A3C /* apppersonalizado-debug */,
				84D58F122D8DE0D700C1EDF6 /* flynow-debug */,
				84891ABF2D8C4CD30093B896 /* engenhariaDoCorpo-debug */,
				CC637A4D2D52ADC5001F6073 /* maybootcamp-debug */,
				CC12CA6F2D4CF84100C155B1 /* liveacademia-debug */,
				CC1A85762D5B8D2E006967D8 /* intensefit-debug */,
				B4C161CF2BB3129E001E7AB3 /* box-debug */,
				97C147071CF9000F007C117D /* Release */,
				B4B07BB02A94FFB00079EB5A /* treino-release */,
				CCF3F0EB2D5D352300FFD759 /* fitstream-release */,
				B4C161D22BB312B2001E7AB3 /* box-release */,
				8433A5502D4807CB00180A3C /* apppersonalizado-release */,
				84D58F0F2D8DE0C100C1EDF6 /* flynow-release */,
				84891ABC2D8C4CC00093B896 /* engenhariaDoCorpo-release */,
				CC637A502D52ADD9001F6073 /* maybootcamp-release */,
				CC12CA722D4CF85F00C155B1 /* liveacademia-release */,
				CC1A85792D5B8D41006967D8 /* intensefit-release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9D291C329176AA100727501 /* Build configuration list for PBXNativeTarget "Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9D291C029176AA100727501 /* Debug */,
				B4B07BAE2A94FFA00079EB5A /* treino-debug */,
				CCF3F0E92D5D351200FFD759 /* fitstream-debug */,
				8433A54E2D4807AF00180A3C /* apppersonalizado-debug */,
				84D58F132D8DE0D700C1EDF6 /* flynow-debug */,
				84891AC02D8C4CD30093B896 /* engenhariaDoCorpo-debug */,
				CC637A4E2D52ADC5001F6073 /* maybootcamp-debug */,
				CC12CA702D4CF84100C155B1 /* liveacademia-debug */,
				CC1A85772D5B8D2E006967D8 /* intensefit-debug */,
				B4C161D02BB3129E001E7AB3 /* box-debug */,
				C9D291C129176AA100727501 /* Release */,
				B4B07BB12A94FFB00079EB5A /* treino-release */,
				CCF3F0EC2D5D352300FFD759 /* fitstream-release */,
				B4C161D32BB312B2001E7AB3 /* box-release */,
				8433A5512D4807CB00180A3C /* apppersonalizado-release */,
				84D58F102D8DE0C100C1EDF6 /* flynow-release */,
				84891ABD2D8C4CC00093B896 /* engenhariaDoCorpo-release */,
				CC637A512D52ADD9001F6073 /* maybootcamp-release */,
				CC12CA732D4CF85F00C155B1 /* liveacademia-release */,
				CC1A857A2D5B8D41006967D8 /* intensefit-release */,
				C9D291C229176AA100727501 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B4E03AA62A15604200AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
		B4E03AAF2A15692800AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageLottieCoder" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageLottieCoder.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B4E03AA72A15604200AD0B26 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = B4E03AA62A15604200AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		B4E03AA92A15605500AD0B26 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = B4E03AA62A15604200AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		B4E03AB02A15694800AD0B26 /* SDWebImageLottieCoder */ = {
			isa = XCSwiftPackageProductDependency;
			package = B4E03AAF2A15692800AD0B26 /* XCRemoteSwiftPackageReference "SDWebImageLottieCoder" */;
			productName = SDWebImageLottieCoder;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
