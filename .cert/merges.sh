#!/bin/bash

# Obter as 15 tags mais recentes que correspondem aos padrões solicitados
tag_array=()

while IFS= read -r tag; do
    tag_array+=("$tag")
done < <(git tag --sort=-creatordate | grep -E '[0-9]+\.[0-9]+\.[0-9]+(-tf-[0-9]+|-release|-pacto)' | head -n 15)

# Verificar se existem tags
if [ ${#tag_array[@]} -eq 0 ]; then
    echo "Nenhuma tag encontrada no repositório que corresponda aos padrões:"
    echo "  - x.x.x-tf-x"
    echo "  - x.x.x-release"
    echo "  - x.x.x-pacto"
    exit 1
fi

# Função para exibir o menu de seleção
show_tag_menu() {
    echo "Selecione uma tag usando as setas (↑/↓) e pressione Enter:"
    echo "----------------------------------------"
    for i in "${!tag_array[@]}"; do
        if [ $i -eq $selected ]; then
            echo "→ ${tag_array[$i]}"
        else
            echo "  ${tag_array[$i]}"
        fi
    done
    echo "----------------------------------------"
    echo "Use as setas ↑/↓ para navegar, Enter para selecionar"
}

# Implementar navegação com setas
selected=0
key=""

# Função para ler uma tecla
read_key() {
    # Configuração do terminal para leitura de teclas individuais
    old_settings=$(stty -g)
    stty -echo -icanon min 1 time 0
    
    # Ler a tecla pressionada
    dd bs=1 count=1 2>/dev/null
    
    # Restaurar configurações do terminal
    stty "$old_settings"
}

# Loop de navegação no menu
while true; do
    clear
    show_tag_menu
    
    # Ler a tecla
    key=$(read_key)
    
    # Verificar qual tecla foi pressionada
    case "$key" in
        $'\e')  # Sequência de escape (possivelmente seta)
            read_key > /dev/null  # Ler o [
            arrow=$(read_key)  # Ler o tipo de seta (A=cima, B=baixo)
            
            case "$arrow" in
                A)  # Seta para cima
                    if [ $selected -gt 0 ]; then
                        selected=$((selected - 1))
                    fi
                    ;;
                B)  # Seta para baixo
                    if [ $selected -lt $((${#tag_array[@]} - 1)) ]; then
                        selected=$((selected + 1))
                    fi
                    ;;
            esac
            ;;
        "")  # Enter
            selected_tag="${tag_array[$selected]}"
            break
            ;;
    esac
done

clear
echo "Tag selecionada: $selected_tag"
echo

# Listar os merges entre a tag selecionada e HEAD
# Extrair apenas a versão no formato 0.0.0 da tag selecionada
clean_version=$(echo "$selected_tag" | grep -o '^[0-9]\+\.[0-9]\+\.[0-9]\+')

echo "Mudanças desde a versão $clean_version, tickets adicionados na nova versão"



# Solicitar o número da versão
echo "Digite o número da nova versão (formato 0.0.0):"
read version_number

# Validar o formato da versão
while ! [[ $version_number =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; do
    echo "Formato inválido. Por favor, digite a versão no formato 0.0.0:"
    read version_number
done

# Gerar relatório para QA
echo
echo "Relatório para QA..."
echo
# Limpar o console
clear
# Exibir relatório diretamente no terminal
echo "====================================================="
echo "Validação de QA - Versão ${version_number}"
echo "====================================================="
echo
echo "Olá equipe de QA,"
echo
echo "Por favor, realizem a validação completa da versão ${version_number} do aplicativo."
echo
echo "Tickets para verificação nesta versão:"
# Criar um array para armazenar IDs únicos
unique_apps_ids=()

# Usar process substitution para evitar o subshell do pipe
while read -r line; do
    commit=$(echo $line | awk '{print $1}')
    commit_msg=$(echo $line | cut -d' ' -f3-)
    
    # Obter a branch de origem do merge
    branch=$(git show -s --format=%B $commit | grep -o "from [^ ]*" | sed 's/from //' | tr -d "'")
    
    # Se não conseguir extrair a branch desta forma, tente outra abordagem
    if [ -z "$branch" ]; then
        branch=$(git show -s --format=%B $commit | grep -o "branch '[^']*'" | sed "s/branch '//;s/'//")
    fi
    
    # Extrair IDs APPS do commit e da branch
    apps_ids=$(echo "$commit_msg $branch" | grep -o 'APPS-[0-9]\+')
    
    # Adicionar IDs ao array se não existirem ainda
    if [ ! -z "$apps_ids" ]; then
        while IFS= read -r id; do
            # Verificar se o ID já existe no array antes de adicionar
            if [[ ! " ${unique_apps_ids[@]} " =~ " ${id} " ]]; then
                unique_apps_ids+=("$id")
            fi
        done <<< "$apps_ids"
    fi
done < <(git log --merges --pretty=format:"%h %cr %s" $selected_tag..HEAD)

# Imprimir os IDs únicos
for id in "${unique_apps_ids[@]}"; do
    echo "https://pacto.atlassian.net/browse/$id"
done

echo
echo "Verificações padrão a serem realizadas:"
echo "1. Funcionalidade geral do aplicativo"
echo "2. Performance e tempo de resposta"
echo "3. Compatibilidade com diferentes dispositivos"
echo "4. Navegação e fluxo do usuário"
echo "5. Verificar se não há regressões em funcionalidades existentes"
echo "6. Teste de login/logout e autenticação"
echo "7. Verificar todas as notificações e alertas"
echo
echo
echo "Qualquer problema encontrado deve ser reportado imediatamente usando o template padrão de bugs."
echo
echo "Obrigado!"
echo "Equipe de Desenvolvimento"
echo "====================================================="
