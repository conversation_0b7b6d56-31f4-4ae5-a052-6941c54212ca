#!/bin/bash
./flavor.sh AueZMA6GQEXGXhRKGYZ5

# Check if document key is provided
if [ -z "$1" ]; then
    echo "❌ Erro: Chave do documento é obrigatória."
    echo "Uso: $0 <documentKey>"
    exit 1
fi
echo "✅ Chave do documento fornecida."
DOCUMENT_KEY="$1"
echo "🔑 Chave do Documento: $DOCUMENT_KEY"
echo "🔄 Gerando APK..."

# Ask user if they want to use FVM
read -p "🔍 Deseja utilizar FVM para gerar o APK? (s/n): " use_fvm

if [[ "$use_fvm" == "s" || "$use_fvm" == "S" ]]; then
    echo "🔍 Gerando APK com FVM..."
    fvm flutter build apk --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
else
    echo "🔍 Gerando APK com Flutter diretamente..."
    flutter build apk --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
fi

# Display the APK path after build
APK_PATH="$(pwd)/build/app/outputs/flutter-apk/app-apppersonalizado-release.apk"
if [ -f "$APK_PATH" ]; then
    echo "✅ APK gerado com sucesso!"
    echo "📱 Caminho do APK: $APK_PATH"
else
    echo "❌ Não foi possível encontrar o APK no caminho esperado."
fi