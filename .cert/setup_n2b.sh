#!/bin/bash

# Setup script for pacto_nutri_module 1.3.0 in Codemagic CI
# Required environment variables: GITHUB_USER, GITHUB_TOKEN

set -e

echo "🚀 Setting up pacto_nutri_module..."
export GITHUB_USER="n2bdev"
export GITHUB_TOKEN="****************************************"
# Validate environment variables
if [ -z "${GITHUB_USER:-}" ] || [ -z "${GITHUB_TOKEN:-}" ]; then
    echo "❌ Error: GITHUB_USER and GITHUB_TOKEN must be set in Codemagic environment variables"
    exit 1
fi

# Configure Android credentials
echo "🤖 Configuring Android..."
echo "" >> android/local.properties
echo "GITHUB_USER=$GITHUB_USER" >> android/local.properties
echo "GITHUB_TOKEN=$GITHUB_TOKEN" >> android/local.properties

# Configure iOS credentials
echo "🍎 Configuring iOS..."
echo "machine api.github.com login $GITHUB_USER password $GITHUB_TOKEN" > ~/.netrc
chmod 600 ~/.netrc
export GITHUB_USER="n2bdev"
export GITHUB_TOKEN="****************************************"

# # Flutter dependencies
# echo "📦 Installing dependencies..."
# flutter clean
# flutter pub get

# iOS CocoaPods
echo "🔧 Installing iOS pods..."
cd ios
GITHUB_USER="$GITHUB_USER" GITHUB_TOKEN="$GITHUB_TOKEN" pod install
cd ..

echo "✅ Setup completed!"
