# Testes Automatizados - Migração Maestro para Appium

Este projeto é uma POC (Proof of Concept) para migrar os testes automatizados do Maestro para o Appium.

## 📋 Pré-requisitos

### Software Necessário
- **Node.js** (versão 16 ou superior)
- **npm** (vem com Node.js)
- **Appium** (será instalado automaticamente)
- **Java JDK** (para Android)
- **Xcode** (para iOS - apenas macOS)
- **Android Studio** (para Android)

### Dispositivos/Emuladores
- **iOS**: Simulador iOS ou dispositivo físico
- **Android**: Emulador Android ou dispositivo físico

## 🚀 Configuração Inicial

### 1. Instalar Dependências
```bash
cd teste_auto_appium
npm run setup
```

Este comando irá:
- Instalar todas as dependências do projeto
- Instalar o Appium globalmente
- Instalar os drivers necessários (iOS e Android)
- Criar a estrutura de diretórios
- Executar diagnóstico do ambiente

### 2. Configurar Variáveis de Ambiente
Edite o arquivo `.env` com suas configurações:

```env
# Plataforma (ios ou android)
PLATFORM=ios

# Dados de teste
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=sua-senha
TEST_GYM_NAME=Nome-da-Academia
```

### 3. Verificar Configuração
```bash
npm run doctor
```

## 🏃‍♂️ Executando os Testes

### Iniciar Servidor Appium
Em um terminal separado:
```bash
npm run start-appium
```

### Executar Testes

#### Todos os testes
```bash
npm test
```

#### Apenas testes de login
```bash
npm run test:login
```

#### Testes específicos por plataforma
```bash
# iOS
npm run test:ios
npm run test:ios:login

# Android
npm run test:android
npm run test:android:login
```

## 📁 Estrutura do Projeto

```
teste_auto_appium/
├── test/
│   └── login/
│       ├── login-only-aluno.test.js
│       └── troca-de-academia.test.js
├── utils/
│   └── appium-helper.js
├── scripts/
│   ├── setup.js
│   └── start-appium.js
├── reports/          # Relatórios de teste
├── screenshots/      # Screenshots de erro
├── apps/            # Arquivos APK/APP
├── logs/            # Logs de execução
├── .env             # Variáveis de ambiente
├── appium.config.js # Configuração do Appium
├── .mocharc.json    # Configuração do Mocha
└── package.json     # Dependências e scripts
```

## 🔄 Migração do Maestro

### Comandos Migrados

| Maestro | Appium | Descrição |
|---------|--------|-----------|
| `tapOn: "texto"` | `appiumHelper.tapOn('texto')` | Toque em elemento por texto |
| `tapOn: {id: "id"}` | `appiumHelper.tapOn({id: 'id'})` | Toque em elemento por ID |
| `tapOn: {point: "x%,y%"}` | `appiumHelper.tapOn({point: 'x%,y%'})` | Toque em coordenadas |
| `inputText: "texto"` | `appiumHelper.inputText('texto')` | Inserir texto |
| `waitForAnimationToEnd` | `appiumHelper.waitForAnimationToEnd()` | Aguardar animação |

### Arquivos Migrados

#### ✅ Concluídos
- `login_only_aluno.yaml` → `login-only-aluno.test.js`
- `trocar_academia.yaml` → `troca-de-academia.test.js`
- `selecionar_academia.yaml` → `troca-de-academia.test.js`

## 🛠️ Comandos Úteis

```bash
# Limpar relatórios e screenshots
npm run clean

# Verificar saúde do ambiente Appium
npm run doctor

# Instalar dependências
npm install

# Executar setup completo
npm run setup
```

## 🐛 Solução de Problemas

### Appium não encontrado
```bash
npm install -g appium@next
```

### Drivers não instalados
```bash
appium driver install xcuitest
appium driver install uiautomator2
```

### Erro de permissões (macOS/Linux)
```bash
sudo npm install -g appium@next
```

### Verificar dispositivos conectados

#### iOS
```bash
xcrun simctl list devices
```

#### Android
```bash
adb devices
```

## 📊 Relatórios

Os relatórios de teste são gerados automaticamente na pasta `reports/`.

## 🔍 Debug

Para debug detalhado, inicie o Appium com log verbose:
```bash
appium --log-level debug
```

## 📝 Notas Importantes

1. **Seletores**: Os seletores podem precisar de ajustes dependendo da versão do app
2. **Timeouts**: Ajuste os timeouts conforme necessário no arquivo `appium.config.js`
3. **Coordenadas**: As coordenadas por porcentagem podem variar entre dispositivos
4. **Estabilidade**: Adicione waits adequados entre ações para maior estabilidade

## 🤝 Contribuindo

1. Adicione novos testes na pasta `test/`
2. Use o `AppiumHelper` para manter consistência
3. Documente novos comandos migrados
4. Execute todos os testes antes de fazer commit

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique a documentação do Appium
2. Execute `npm run doctor` para diagnóstico
3. Consulte os logs na pasta `logs/`
