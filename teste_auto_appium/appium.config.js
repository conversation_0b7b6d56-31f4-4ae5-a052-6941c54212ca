const path = require('path');

const config = {
  // Configurações do servidor Appium
  server: {
    host: 'localhost',
    port: 4723,
    path: '/wd/hub'
  },

  // Configurações para iOS
  ios: {
    platformName: 'iOS',
    platformVersion: '16.0',
    deviceName: 'iPhone 14',
    bundleId: 'br.com.pactosolucoes.treino.ios',
    automationName: 'XCUITest',
    noReset: false,
    fullReset: false,
    newCommandTimeout: 300,
    commandTimeouts: 300000,
    wdaLaunchTimeout: 300000,
    wdaConnectionTimeout: 300000
  },

  // Configurações para Android
  android: {
    platformName: 'Android',
    platformVersion: '12.0',
    deviceName: 'Android Emulator',
    appPackage: 'br.com.pactosolucoes.treino',
    appActivity: '.MainActivity',
    automationName: 'UiAutomator2',
    noReset: false,
    fullReset: false,
    newCommandTimeout: 300
  },

  // Configurações de timeout
  timeouts: {
    implicit: 10000,
    pageLoad: 30000,
    script: 30000
  },

  // Configurações de retry
  retry: {
    attempts: 3,
    delay: 2000
  }
};

module.exports = config;
