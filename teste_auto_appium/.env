# Configurações de ambiente para testes Appium

# Plataforma (ios ou android)
PLATFORM=ios

# Configurações do servidor Appium
APPIUM_HOST=localhost
APPIUM_PORT=4723

# Configurações iOS
IOS_PLATFORM_VERSION=16.0
IOS_DEVICE_NAME=iPhone 14
IOS_BUNDLE_ID=br.com.pactosolucoes.treino.ios

# Configurações Android
ANDROID_PLATFORM_VERSION=12.0
ANDROID_DEVICE_NAME=Android Emulator
ANDROID_APP_PACKAGE=br.com.pactosolucoes.treino
ANDROID_APP_ACTIVITY=.MainActivity

# Timeouts (em milissegundos)
IMPLICIT_TIMEOUT=10000
PAGE_LOAD_TIMEOUT=30000
SCRIPT_TIMEOUT=30000

# Configurações de retry
RETRY_ATTEMPTS=3
RETRY_DELAY=2000

# Dados de teste
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=123
TEST_GYM_NAME=Revisao
