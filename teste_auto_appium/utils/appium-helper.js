const { remote } = require('webdriverio');
const config = require('../appium.config');

class AppiumHelper {
  constructor() {
    this.driver = null;
    this.platform = process.env.PLATFORM || 'ios';
  }

  async startDriver() {
    const capabilities = this.platform === 'ios' ? config.ios : config.android;
    
    this.driver = await remote({
      protocol: 'http',
      hostname: config.server.host,
      port: config.server.port,
      path: config.server.path,
      capabilities
    });

    // Configurar timeouts
    await this.driver.setTimeout({
      implicit: config.timeouts.implicit,
      pageLoad: config.timeouts.pageLoad,
      script: config.timeouts.script
    });

    return this.driver;
  }

  async stopDriver() {
    if (this.driver) {
      await this.driver.deleteSession();
      this.driver = null;
    }
  }

  // Equivalente ao tapOn do Maestro
  async tapOn(selector, options = {}) {
    const element = await this.findElement(selector);
    await element.click();
    
    if (options.waitAfter) {
      await this.driver.pause(options.waitAfter);
    }
  }

  // Equivalente ao inputText do Maestro
  async inputText(text, selector = null) {
    if (selector) {
      const element = await this.findElement(selector);
      await element.setValue(text);
    } else {
      // Se não há seletor, assume que o elemento já está focado
      await this.driver.keys(text);
    }
  }

  // Equivalente ao waitForAnimationToEnd do Maestro
  async waitForAnimationToEnd(timeout = 5000) {
    await this.driver.pause(timeout);
  }

  // Método auxiliar para encontrar elementos
  async findElement(selector) {
    let element;
    
    if (typeof selector === 'string') {
      // Tenta encontrar por texto primeiro
      try {
        if (this.platform === 'ios') {
          element = await this.driver.$(`~${selector}`);
          if (await element.isExisting()) return element;
          
          element = await this.driver.$(`//XCUIElementTypeButton[@name="${selector}"]`);
          if (await element.isExisting()) return element;
          
          element = await this.driver.$(`//XCUIElementTypeStaticText[contains(@name,"${selector}")]`);
          if (await element.isExisting()) return element;
        } else {
          element = await this.driver.$(`//*[@text="${selector}"]`);
          if (await element.isExisting()) return element;
          
          element = await this.driver.$(`//*[contains(@text,"${selector}")]`);
          if (await element.isExisting()) return element;
        }
      } catch (error) {
        console.log(`Elemento não encontrado por texto: ${selector}`);
      }
    } else if (selector.id) {
      // Busca por ID
      if (this.platform === 'ios') {
        element = await this.driver.$(`~${selector.id}`);
      } else {
        element = await this.driver.$(`//*[@resource-id="${selector.id}"]`);
      }
    } else if (selector.point) {
      // Busca por coordenadas (converte porcentagem para pixels)
      const [x, y] = selector.point.split(',').map(coord => {
        const percentage = parseFloat(coord.replace('%', ''));
        return percentage;
      });
      
      const windowSize = await this.driver.getWindowSize();
      const pixelX = Math.round((windowSize.width * x) / 100);
      const pixelY = Math.round((windowSize.height * y) / 100);
      
      await this.driver.touchAction({
        action: 'tap',
        x: pixelX,
        y: pixelY
      });
      return null; // Não retorna elemento para coordenadas
    }

    if (!element || !(await element.isExisting())) {
      throw new Error(`Elemento não encontrado: ${JSON.stringify(selector)}`);
    }

    return element;
  }

  // Método para aguardar elemento aparecer
  async waitForElement(selector, timeout = 10000) {
    const element = await this.findElement(selector);
    await element.waitForExist({ timeout });
    return element;
  }

  // Método para verificar se elemento existe
  async elementExists(selector) {
    try {
      const element = await this.findElement(selector);
      return await element.isExisting();
    } catch {
      return false;
    }
  }
}

module.exports = AppiumHelper;
