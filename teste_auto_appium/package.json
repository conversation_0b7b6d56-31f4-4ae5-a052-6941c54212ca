{"name": "teste-auto-appium", "version": "1.0.0", "description": "POC para migração de testes do Maestro para Appium", "main": "index.js", "scripts": {"test": "mocha test/**/*.js --timeout 60000", "test:login": "mocha test/login/**/*.js --timeout 60000", "test:android": "PLATFORM=android npm test", "test:ios": "PLATFORM=ios npm test"}, "keywords": ["appium", "mobile", "testing", "automation"], "author": "Equipe de QA", "license": "MIT", "devDependencies": {"appium": "^2.0.0", "appium-doctor": "^1.16.2", "@appium/driver": "^2.0.0", "@appium/xcuitest-driver": "^5.0.0", "@appium/uiautomator2-driver": "^3.0.0", "webdriverio": "^8.0.0", "mocha": "^10.0.0", "chai": "^4.3.0", "allure-mocha": "^2.0.0", "allure-commandline": "^2.0.0"}, "dependencies": {"dotenv": "^16.0.0"}}