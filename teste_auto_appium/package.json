{"name": "teste-auto-appium", "version": "1.0.0", "description": "POC para migração de testes do Maestro para Appium", "main": "index.js", "scripts": {"setup": "node scripts/setup.js", "start-appium": "node scripts/start-appium.js", "test": "mocha test/**/*.js --timeout 60000", "test:login": "mocha test/login/**/*.js --timeout 60000", "test:android": "cross-env PLATFORM=android npm test", "test:ios": "cross-env PLATFORM=ios npm test", "test:android:login": "cross-env PLATFORM=android npm run test:login", "test:ios:login": "cross-env PLATFORM=ios npm run test:login", "doctor": "appium-doctor", "clean": "rimraf reports screenshots logs"}, "keywords": ["appium", "mobile", "testing", "automation"], "author": "Equipe de QA", "license": "MIT", "devDependencies": {"appium": "^2.0.0", "appium-doctor": "^1.16.2", "@appium/driver": "^2.0.0", "@appium/xcuitest-driver": "^5.0.0", "@appium/uiautomator2-driver": "^3.0.0", "webdriverio": "^8.0.0", "mocha": "^10.0.0", "chai": "^4.3.0", "allure-mocha": "^2.0.0", "allure-commandline": "^2.0.0", "cross-env": "^7.0.3", "rimraf": "^5.0.0"}, "dependencies": {"dotenv": "^16.0.0"}}