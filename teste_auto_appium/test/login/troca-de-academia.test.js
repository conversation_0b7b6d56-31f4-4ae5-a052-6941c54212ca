const { expect } = require('chai');
const AppiumHelper = require('../../utils/appium-helper');
require('dotenv').config();

describe('Login - Troca de Academia', function() {
  let appiumHelper;
  let driver;

  before(async function() {
    this.timeout(60000);
    appiumHelper = new AppiumHelper();
    driver = await appiumHelper.startDriver();
  });

  after(async function() {
    this.timeout(30000);
    if (appiumHelper) {
      await appiumHelper.stopDriver();
    }
  });

  describe('Trocar Academia', function() {
    it('Deve trocar de academia com sucesso', async function() {
      this.timeout(60000);

      try {
        // Migração do script trocar_academia.yaml
        
        // - tapOn: id: "bnt_trocar_academia"
        await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
        
        // - tapOn: "Swap gym"
        await appiumHelper.tapOn('Swap gym');
        
        // - tapOn: "Select your gym"
        await appiumHelper.tapOn('Select your gym');

        console.log('Troca de academia iniciada com sucesso!');
        
      } catch (error) {
        console.error('Erro durante a troca de academia:', error);
        
        // Captura screenshot em caso de erro
        try {
          const screenshot = await driver.takeScreenshot();
          console.log('Screenshot capturada em caso de erro');
        } catch (screenshotError) {
          console.error('Erro ao capturar screenshot:', screenshotError);
        }
        
        throw error;
      }
    });
  });

  describe('Selecionar Academia', function() {
    it('Deve selecionar uma academia específica', async function() {
      this.timeout(60000);

      try {
        // Migração do script selecionar_academia.yaml
        
        // - tapOn: id: "inserir_nome_academia"
        await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
        
        // - inputText: "Revisao"
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        
        // - tapOn: point: "50%,32%"
        await appiumHelper.tapOn({ point: '50%,32%' });
        
        // - tapOn: "Your fitness journey begins now!"
        await appiumHelper.tapOn('Your fitness journey begins now!');

        console.log('Academia selecionada com sucesso!');
        
      } catch (error) {
        console.error('Erro durante a seleção de academia:', error);
        
        // Captura screenshot em caso de erro
        try {
          const screenshot = await driver.takeScreenshot();
          console.log('Screenshot capturada em caso de erro');
        } catch (screenshotError) {
          console.error('Erro ao capturar screenshot:', screenshotError);
        }
        
        throw error;
      }
    });
  });

  describe('Fluxo Completo - Troca e Seleção', function() {
    it('Deve executar o fluxo completo de troca e seleção de academia', async function() {
      this.timeout(120000);

      try {
        // Executa primeiro a troca
        await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
        await appiumHelper.tapOn('Swap gym');
        await appiumHelper.tapOn('Select your gym');
        
        // Aguarda um pouco entre as ações
        await driver.pause(2000);
        
        // Executa a seleção
        await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        await appiumHelper.tapOn({ point: '50%,32%' });
        await appiumHelper.tapOn('Your fitness journey begins now!');

        console.log('Fluxo completo de troca de academia executado com sucesso!');
        
      } catch (error) {
        console.error('Erro durante o fluxo completo:', error);
        
        // Captura screenshot em caso de erro
        try {
          const screenshot = await driver.takeScreenshot();
          console.log('Screenshot capturada em caso de erro');
        } catch (screenshotError) {
          console.error('Erro ao capturar screenshot:', screenshotError);
        }
        
        throw error;
      }
    });
  });
});
