const { expect } = require('chai');
const AppiumHelper = require('../../utils/appium-helper');
require('dotenv').config();

describe('Login - Aluno', function() {
  let appiumHelper;
  let driver;

  before(async function() {
    this.timeout(60000);
    appiumHelper = new AppiumHelper();
    driver = await appiumHelper.startDriver();
  });

  after(async function() {
    this.timeout(30000);
    if (appiumHelper) {
      await appiumHelper.stopDriver();
    }
  });

  it('Deve realizar login como aluno com sucesso', async function() {
    this.timeout(120000);

    try {
      // Migração do script Maestro login_only_aluno.yaml
      
      // - tapOn: Continue
      await appiumHelper.tapOn('Continue');
      
      // - tapOn: Look for your gym
      await appiumHelper.tapOn('Look for your gym');
      
      // - inputText: Revisao
      await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
      
      // - tapOn: Revisão
      await appiumHelper.tapOn('Revisão');
      
      // - tapOn: Login with user
      await appiumHelper.tapOn('Login with user');
      
      // - tapOn: Enter your email or username
      await appiumHelper.tapOn('Enter your email or username');
      
      // - inputText: <EMAIL>
      await appiumHelper.inputText(process.env.TEST_EMAIL || '<EMAIL>');
      
      // - tapOn: Continue
      await appiumHelper.tapOn('Continue');
      
      // - tapOn: Enter password
      await appiumHelper.tapOn('Enter password');
      
      // - tapOn: Password
      await appiumHelper.tapOn('Password');
      
      // - inputText: 123
      await appiumHelper.inputText(process.env.TEST_PASSWORD || '123');
      
      // - tapOn: Confirm
      await appiumHelper.tapOn('Confirm');
      
      // - waitForAnimationToEnd: timeout: 5000
      await appiumHelper.waitForAnimationToEnd(5000);
      
      // - tapOn: Accept
      await appiumHelper.tapOn('Accept');

      // Verificação de sucesso do login
      // Aguarda um elemento que indica que o login foi bem-sucedido
      await driver.pause(3000);
      
      console.log('Login realizado com sucesso!');
      
    } catch (error) {
      console.error('Erro durante o teste de login:', error);
      
      // Captura screenshot em caso de erro
      try {
        const screenshot = await driver.takeScreenshot();
        console.log('Screenshot capturada em caso de erro');
        // Aqui você pode salvar o screenshot se necessário
      } catch (screenshotError) {
        console.error('Erro ao capturar screenshot:', screenshotError);
      }
      
      throw error;
    }
  });
});
