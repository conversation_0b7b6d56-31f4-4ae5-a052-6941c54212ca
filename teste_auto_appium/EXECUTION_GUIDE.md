# Guia de Execução - Testes Appium

Este guia fornece instruções detalhadas para executar os testes migrados do Maestro para Appium.

## 🚀 Início Rápido

### 1. Configuração Inicial (Primeira Execução)
```bash
cd teste_auto_appium
npm run setup
```

### 2. Configurar Ambiente
Edite o arquivo `.env`:
```env
PLATFORM=ios  # ou android
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=sua-senha
TEST_GYM_NAME=Nome-da-Academia
```

### 3. Iniciar Appium (Terminal 1)
```bash
npm run start-appium
```

### 4. Executar Testes (Terminal 2)
```bash
npm run test:login
```

## 📱 Configuração por Plataforma

### iOS

#### Pré-requisitos
- macOS
- Xcode instalado
- Simulador iOS configurado

#### Configuração
1. Abrir Xcode e aceitar licenças
2. Instalar Command Line Tools:
   ```bash
   xcode-select --install
   ```
3. Verificar simuladores disponíveis:
   ```bash
   xcrun simctl list devices
   ```

#### Executar Testes iOS
```bash
npm run test:ios:login
```

### Android

#### Pré-requisitos
- Android Studio
- SDK Android
- Emulador ou dispositivo físico

#### Configuração
1. Configurar variáveis de ambiente:
   ```bash
   export ANDROID_HOME=/path/to/android/sdk
   export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
   ```

2. Verificar dispositivos conectados:
   ```bash
   adb devices
   ```

3. Iniciar emulador (se necessário):
   ```bash
   emulator -avd NomeDoEmulador
   ```

#### Executar Testes Android
```bash
npm run test:android:login
```

## 🔧 Comandos Disponíveis

### Setup e Configuração
```bash
npm run setup          # Configuração inicial completa
npm run doctor          # Verificar saúde do ambiente
npm run start-appium    # Iniciar servidor Appium
npm run clean          # Limpar relatórios e screenshots
```

### Execução de Testes
```bash
npm test               # Todos os testes
npm run test:login     # Apenas testes de login
npm run test:ios       # Todos os testes no iOS
npm run test:android   # Todos os testes no Android
npm run test:ios:login # Testes de login no iOS
npm run test:android:login # Testes de login no Android
```

## 📊 Interpretando Resultados

### Saída de Sucesso
```
  Login - Aluno
    ✓ Deve realizar login como aluno com sucesso (45123ms)

  1 passing (45s)
```

### Saída de Erro
```
  Login - Aluno
    1) Deve realizar login como aluno com sucesso

  0 passing (30s)
  1 failing

  1) Login - Aluno
     Deve realizar login como aluno com sucesso:
     Error: Elemento não encontrado: "Continue"
```

### Arquivos Gerados
- **Reports**: `reports/` - Relatórios detalhados
- **Screenshots**: `screenshots/` - Capturas de erro
- **Logs**: `logs/` - Logs de execução

## 🐛 Solução de Problemas

### Problema: Appium não inicia
**Sintomas**: Erro ao executar `npm run start-appium`
**Soluções**:
```bash
# Verificar instalação
appium --version

# Reinstalar se necessário
npm install -g appium@next

# Verificar drivers
appium driver list
```

### Problema: Elemento não encontrado
**Sintomas**: `Error: Elemento não encontrado`
**Soluções**:
1. Usar Appium Inspector para encontrar seletores corretos
2. Verificar se app está na tela correta
3. Adicionar waits antes de buscar elemento
4. Verificar se texto está correto (case-sensitive)

### Problema: Timeout nos testes
**Sintomas**: Testes falhando por timeout
**Soluções**:
1. Aumentar timeout no teste:
   ```javascript
   this.timeout(120000); // 2 minutos
   ```
2. Verificar se dispositivo/emulador está respondendo
3. Otimizar waits no código

### Problema: Dispositivo não encontrado
**Sintomas**: `No devices found`
**Soluções**:

#### iOS
```bash
# Listar simuladores
xcrun simctl list devices

# Iniciar simulador específico
xcrun simctl boot "iPhone 14"
```

#### Android
```bash
# Listar dispositivos
adb devices

# Reiniciar ADB se necessário
adb kill-server
adb start-server
```

## 📋 Checklist Pré-Execução

### ✅ Ambiente
- [ ] Node.js instalado (v16+)
- [ ] Appium instalado globalmente
- [ ] Drivers instalados (xcuitest/uiautomator2)
- [ ] Dependências do projeto instaladas

### ✅ Dispositivo/Emulador
- [ ] Dispositivo conectado ou emulador iniciado
- [ ] App instalado no dispositivo
- [ ] Dispositivo desbloqueado
- [ ] Configurações de desenvolvedor habilitadas (Android)

### ✅ Configuração
- [ ] Arquivo `.env` configurado
- [ ] Configurações do `appium.config.js` ajustadas
- [ ] Servidor Appium iniciado

## 🔍 Debug e Troubleshooting

### Logs Detalhados
Para debug mais detalhado, inicie o Appium com logs verbose:
```bash
appium --log-level debug --log-timestamp
```

### Appium Inspector
1. Baixar: https://github.com/appium/appium-inspector
2. Conectar ao servidor Appium
3. Usar para encontrar elementos

### Screenshots de Erro
Screenshots são automaticamente capturadas em caso de erro e salvos em `screenshots/`.

### Logs de Execução
Logs detalhados são salvos em `logs/` para análise posterior.

## 📈 Otimização de Performance

### Reduzir Timeouts
```javascript
// Em vez de waits fixos
await driver.pause(5000);

// Use waits específicos
await appiumHelper.waitForElement('elemento');
```

### Reutilizar Sessões
Para testes rápidos, configure `noReset: true` no `appium.config.js`.

### Paralelização
Para executar testes em paralelo:
```bash
npm install --save-dev mocha-parallel-tests
```

## 📞 Suporte e Recursos

### Documentação
- [Appium Docs](https://appium.io/docs/en/2.0/)
- [WebDriverIO API](https://webdriver.io/docs/api)
- [Mocha Framework](https://mochajs.org/)

### Ferramentas Úteis
- **Appium Inspector**: Para encontrar elementos
- **Appium Doctor**: Para verificar configuração
- **ADB**: Para debug Android
- **Simulator**: Para iOS

### Comunidade
- [Appium GitHub](https://github.com/appium/appium)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/appium)
- [Appium Discuss](https://discuss.appium.io/)

## 💡 Dicas de Execução

1. **Sempre inicie o Appium antes dos testes**
2. **Use um dispositivo/emulador dedicado para testes**
3. **Mantenha o app atualizado no dispositivo**
4. **Execute testes em ambiente estável (rede, etc.)**
5. **Monitore logs para identificar problemas rapidamente**
6. **Use screenshots para debug visual**
7. **Mantenha timeouts realistas mas generosos**
