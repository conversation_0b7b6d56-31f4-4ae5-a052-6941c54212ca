#!/usr/bin/env node

const { spawn } = require('child_process');
const config = require('../appium.config');

console.log('🚀 Iniciando servidor Appium...\n');

const appiumArgs = [
  '--address', config.server.host,
  '--port', config.server.port.toString(),
  '--base-path', config.server.path,
  '--log-level', 'info',
  '--log-timestamp',
  '--local-timezone'
];

console.log(`📍 Servidor será iniciado em: http://${config.server.host}:${config.server.port}${config.server.path}`);
console.log(`🔧 Argumentos: ${appiumArgs.join(' ')}\n`);

const appiumProcess = spawn('appium', appiumArgs, {
  stdio: 'inherit'
});

appiumProcess.on('error', (error) => {
  console.error('❌ Erro ao iniciar Appium:', error.message);
  console.log('\n💡 Certifique-se de que o Appium está instalado:');
  console.log('   npm install -g appium@next');
  process.exit(1);
});

appiumProcess.on('close', (code) => {
  console.log(`\n🛑 Servidor Appium encerrado com código: ${code}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Encerrando servidor Appium...');
  appiumProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Encerrando servidor Appium...');
  appiumProcess.kill('SIGTERM');
});
