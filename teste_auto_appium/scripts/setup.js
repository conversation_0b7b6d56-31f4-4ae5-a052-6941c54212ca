#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Configurando ambiente de testes Appium...\n');

// Verificar se o Node.js está instalado
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`✅ Node.js encontrado: ${nodeVersion}`);
} catch (error) {
  console.error('❌ Node.js não encontrado. Por favor, instale o Node.js primeiro.');
  process.exit(1);
}

// Verificar se o npm está instalado
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`✅ npm encontrado: ${npmVersion}`);
} catch (error) {
  console.error('❌ npm não encontrado. Por favor, instale o npm primeiro.');
  process.exit(1);
}

// Instalar dependências
console.log('\n📦 Instalando dependências...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependências instaladas com sucesso!');
} catch (error) {
  console.error('❌ Erro ao instalar dependências:', error.message);
  process.exit(1);
}

// Verificar se o Appium está instalado globalmente
console.log('\n🔍 Verificando instalação do Appium...');
try {
  const appiumVersion = execSync('appium --version', { encoding: 'utf8' }).trim();
  console.log(`✅ Appium encontrado: ${appiumVersion}`);
} catch (error) {
  console.log('⚠️  Appium não encontrado globalmente. Instalando...');
  try {
    execSync('npm install -g appium@next', { stdio: 'inherit' });
    console.log('✅ Appium instalado com sucesso!');
  } catch (installError) {
    console.error('❌ Erro ao instalar Appium:', installError.message);
    console.log('💡 Tente instalar manualmente: npm install -g appium@next');
  }
}

// Instalar drivers do Appium
console.log('\n🔧 Instalando drivers do Appium...');
try {
  console.log('Instalando driver iOS (XCUITest)...');
  execSync('appium driver install xcuitest', { stdio: 'inherit' });
  console.log('✅ Driver iOS instalado!');
} catch (error) {
  console.log('⚠️  Erro ao instalar driver iOS:', error.message);
}

try {
  console.log('Instalando driver Android (UiAutomator2)...');
  execSync('appium driver install uiautomator2', { stdio: 'inherit' });
  console.log('✅ Driver Android instalado!');
} catch (error) {
  console.log('⚠️  Erro ao instalar driver Android:', error.message);
}

// Criar diretórios necessários
console.log('\n📁 Criando estrutura de diretórios...');
const directories = [
  'reports',
  'screenshots',
  'apps',
  'logs'
];

directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Diretório criado: ${dir}`);
  } else {
    console.log(`✅ Diretório já existe: ${dir}`);
  }
});

// Verificar configuração com appium-doctor
console.log('\n🩺 Executando diagnóstico do Appium...');
try {
  execSync('appium-doctor', { stdio: 'inherit' });
} catch (error) {
  console.log('⚠️  appium-doctor não encontrado ou erro na execução');
  console.log('💡 Instale com: npm install -g appium-doctor');
}

console.log('\n🎉 Setup concluído!');
console.log('\n📋 Próximos passos:');
console.log('1. Configure seu dispositivo/emulador');
console.log('2. Ajuste as configurações no arquivo .env');
console.log('3. Execute os testes com: npm test');
console.log('\n💡 Para executar apenas testes de login: npm run test:login');
console.log('💡 Para executar no Android: npm run test:android');
console.log('💡 Para executar no iOS: npm run test:ios');
