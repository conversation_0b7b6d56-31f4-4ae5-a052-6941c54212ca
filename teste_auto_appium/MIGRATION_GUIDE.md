# Guia de Migração: Maestro → Appium

Este documento detalha o processo de migração dos testes do Maestro para o Appium.

## 🔄 Diferenças Principais

### Maestro vs Appium

| Aspecto | Maestro | Appium |
|---------|---------|--------|
| **Linguagem** | YAML | JavaScript/TypeScript |
| **Configuração** | Simples (YAML) | Mais complexa (JSON/JS) |
| **Seletores** | Texto, ID, Coordenadas | XPath, ID, Accessibility ID, etc. |
| **Execução** | CLI direto | Servidor + Cliente |
| **Debugging** | Limitado | Extensivo |
| **Relatórios** | Básico | Avançado (Allure, etc.) |

## 📋 Mapeamento de Comandos

### 1. Toque em Elementos

#### Maestro
```yaml
- tapOn: "Continue"
- tapOn:
    id: "button_id"
- tapOn:
    point: "50%,30%"
```

#### Appium
```javascript
await appiumHelper.tapOn('Continue');
await appiumHelper.tapOn({id: 'button_id'});
await appiumHelper.tapOn({point: '50%,30%'});
```

### 2. Inserção de Texto

#### Maestro
```yaml
- inputText: "texto aqui"
```

#### Appium
```javascript
await appiumHelper.inputText('texto aqui');
```

### 3. Esperas

#### Maestro
```yaml
- waitForAnimationToEnd:
    timeout: 5000
```

#### Appium
```javascript
await appiumHelper.waitForAnimationToEnd(5000);
```

## 🛠️ Processo de Migração

### Passo 1: Análise do Script Maestro
1. Identifique todos os comandos utilizados
2. Mapeie os seletores (texto, ID, coordenadas)
3. Note os timeouts e esperas
4. Identifique fluxos condicionais

### Passo 2: Criação do Teste Appium
1. Crie arquivo `.test.js` correspondente
2. Configure setup e teardown
3. Migre comandos um por um
4. Adicione verificações de sucesso

### Passo 3: Validação
1. Execute o teste migrado
2. Compare comportamento com Maestro
3. Ajuste seletores se necessário
4. Otimize timeouts

## 📝 Exemplo Prático de Migração

### Script Maestro Original
```yaml
# login_only_aluno.yaml
appId: br.com.pactosolucoes.treino.ios
---
- tapOn: Continue
- tapOn: Look for your gym
- inputText: Revisao
- tapOn: Revisão
- tapOn: Login with user
- tapOn: Enter your email or username
- inputText: <EMAIL>
- tapOn: Continue
- waitForAnimationToEnd:
    timeout: 5000
- tapOn: Accept
```

### Script Appium Migrado
```javascript
// login-only-aluno.test.js
const { expect } = require('chai');
const AppiumHelper = require('../../utils/appium-helper');

describe('Login - Aluno', function() {
  let appiumHelper;

  before(async function() {
    appiumHelper = new AppiumHelper();
    await appiumHelper.startDriver();
  });

  after(async function() {
    await appiumHelper.stopDriver();
  });

  it('Deve realizar login como aluno', async function() {
    await appiumHelper.tapOn('Continue');
    await appiumHelper.tapOn('Look for your gym');
    await appiumHelper.inputText('Revisao');
    await appiumHelper.tapOn('Revisão');
    await appiumHelper.tapOn('Login with user');
    await appiumHelper.tapOn('Enter your email or username');
    await appiumHelper.inputText('<EMAIL>');
    await appiumHelper.tapOn('Continue');
    await appiumHelper.waitForAnimationToEnd(5000);
    await appiumHelper.tapOn('Accept');
  });
});
```

## 🎯 Boas Práticas

### 1. Estrutura de Testes
- Um arquivo de teste por funcionalidade
- Métodos `before` e `after` para setup/cleanup
- Timeouts adequados para cada teste
- Tratamento de erros com try/catch

### 2. Seletores Robustos
```javascript
// Preferir IDs únicos
await appiumHelper.tapOn({id: 'unique_button_id'});

// Fallback para texto quando ID não disponível
await appiumHelper.tapOn('Button Text');

// Coordenadas como último recurso
await appiumHelper.tapOn({point: '50%,30%'});
```

### 3. Esperas Inteligentes
```javascript
// Aguardar elemento aparecer
await appiumHelper.waitForElement('Loading Complete');

// Aguardar animação
await appiumHelper.waitForAnimationToEnd(3000);

// Pausa simples (usar com moderação)
await driver.pause(1000);
```

### 4. Tratamento de Erros
```javascript
try {
  await appiumHelper.tapOn('Optional Button');
} catch (error) {
  console.log('Botão opcional não encontrado, continuando...');
}
```

## 🔧 Configurações Específicas

### iOS
```javascript
// appium.config.js - seção iOS
ios: {
  platformName: 'iOS',
  bundleId: 'br.com.pactosolucoes.treino.ios',
  automationName: 'XCUITest',
  // Configurações específicas do iOS
}
```

### Android
```javascript
// appium.config.js - seção Android
android: {
  platformName: 'Android',
  appPackage: 'br.com.pactosolucoes.treino',
  appActivity: '.MainActivity',
  automationName: 'UiAutomator2',
  // Configurações específicas do Android
}
```

## 🚨 Problemas Comuns e Soluções

### 1. Elemento não encontrado
**Problema**: Seletor do Maestro não funciona no Appium
**Solução**: 
- Usar Appium Inspector para encontrar seletores corretos
- Implementar múltiplas estratégias de busca
- Adicionar waits antes de buscar elementos

### 2. Timeouts
**Problema**: Testes falhando por timeout
**Solução**:
- Aumentar timeouts nos testes
- Usar waits explícitos em vez de implícitos
- Aguardar elementos específicos em vez de tempo fixo

### 3. Coordenadas
**Problema**: Coordenadas por porcentagem não funcionam
**Solução**:
- Converter para pixels baseado no tamanho da tela
- Usar seletores mais robustos quando possível
- Testar em diferentes resoluções

## 📊 Checklist de Migração

### ✅ Antes da Migração
- [ ] Analisar script Maestro original
- [ ] Identificar todos os comandos utilizados
- [ ] Mapear seletores necessários
- [ ] Definir dados de teste

### ✅ Durante a Migração
- [ ] Criar estrutura do teste Appium
- [ ] Migrar comandos sequencialmente
- [ ] Adicionar tratamento de erros
- [ ] Implementar verificações de sucesso

### ✅ Após a Migração
- [ ] Executar teste migrado
- [ ] Comparar com comportamento Maestro
- [ ] Otimizar performance
- [ ] Documentar diferenças encontradas

## 🎓 Recursos Adicionais

- [Documentação Oficial Appium](https://appium.io/docs/en/2.0/)
- [WebDriverIO Docs](https://webdriver.io/)
- [Appium Inspector](https://github.com/appium/appium-inspector)
- [Mocha Testing Framework](https://mochajs.org/)

## 💡 Dicas Finais

1. **Comece simples**: Migre testes básicos primeiro
2. **Use o Inspector**: Ferramenta essencial para encontrar elementos
3. **Teste incrementalmente**: Execute após cada comando migrado
4. **Documente diferenças**: Anote comportamentos diferentes do Maestro
5. **Mantenha flexibilidade**: Implemente fallbacks para seletores
