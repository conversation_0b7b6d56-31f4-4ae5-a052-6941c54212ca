# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
# include: package:flutter_lints/flutter.yaml
analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.

  # Se precisar corrigir todos os Problems (Warnings) basta rodar o comando:
  # dart fix --dry-run 
  # e depois o dart fix --apply

  rules:
     avoid_print: true  # Uncomment to disable the `avoid_print` rule
     prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
     always_use_package_imports: true
     avoid_relative_lib_imports: true
     avoid_empty_else: true
     unnecessary_statements: true
     empty_constructor_bodies: true
     empty_statements: true
     prefer_final_in_for_each: true
     avoid_unnecessary_containers: true
     camel_case_types: true
     camel_case_extensions: true
     prefer_adjacent_string_concatenation: true
     prefer_conditional_assignment: true
     prefer_final_fields: true
     exhaustive_cases: true
     curly_braces_in_flow_control_structures: false
     sized_box_for_whitespace: true
     avoid_function_literals_in_foreach_calls: true
     prefer_const_constructors: true
     avoid_types_as_parameter_names: true
     no_duplicate_case_values: true
     no_logic_in_create_state: true

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
#
