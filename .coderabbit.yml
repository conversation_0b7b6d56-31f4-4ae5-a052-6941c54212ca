# Configurações globais do CodeRabbit para projetos em Flutter
language: "pt-BR"
tone_instructions: "Seja direto e foque apenas em problemas críticos que possam causar bugs, falhas de segurança, vazamentos de memória ou problemas de performance. Evite comentários de estilo ou sugestões de refatoração desnecessárias."

# Branches onde a análise deve ser feita durante merges
base_branches:
  - master
  - main
  - development
  - develop

reviews:
  # Perfil chill para revisões menos rigorosas
  profile: "chill"

  # Configurações para reduzir conteúdo desnecessário e focar apenas em potential issues
  high_level_summary: false
  changed_files_summary: false
  sequence_diagrams: false
  poem: false
  suggested_labels: false
  estimate_code_review_effort: false
  assess_linked_issues: false
  related_issues: false
  related_prs: false
  suggested_reviewers: false

  # Desabilitar funcionalidades de finishing touches (🧩 Analysis chain, 🛠️ Refactor suggestion)
  finishing_touches:
    docstrings:
      enabled: false
    unit_tests:
      enabled: false

  # Filtros de caminho para ignorar arquivos gerados e desnecessários
  path_filters:
    - "!**/*.g.dart"
    - "!**/*.freezed.dart"
    - "!**/*.generated.dart"
    - "!**/*.mocks.dart"
    - "!**/l10n/*.dart"
    - "!**/generated_plugin_registrant.dart"
    - "!**/firebase_options.dart"
    - "!build/**"
    - "!.dart_tool/**"
    - "!ios/**"
    - "!android/**"
    - "!web/**"
    - "!macos/**"
    - "!windows/**"
    - "!linux/**"
    - "!.cert/**/*.json"
    - "!.cert/**/*.pem" 
    - "!**/*google-services.json"
    - "!**/*.p12"
    - "!**/strings.xml"  # Se contém tokens do Facebook


  # Instruções específicas para Dart/Flutter
  path_instructions:
    - path: ".cert/**"
      instructions: "Estes arquivos contêm credenciais necessárias para o processo de build automatizado. Ignorar validações de segurança para esta pasta."
    - path: "**/google-services.json"
      instructions: "Arquivo de configuração do Firebase necessário para builds de flavors. Não revisar por questões de segurança."
    - path: "**/*.dart"
      instructions: |
        - Foque APENAS em bugs, problemas de segurança, vazamentos de memória ou incompatibilidades
        - NÃO comente sobre estilo, formatação ou refatorações desnecessárias
        - NÃO sugira melhorias de código que não afetem funcionalidade crítica
        - Consulte pubspec.yaml antes de sugerir mudanças de dependências
        - Ignore arquivos gerados automaticamente pelo build_runner
        - NÃO comente sobre Effective Dart a menos que cause bugs reais
        - Priorize problemas de performance em widgets e controllers MobX
        - Identifique possíveis memory leaks em Streams, Controllers e Listeners
        - Verifique uso correto de dispose() em StatefulWidgets
        - Alerte sobre uso incorreto de context após operações assíncronas

    - path: "**/pubspec.yaml"
      instructions: |
        - Verifique conflitos de versões de dependências
        - Identifique dependências desnecessárias ou duplicadas
        - Alerte sobre versões desatualizadas críticas para segurança

    - path: "**/analysis_options.yaml"
      instructions: |
        - Verifique configurações que podem mascarar bugs
        - Identifique rules muito restritivas que impedem desenvolvimento

  # Configurações de ferramentas para Flutter/Dart
  tools:
    # Apenas ferramentas relevantes para Flutter/Dart
    eslint:
      enabled: false

# Configurações de base de conhecimento
knowledge_base:
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/analysis_options.yaml"
      - "**/pubspec.yaml"
      - "**/README.md"
      - "**/CHANGELOG.md"
  learnings:
    scope: "local"

# Busca na web habilitada para consultar documentação Flutter/Dart
web_search:
  enabled: true
