appId: br.com.pactosolucoes.treino.ios
---

- tapOn: Enter your email or username
- inputText: <EMAIL>
- tapOn: Continue
- tapOn: Enter password
- tapOn: Password
- inputText: 123
- tapOn: Confirm
- waitForAnimationToEnd:
    timeout: 5000
- runFlow:
    when:
      visible: Accept
    commands:
        - tapOn: Accept
- runFlow:   #Se a assinatura de contrato estiver habilitada no App. 
    when:
      visible: Sign contract
    commands:
        - tapOn: Sign contract
- runFlow:
    when:
      visible: Contract terms
    commands:
        - tapOn: Contract terms
- runFlow:
    when:
      visible: Contract
    commands:
        - tapOn: Contract
- runFlow:
    when:
      visible: 10%, 7%
    commands:
        - tapOn: 10%, 7%
- runFlow:
    when:
      visible: Contract signature
    commands:
        - tapOn: Contract signature
- runFlow:
    when:
      visible: 50%, 44%
    commands:
        - tapOn: 50%, 44%
- runFlow:
    when:
      visible: Conclude
    commands:
        - tapOn: Conclude
- runFlow:
    when:
      visible: Finish
    commands:
        - tapOn: Finish
- runFlow:
    when:
      visible: Scrim
    commands:
        - tapOn: Scrim
