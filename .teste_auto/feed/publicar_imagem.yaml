appId: br.com.pactosolucoes.treino.ios
---
#Publicar um post apenas com imagem

- tapOn:
    point: "90%,7%" #Clicar no botão "+" para ir para tela de Nova publicação
- tapOn:
    point: "23%,59%" #Clicar no botão para adicionar uma imagem da galeria
- runFlow:
    when:
      visible: While using the app 
    commands:
        - tapOn: While using the app
- runFlow:
    when:
      visible: Allow 
    commands:
        - tapOn: Allow      
- tapOn:
    id: "com.google.android.documentsui:id/icon_thumb" #Selecionar foto da galeria
- tapOn: "Conclude" #Clicar em concluir na tela "Recortar imagem"
- tapOn:
    point: "89%,7%" #Clicar no botão de publicar post
- tapOn: "Feed"
