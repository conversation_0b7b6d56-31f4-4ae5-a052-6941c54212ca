appId: br.com.pactosolucoes.treino.ios
---

- tapOn: "Share"
- tapOn:
    point: "87%,10%" #Clicar no ícone da câmera
- runFlow:
    when:
      visible: "Ok"
    commands:
        - tapOn: "Ok"
- runFlow:
    when:
      visible: "87%,10%"
    commands:
        - tapOn: "87%,10%" #Clicar no ícone da câmera,  se caso pedir as permissões de câmera
- runFlow:
    when:
      visible: "While using the app"
    commands:
        - tapOn: "While using the app"
- runFlow:
    when:
      visible: "While using the app"
    commands:
        - tapOn: "While using the app"
- runFlow:
    when:
      visible: "Allow"
    commands:
        - tapOn: "Allow"
- tapOn:
    point: "10%,91%" #Clicar para abrir a galeria
- tapOn:
    id: "com.google.android.documentsui:id/icon_thumb"
- tapOn: "Conclude"
