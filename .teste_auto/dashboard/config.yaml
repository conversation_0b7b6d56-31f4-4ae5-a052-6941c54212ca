flows:
  - "**"
executionOrder:
  continueOnFailure: false
  flowsOrder:
     - apresentar_alunos
     - filtrar_aluno_sua_carteira
     - filtrar_aluno_todas_carteiras
     - acessar_perfil_aluno
     - acessar_vencidos
     - acessar_a_vencer
     - acessar_em_dia
     - acessar_alunos_ativos
     - acessar_alunos_inativos
     - acessar_alunos_aVencer
     - acessar_lista
     - buscar_aluno_na_lista
     - buscar_aluno_n_esta_ista

