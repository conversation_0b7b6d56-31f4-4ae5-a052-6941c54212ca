appId: br.com.pactosolucoes.treino.ios
---


- tapOn: "Share"
- tapOn:
    id: "acessar_camera"
- runFlow:
    when:
      visible: "Ok"
    commands:
        - tapOn: "Ok"
- runFlow:
    when:
      visible: "acessar_camera"
    commands:
        - tapOn: "acessar_camera"
- runFlow:
    when:
      visible: "While using the app"
    commands:
        - tapOn: "While using the app"
- runFlow:
    when:
      visible: "While using the app"
    commands:
        - tapOn: "While using the app"
- runFlow:
    when:
      visible: "Allow"
    commands:
        - tapOn: "Allow"
- tapOn:
    id: "acessar_galeria"
- tapOn:
    id: "com.google.android.documentsui:id/icon_thumb"
- tapOn: "Conclude"
