{"resetar_aluno": "Reset student", "alteracao_vencimento_parcela": "When locking the contract, pending installments will have their due date changed, postponing the due date.", "alteracao_vencimento_parcela_aguarde": "This may take a few seconds, please wait...", "nenhum_video_encontrado": "No videos found", "atv_info_01": "Activity Information", "atv_info_02": "More Information", "troca_atv_original_concluir": "Complete editing", "troca_atv_original_sair": "Exit editing", "troca_atv_original": "Original Activity", "troca_atv_0_paratrocar": "No activity to exchange", "troca_atv_disponivel": "Exchange available", "troca_atv_nao_possivel_consultar": "It was not possible to query", "troca_atv_tentar_em_breve": "Try again in a few minutes", "troca_atv_sem_atvs": "No activities available", "troca_atv_descricao_ativo": "If you don't like an exercise, you can change it using the edit button. Substitution is only available for exercises that allow replacement.", "você_ja_possui_compromissos_no_horario": "You already have appointments at: {}", "observacao_wod_obrigatoria": "The 'Wod Description' field is required.", "solucao_blue_01": "Reconnect device", "solucao_blue_02": "Problems with the connection", "solucao_blue_03": "Try again", "solucao_blue_04": "Open your phone's Settings\nGo to Bluetooth\nFind your {} device in the list\nTap \"Forget\" or \"Unpair\"\nRestart the {} device\nReturn to the app and try connecting again", "solucao_blue_05": "Reconnec {}", "solucao_blue_06": "• Check if Bluetooth is on\n• Make sure the device is charged\n• Keep the device close to the cell phone\n• Restart the cell phone if the problem persists", "solucao_blue_07": "Additional tips:", "solucao_blue_08": "Connecting to the scale", "solucao_blue_09": "Collecting data", "solucao_blue_10": "Evaluation completed successfully!", "solucao_blue_11": "We are setting everything up!\nThis may take a few moments.\nPlease wait..", "solucao_blue_12": "We have successfully completed the evaluation!\nNow you can view the results.", "solucao_blue_13": "Collecting data", "solucao_blue_14": "Finish evaluation", "health_screen_evaluations": {"zero": "You have no evaluation on the health screen", "one": "You have {} evaluation on the health screen", "other": "You have {} evaluations on the health screen"}, "conectar_balanca_47": "Invalid height, must be greater than or equal to 1 meter and less than 2.5 meters.", "erro_saude_cpf_2": "CPF already registered in another account", "erro_saude_email_2": "E-mail already registered in another account", "erro_saude_email": "E-mail not informed in your registration", "erro_saude_cpf": "CPF not informed in your registration", "conectar_balanca_46": "Muscle Mass", "conectar_balanca_45": "Visceral Fat", "scale_bf1000_uso_content": "1. Step onto the scale barefoot, positioning your feet correctly on the electrodes (if it's a scale with body composition analysis).\n2. Stand still and wait until the measurement is complete and the results appear on the display.\n3. Check the data in your health app (if connected) or write down the results directly from the scale's display.", "scale_xiaomi_uso_content": "1. Place the scale on a flat, stable surface.\n2. Turn on the scale by gently pressing your foot until it turns on.\n3. Connect the scale to the Mi Fit app on your smartphone.\n4. Step onto the scale barefoot and remain still until the measurement is complete.\n5. Check the results in the app.", "conectar_balanca_44": "New bioimpedance", "conectar_balanca_43": "Invalid height", "conectar_balanca_42": "* Sedentary (1) to Super Active (5)", "conectar_balanca_41": "Training Level", "conectar_balanca_40": "Physical Activity Level", "conectar_balanca_39": "Training data", "conectar_balanca_38": "Physical Data", "treino_quse_pronto_content_b": "The generated workout is awaiting validation from a trainer", "ia_em_breve_entegue": "Will be delivered soon", "professor_ira_validar_treino_ia": "Please wait for a professor to validate your workout and release access for you to start training.", "ia_criando_seu_treino": "Our artificial intelligence is creating your workout, please wait a moment", "treino_quase_pronto": "Your workout is almost ready!", "treino_quse_pronto_content": "The AI-generated workout is awaiting teacher validation, which can take up to", "conectar_balanca_0": "List of compatible devices", "conectar_balanca_1": "Connect the scale", "conectar_balanca_2": "Select scale", "conectar_balanca_5": "Error in the evaluation, please try again. If the device does not appear listed, turn Bluetooth off and on again.", "conectar_balanca_7": "Error: ", "conectar_balanca_8": "Activate the scale and connect to your user", "conectar_balanca_9": "Not identified", "conectar_balanca_10": "No device found.", "conectar_balanca_11": "Please turn on your phone's Bluetooth and make sure the device is on.", "conectar_balanca_12": "View compatible devices", "conectar_balanca_13": "Stream ended.", "conectar_balanca_15": "Authorize Bluetooth access so the phone can connect to the scale", "conectar_balanca_16": "Grant access", "conectar_balanca_18": "Instructions", "conectar_balanca_19": "Instructions for the exam", "conectar_balanca_20": "Wait for the display light to turn on and instruct the student to step on the scale barefoot, without accessories.", "conectar_balanca_21": "Finish bioimpedance", "conectar_balanca_22": "Evaluation completed", "conectar_balanca_23": "Weight", "conectar_balanca_24": "Height", "conectar_balanca_25": "BMI", "conectar_balanca_26": "Lean <PERSON>", "conectar_balanca_27": "Body Fat", "conectar_balanca_28": "Water", "conectar_balanca_29": "BMR", "conectar_balanca_30": "Save Evaluation", "conectar_balanca_31": "Failed to save physical evaluation.", "conectar_balanca_32": "The list of compatible devices is constantly updated, please check if your device is on the list.", "conectar_balanca_33": "Compatible devices:", "conectar_balanca_34": "Beurer BF1000", "conectar_balanca_35": "Xiaomi Scale Composition 2", "conectar_balanca_36": "Got it", "conectar_balanca_37": "Bluetooth Disabled", "recurso_health_nao_disponivel_8": "Feature unavailable for this version of Android.", "recurso_health_nao_disponivel_7": "Download Google Fit", "recurso_health_nao_disponivel_6": "Ok, I understand!", "recurso_health_nao_disponivel_5": "We appreciate your understanding.", "recurso_health_nao_disponivel_4": "2. Consider other ways to track your health indicators, such as the Google Fit app.", "recurso_health_nao_disponivel_3": "1. Check if there is a system update available for your device.", "recurso_health_nao_disponivel_2": "To continue monitoring your health, you can:", "recurso_health_nao_disponivel_1": "Our app uses technologies that are compatible with Android 10 devices or later to provide health indicators accurately and safely. Unfortunately, some older versions of Android don't support these features.", "sem_recurso_health5": ", update your system to Android 10 or above.", "sem_recurso_health4": "Step count", "sem_recurso_health3": "and", "sem_recurso_health2": "Calories burned", "sem_recurso_health1": "To view your indicators of", "acao_permitir": "Allow", "heatlh_content": "To access your health data, you need to allow access to fitness data. Do you want to allow?", "heatlh_titulo": "Permission required", "treino_ia_body_builder": "I'm a bodybuilder or professional athlete", "treino_ia_entre_1_ano_e_dois": "I've been working out for between 1 and 2 years", "treino_ia_menos_6_meses": "I've been working out for less than 6 months", "treino_ia_treino_2_anos": "I've been working out for over 2 years", "treino_ia_treino_6_meses": "I've been working out for between 6 months and 1 year", "treino_ia_estouvoltando": "I'm getting back into working out now", "treino_ia_treinou_voltando": "I used to work out, but I'm getting back into it now", "treino_ia_nunca_treinou": "I've never worked out before", "ask_tempo_de_treino": "How many minutes per day do you have available to workout?", "splash_acao_user_content": "Your user account is outdated. You need to log in again so we can update your data.", "splash_acao_user": "Necessary action", "espere_splash_screen3": "We're still loading, just a second...", "espere_splash_screen2": "Getting everything ready for you...", "espere_splash_screen1": "Almost there, just a moment...", "espere_splash_screen0": "Loading, please wait...", "aval_fisica_sem_altura_content": "Please go to the service desk to update your height.", "aval_fisica_titulo_sem_altura": "Incomplete Evaluation", "splash_preso_recarregar_app": "Reload App", "splash_preso_refazer_login": "Log in again", "splash_preso_mensagem": "Try logging in again", "splash_preso_titulo": "We couldn't load your user", "desativado_cliente_disclaimer": "Your account or gym is currently inactive. For more information or to reactivate your access, please contact the gym reception.", "desativado_cliente_titulo": "The company {} no longer uses Training", "consultar_aval_recarregar": "Reload data", "consulta_aval_erro_mensagem": "To try again, refresh the page by scrolling down or tap the refresh button", "consulta_aval_erro_titulo": "Unable to load physical assessments!", "n2bContent": "Ask at your gym reception how to have online consultations with nutritionists!", "n2b_titulo_rev": "Your evolution awaits you", "n2b_titulo_venda_content": "Have online consultations with nutritionists right here on your app", "n2b_titulo_venda_video": "Book your consultation", "n2b_euquero_cta": "I want", "com_qual_genero": "What is your biological sex?", "marcar_all": "Select all", "desmarcar_all": "Deselect all", "confpushIa_description": "Set up notifications based on your service hours", "confpushIa_titulo": "Customize Notifications", "confpushiaraddvazio": "It is not possible to add groups if you have empty group", "confpushiargrupoatual": "Set your current group first", "confpushiarsemdias": "No days available", "confpushiaremovn1": "By removing snooze you will no longer receive notifications", "confpushiaremov": "Remove snooze period", "horariostr": "Times", "configpushdiasSemana": "Week days", "diastr": "Days", "configpushdiaselecionado": "The day is already selected in another group.", "configpushdia": "The day number should be between 1 and 7.", "copiandotr": "<PERSON>pied", "configpush": "Push configurations", "trocarExercicio": "Swap Exercise", "ia.erroTroca": "The activity could not be swapped.", "ia.tentenovamente": "Try again in a few minutes.", "profIA": "AI Workout", "search": "Search", "welcome": "Welcome", "novoLogin_localizacao_1": "Fix user data", "novoLogin_localizacao_2": "Try another login method", "novoLogin_localizacao_3": "Send single login link", "novoLogin_localizacao_4": "Error sending link", "novoLogin_localizacao_5": "Incorrect password", "novoLogin_localizacao_6": "No user found with this email", "novoLogin_localizacao_7": "Incorrect code", "novoLogin_localizacao_8": "Code expired", "novoLogin_localizacao_9": "User not found", "novoLogin_localizacao_10": "Error sending SMS", "novoLogin_localizacao_11": "There was an error sending the login link, please try again. If the error persists, try another login method.", "novoLogin_localizacao_12": "The entered password is incorrect. Please try again or login by sending a single login link.", "novoLogin_localizacao_13": "The entered email “<EMAIL>” is not linked to any user in our database, please review the email and try again.", "novoLogin_localizacao_14": "The entered code is incorrect. Please review it or try another login method.", "novoLogin_localizacao_15": "The entered code has timed out for confirmation. Please resend the code and try again.", "novoLogin_localizacao_16": "The provided user was not found, please review the entered data and try again.", "novoLogin_localizacao_17": "There was an error sending the authentication code via SMS, please try again. If the error persists, try another login method.", "novoLogin_localizacao_18": "<PERSON><PERSON> sent successfully!", "novoLogin_localizacao_19": "acceptedNewTerms", "novoLogin_localizacao_20": "refUser", "novoLogin_localizacao_21": "debug", "novoLogin_localizacao_22": "The user account has been disabled by an administrator.", "novoLogin_localizacao_23": "Oops!", "novoLogin_localizacao_24": "Login with email", "novoLogin_localizacao_25": "infoUserLogin", "novoLogin_localizacao_28": "Select user", "novoLogin_localizacao_29": "Validation", "novoLogin_localizacao_30": "Login with Apple account", "novoLogin_localizacao_31": "Connect your Apple account to a real email", "novoLogin_localizacao_32": "To continue, it is necessary to link your email to a real email so that we can find your user.", "novoLogin_localizacao_33": "Enter your email", "novoLogin_localizacao_34": "Insert your email", "novoLogin_localizacao_35": "btnEditEmail", "novoLogin_localizacao_36": "btnContinue", "novoLogin_localizacao_37": "Continue with Google", "novoLogin_localizacao_38": "Continue with Facebook", "novoLogin_localizacao_39": "Continue with Apple", "novoLogin_localizacao_40": "Login with user", "novoLogin_localizacao_41": "Oops!", "novoLogin_localizacao_42": "Welcome to {}", "novoLogin_localizacao_43": "Your fitness journey begins now!", "novoLogin_localizacao_44": "Choose your best login option, remember to use the same email address provided to the gym to continue.", "novoLogin_localizacao_45": "Login with user", "novoLogin_localizacao_46": "Have any questions?", "novoLogin_localizacao_47": "Validate the code", "novoLogin_localizacao_48": "Enter the 6-digit code that was sent to the email “{}”", "novoLogin_localizacao_51": "Phone", "novoLogin_localizacao_53": "Enter your email or username", "novoLogin_localizacao_54": "Send code by email", "novoLogin_localizacao_55": "Safer option", "novoLogin_localizacao_56": "Enter password", "novoLogin_localizacao_57": "Via SMS", "novoLogin_localizacao_58": "Code sent", "novoLogin_localizacao_59": "Enter the 6-digit code that was sent to the email “{}” or click the login link.", "novoLogin_localizacao_60": "See all", "novoLogin_localizacao_61": "Help with login", "novoLogin_localizacao_62": "User not found, please review the entered data", "novoLogin_localizacao_63": "No user linked to this email was found", "push.link.titulo": "This notification leads to an external link", "push.link.disclaimer": "By accessing, you will leave the app", "push.link.voltar": "Return to the app", "push.link.acessar": "Access link", "text_not_ative_atz": "Enable notifications and receive updates and tips on your phone!", "text_not_nao_perca_nenhuma_atz": "Don't miss any important updates and stay informed about your business.", "text_not_ative_todas_atz": "Enable all notifications", "saude.aval.detalhes.percentualGordura": "Total Fat Percentage", "Percentual_de_Massa_magra": "Lean mass percentage", "peso_real_desc_abre": "It is considered healthy for your weight to range between {}kg and {}kg", "Historico_de_bioimpedância": "Bioimpedance history", "saudepesoreal": "Real weight", "saudecomparativodepesos": "Weight comparison", "saudeindicemassacorporeaimc": "Body mass index (BMI)", "saudeanalisesegmentar": "Segmental analysis", "saudemassamagra": "Lean body mass", "saudegorduracorporal": "Body fat", "saudeaguacorporal": "Body water", "saudemassaossea": "Bone mass", "desc_comparativo_pesos": "Here, you can compare the percentage of lean mass and fat that make up your total weight. In other words, out of your current weight, how much is fat and how much is lean mass. Still within fat, there's a percentage differentiation between total fat and visceral fat (which deserves more attention, as it accumulates in the abdominal cavity, beneath the muscle layer, and is close to vital organs).", "desc_saudePesoReal": "It is the total accumulation of mass in the body. It consists of fat, lean mass, body water, and bone tissue.", "desc_saudeIndiceDeMassaCorporeaIMC": "The Body Mass Index is calculated by dividing your weight (in kilograms) by your height (in meters) squared. It is used to check if body weight is above or below the ideal. Alone, this index doesn't say much; we need to consider other markers.", "desc_saudeGorduraCorporal": "It is the total amount of fat distributed throughout our body. It is measured in percentage, and the value differs for men and women. Silicone prostheses can be included in this result.", "desc_saudeMassaDeGordura": "It is the total amount, in kilograms, of fat under the skin, visceral, and around muscles. The value differs for men and women. Silicone prostheses can be added to the result.", "desc_saudeMassaMagra": "It is the total amount of muscle mass distributed throughout our body. It is present in cardiac, visceral, and skeletal muscles and is defined in kilograms.", "desc_saudeAguaCorporal": "It is the total amount of water in the body, including the total water inside and outside our cells. It can identify fluid retention or dehydration.", "desc_saudeGorduraVisceral": "It is all the fat that protects our vital organs. When in excess, this fat accumulates in the liver and abdomen.", "desc_saudeAnaliseSegmentar": "Segmental analysis shows how fat and muscle are distributed in the arms, trunk, and legs. It does not include fat or muscle in internal or cardiac organs.", "desc_saudeTaxaMetabolicaBasalTMB": "It is the amount of calories that the body needs to maintain its basic functions, such as respiration and digestion. It is measured in calories per day and, when added to the level of physical activity, helps the nutritionist determine your caloric needs and develop an adequate meal plan.", "descricao_calculadora": "Change the reference weight and see your exercise percentage", "acessar": "Access", "unable_to_launch_whatsapp": "Unable to launch WhatsApp", "mensagem_whatsapp": "Hey, {}. {} here!", "select_your_gym": "Select your gym", "select_your_box": "Select your crossfit box", "search_for_your_gym": "Look for your gym", "unable_to_find_gym": "Your search did not match any results. Try a different keyword.", "gym_units": {"one": "{} unit", "two": "{} units", "many": "{} units", "other": "{} units"}, "login_with_phone_number": "Login with phone number", "continue": "Continue", "advance": "Advance", "submit_form": "Submit form", "no_internet_connection": "You are not connected to the internet", "login_with_email": "Access with email or username", "by_clicking_continue": "By clicking continue you agree with the", "terms_of_service_and_privacy_policy": "\nterms of service and privacy policy", "by_tapping_on_swap": "By tapping on swap, you will be directed to the gym selection screen;", "swap_gym": "Swap gym", "go_back": "Go back", "swap_gym?": "Swap gym?", "got_it": "Got it", "are_you_not_a_member_yet": "Are you not a gym member yet?", "user": "Email or username", "password": "Password", "atention": "Attention", "email_and_password_must_be_fulfilled": "Email and password must be correctly fulfilled", "user_not_found": "User not found. Did you insert the email and password correctly?", "incorrect_data": "Incorrect data!", "or": "OR", "forgot_my_password": "Forgot password", "welcome_back": "Welcome back", "usuario_recente": "Recent users", "login_with_another_user": "Login with another user", "are_you_sure_you_want_to_remove_user": "Are you sure you want to remove user from your cell phone?", "delete": "Delete", "cancel": "Cancel", "remove_user": "Remove user?", "collaborator": "Gym operators", "member": "Member", "WODSTRINGS": "", "no_wod_registered": "No WOD registered", "unfortunately_there_are_no_wods": "Unfortunately there is no WOD \nregistered so far", "your_contract_does_not_allow_you_cross": "Your contract does not allow you to get access to the Cross data. You can request your gym for an update.", "ops_you_cant_get_access_to_cross": "Ops, you can't get access to the Cross", "calendar": "Calendar", "help": "Help", "see_more": "See more", "no_results_for_the_rank": "No results for the rank", "be_the_first_one_to_register": "Be the first one to register your result \nand assure your spot in the rank", "notifications": "Notifications", "there_has_been_an_error_processing_your_notifications": "There has been an error processing your notifications", "pull_down_to_try_again": "Pull down to try again", "there_are_no_notifications_so_far": "There are no notifications so far", "home_training": "Home Training", "home_training_title": "Home Training", "live": "Live", "unable_to_load_home_training": "Unable to load home training", "check_your_internet_connection_pull_down": "Check your internet connection and pull down to try again", "ops_access_denied": "Ops! Access denied", "it_was_not_possible_to_validate_your_situation_with_the_gym": "It was not possible to validate your situationo with the gym. Contact the administratiion to solve this.", "type_the_name_of_the_training": "Type the name of the training", "news": "News", "train_again": "Train again", "where_are_the_videos?": "Where are the videos?", "unable_to_find_any_video_with_the_filters": "Unable to find any video with the filters you selected. How about trying some others?", "we_are_out_of_videos_at_the_moment": "We are out of videos at the moment", "no_videos_available_right_now": "No videos available right now. Check again soon!", "no_class_registered": "Currently, there is no class registered.", "there_has_been_an_error_consulting_live_classes": "There has been an error consulting live classes", "we_are_out_of_live_trainings_right_now": "We are out of live trainings right now", "no_online_training_registered": "No online training registered!", "rank": "Rank", "me": "Me", "workouts_executed": "Workouts executed", "loading": "Loading", "start_right_now": "Start right now", "total_of": "Total of", "total_of_1_workout": "Total of 1 workout", "workouts": "workouts", "with_a_total_of": "With a total of", "access": "Access", "do_you_like_challenges?": "Do you like challenges?", "participate_in_your_gym's_rank": "Participate in your gym's rank. Your workout is now much more impactful!", "follow_your_progress": "Follow your progress!", "and_your_gym_partners'_too": "And your gym partners' too. A great way to motivate each other!", "a_new_rank_every_month": "A new rank every month", "work_out_5_times_and_get_the_bronze_medal": "Work out 5 times and earn the bronze medal; 15 times and earn the silver medal; 25 times and earn the gold one", "did_i_hear_rewards?": "Did I hear rewards?", "your_points_can_be_redeemed_for_rewards": "Your points can be redeemed for rewards. The more you work out, the more points you get!", "my_appointments": "My appointments", "available": "Available appointments", "no_appointments": "No appointments", "no_appointments_available": "No appointments available", "there_has_been_an_error_processing_your_appointments": "There has been an error processing your appointments", "no_appointments_available_so_far": "No appointments available so far", "try_other_dates_toggling_the_days_in_the_calendar_above": "Try other dates toggling the days in the calendar above", "confirmed": "Confirmed", "instructor:": "Instructor: ", "at_time": "From", "cancel_appointment": "Scheduled", "schedule": "Schedule", "by_clicking_confirm_you_will_be_scheduled_to_the_selected_appointment": "By clicking confirm you will be scheduled to the selected appointment.", "would_you_like_to_confirm_the_appointment": "Would you like to confirm the appointment?", "you_can_reschedule_or_cancel_this_appointment": "You can reschedule or cancel this appointment.", "reschedule": "Reschedule", "what_do_you_want_to_do?": "What do you want to do?", "month": "month", "meses": "Months", "week": "week", "confirm": "Confirm", "days_with_available_schedules": "Days with available schedules", "no_schedules_for_this_day": "No schedules for this day", "success": "Success", "appointment_rescheduled": "Appointment rescheduled", "nutrition": "Nutrition", "error": "Error", "today,": "Today,", "stay_in": "Stay in!", "make_your_diet_totally_customized": "Make your diet totally customized!", "choose_what_you_will_eat_when": "Choose what you will eat, when you will eat and how many meals you want to have a day. Based on your choices we will provide you the best option according to your goals.", "step": "Step", "of": "of", "your_plan_is_ready": "Your plan is ready", "see_more_details_of_your_journey": "See more details of your journey.", "is_your_goal": "is your goal.", "keep_up_the_focus_on_your_plan": "Keep up the focus on your plan so you can reach your goals. You can also check exclusive tips clicking “Stay in”!", "it_will_last_30_days": "It will last 30 days!", "enough_time_for_neither_you": "Time for neither you nor your organism get tired of the same meals", "it_only_depends_on_you": "It only depends on you!", "for_it_to_work_it_takes_100": "For it to work it takes a 100% of commitment", "lets_start_off": "Let's start off!", "conclude": "Conclude", "get_my_plan": "Get my plan", "shopping_list": "Shopping list", "my_hydration": "My hydration", "adjust_your_plan": "Adjust your plan", "reminders": "Reminders", "we_need_you_to_set_a_time_gap": "We need you to set the duration so we are able to process the ingredients needed during your plan.", "set_time_duration": "Set duration", "set_period": "Set period", "weekly_shopping_list": "*🛒 Weekly shopping list* \n", "before_getting_access_to_your_shopping_list": "Before getting access to your shopping list", "yuo_need_to_start_off_your_nutritional_plan": "You need to start off your nutritional plan by confirming the recipes we generated for you.", "nutritional_plan": "Nutritional Plan", "begin_plan": "Begin plan", "type_here": "Type here", "modify_quantity": "Modify quantity", "it_has_been_a_while_since_you_last_checked": "It has been a while since you last checked on your shopping list. Would you like to update it?", "update_list": "Update list", "not_now": "Not now", "update_your_list": "Update your list", "water_consumption": "Water \nConsumption", "are_you_sure_you_want_to_leave_without_confirming_the_changes": "Are you sure do you want to leave without confirming the changes you made?", "leave_without_saving_the_changes": "Leave without saving the changes", "cup's_capacity": "Cup's capacity", "daily_goal": "Daily goal", "quantity_sufficient_according_to_your_age": "Quantity sufficient according \nto your age and weight.", "quantity_insufficient_according_to_your_age": "Quantity insufficient according \nto your age and weight.", "reminder": "Reminder", "tea_cup": "Ex.: Tea cup", "disposable_cup": "Ex.: Disposable cup", "small_cup": "Ex.: Small cup", "mug": "Ex.: Mug", "medium_cup": "Ex.: Medium cup", "large_cup": "Ex.: Large cup", "extra_large_cup": "Ex.: Extra large cup", "bottle_of_water": "Ex.: Bottle of water", "squeeze": "Ex.: Squeeze bottle", "copo_duplo_medio": "Customized size", "customized_size": "Customized size", "lose_weight": "Lose weight", "keep_my_weight": "Keep my current weight", "gain_muscle_mass": "Gain muscle mass", "your_current_weight_is": "Your current weight is", "my_goal": "My goal", "select_your_goal": "Select your goal", "are_you_sure_you_want_to_change_your_goal": "Are you sure you want to change your goal? We will have to recalculate your plan.", "will_you_change_your_goal": "Will you change your goal?", "reset_plan": "Reset plan", "your_current_progress_will_be_lost": "Your current progress will be lost and you will have to begin your plan from scratch. Are you sure you want to reset it?", "will_you_reset_your_plan": "Will you reset your plan?", "your_plan_has_been_recalculated": "Your plan has been recalculated to comply with your new objectives.", "let's_go": "Let's go", "objectve_changed_successfully": "Goal changed successfully", "breakfast": "Breakfast", "brunch": "Brunch", "lunch": "Lunch", "afternoon_snack": "Afternoon snack", "dinner": "Dinner", "supper": "Supper", "remember_to_drink_water": "Remember to drink water", "regard_the_cooking_time": "Regard the cooking time", "the_reminder_will_be_sent_to_you": "The reminder will be sent to you regarding the cooking time and the time of your next meal so you can never run late because of them.", "remake": "Remake", "guided_tutorial": "Guided tutorial", "remake_guided_tutorial": "Re<PERSON>ke guided tutorial", "other_informations": "Other informations", "rate_this_help": "Rate this help", "did_we_get_to_help_you_with_your_doubts": "Did we get to help you with your doubts?", "explanatory_video": "Explanatory video", "by_signing_up_for_premium": "By signing up for premium, you get extra features, which will help you boost your results with the workouts and classes from your gym.", "how_to_start_my_nutritional_plan": "How to start my nutritional plan?", "to_start_your_customized_nutritional_plan": "To start your personalized food plan, you must first answer the anamnesis, with your preferences, restrictions and needs. Through it, our app generates a plan with all the meals you need to make and a shopping list with the ingredients for each one. Then just record your meals over the days and monitor their evolution. If you want, you can still exchange the recipe for other options still within your personalized plan.", "i_didn't_like_one_of_the_suggested_recipes": "I didn't like one of the suggested recipes. Can I swap it?", "yes_you_can_after_the_plan_has_been_generated": "Yes, you can! After the plan has been generated you will get to the step of confirming the recipes that are suggested by the app. If one of them does not please you, just click 'I don't like it' to navigate through other option and select those that fit your taste and follow your plan and nutritional needs. And if you want to try new recipes, go to 'More recipes' in the lower part of the Nutrition screen and explore lots of possibilities.", "how_to_register_my_meal": "How to register my meals?", "when_your_plan_has_been_generated_your_meals": "When your plan has been generated, your meals of each part of the day will be shown in the 'My Meals' section. According to the time defined by you, click 'Register' in the current meal. The app will ask if you have followed the recipe suggested by the plan. If you have not, click 'No' and search for the foods you actually ate.", "how_does_the_shopping_list_works": "How does the shopping list works?", "after_your_plan_has_been_generated_and_your_meals_confirmed": "After your plan has been generated and your meals confirmed, you can get access to your shopping list in the upper right icon (︙). It is automatically processed, according to the current meals of your plan and its duration. You can check the items that you have already bought and you can also share your list through Whatsapp.", "how_can_i_follow_my_results": "How can I follow my results?", "click_see_more_in_the_your_plan": "Click in 'See more' in the 'Your Plan' section of Nutrition to check your results so far. The app shows you graphics and statistics related to the goals you set.", "how_will_i_remember_when_should_i_eat": "How will I remember when I should eat and drink water so I can achieve my goals?", "in_the_upper_right_icon_click_reminders": "In the upper right icon (︙), click 'Reminders' turn on or off the notifications necessaries so you won't lose a single step of your plan.", "does_the_premium_signature_fit_me": "Does the premium signature fit me?", "if_you_are_looking_for_a_nutritional_support": "If you are looking for a nutritional support in addition to your trainings and needs, if you want help to keep yourself hydrated and have graphics to show you where you are compared to your goals, the Premium is perfect for you. With it you get the help you need to keep your focus and accomplish your objectives.", "can_i_have_more_than_one_plan": "Can I have more than one plan at a time?", "no_just_one_at_a_time": "No, just one at a time. If you stop one and choose another instead, by ending this one, you will be able to continue the last one from where you stopped.", "how_can_i_switch_my_plan": "How can I switch my plan?", "in_more_plans_at_the_end_of_nutrition_page": "In 'More plans' at the end of Nutrition page you can view other options. By clicking 'Start', your current plan will be paused and this new one will start from zero.", "can_i_change_my_weight_goal": "Can I change my weight goal?", "yes_you_just_got_to_go_to_the_upper_right_icon": "Yes, you just got to go to the upper right icon and click (︙) and click 'Adjust your plan'. If you want to, you can also adjust your main goal the same way.", "can_i_change_my_hydration_goal": "Can I change my hydration goal?", "yes_just_click_my_hydration": "Yes. Just click 'My hydration' in the upper right icon (︙) and set your optimal water consumption.", "where_can_i_find_the_health_and_nutrition_tips": "Where can I find the health and nutrition tips?", "navigate_to_the_lower_section_in_nutrition": "Navigate to the lower section in Nutrition and click 'Quick tips' to keep up to date with all the news!", "learn_more": "Learn more", "what_is_the_workout's_feedback": "What is the workout's feedback?", "through_the_feedback_you_can_view_the_ratings": "Through the feedback you can view the ratings the members give to their workout. It also shows the percentage of trainings executed by them, that is, you have data about the satisfaction of the members as well as their consistency.", "what_are_the_instructions": "What are the instructions?", "this_card_works_as_a_reminder": "This card works as a reminder. It's highlighted on it the workouts that are yet to execute (To do); the workouts that have passed their deadlines (Renewals); and the members that still have no workouts registered (No workouts).", "what_is_the_members": "What is the 'Members'?", "on_this_tab_you_can_consult": "On this tab you can consult the status of a member: active or inactive, and you can also check how many members you got on the app.", "basic_program": "Base programs", "what_are_the_workouts_exhibited_here": "What are these training sessions that appear here?", "these_are_the_pre_set_workout": "There are the pre-set workouts, files with exercises added to it. Some pre-set workouts are included to the app by default, but you can create new ones whenever you want.", "how_can_i_create_a_new_pre_set_workout": "How can I create a new pre-set workout?", "by_clicking_in_the_plus_icon": "By tapping the plus icon (+), you start creating a new workout. \n \nStep 1: Register the details of the workout(name, observation, gender, category, level, performance type) and tap 'Advance'. \nStep 2: After saving your workout, you can edit it, create a series pattern and add exercises. \nStep 3: Add exercises by clicking 'Add'. When you are done selecting all the exercises click 'Add exercises' to add them to the specific workout. \n Step 4: If you want to edit an exercise just click its name. Besides, it is possible to select a performance method (if you have more than one exercise you can create a pattern for all of them, including series, reps, and rest). Click 'Save' when you are done to add the exercises edition. \nStep 5: Click 'Save' to add your new workout. If you want you can add this workout to one of the member or you can just save it for later.", "can_i_add_this_workout_to_many_members_at_the_same_time": "Can I add this workout to many member at the same time?", "you_can_only_select_one_member_at_a_time": "You can only select one member at a time. If you want to add it to more members, go back and select one at a time.", "member_details": "Member details", "how_to_create_a_workout_program": "How to create a workout program?", "after_selecting_a_member": "After selecting a member, at the end of the page there will be a button to create a new workout program. Click 'Create new' and register: Name, planned classes, initial and final program date, and goals. Click 'Save' when you are done.", "how_do_i_register_a_workout_for_one_of_my_members": "How do I register a workout for one of my members?", "you_can_access_the_workouts_through_the_workouts_program": "You can access the workouts through the workout program, or click the floating button 'New workout', which will lead you to the pre-set workouts screen. If you don't want any of them just click the 'Register new workout' button and follow the steps:\n \n Step 1: Insert the details(name, observation, gender, category, level, performance type) and tap 'Advance'. \nStep 2: After saving your workout, you can edit it, generate a series pattern and add exercises. \n Step 3: Add exercises by clicking 'Add'. Search for the exercises you want and select them. Click 'Add exercises' when you are done to add them to a workout. \n Step 4: If you want to edit an exercise just click its name. Besides, it is possible to select a performance method (if you have more than one exercise you can create a pattern for all of them, including series, reps, and rest). Click 'Save' when you are done to add the exercises edition.", "my_workout_program's_deadline_is_close": "My workout program's deadline is close. What do I do?", "you_need_to_wait_until_it_has_passed_its_deadlilne": "You need to wait until it has passed its deadline so you can renew it. After that, in the member's profile, click 'Renew' in the workout program.", "workout_details": "Workout details", "how_to_add_exercises_to_a_workout": "How to add exercises to a workout?", "click_add_in_the_workout_screen_to_navigate_to": "Click 'Add' in the workout screen to navigate to the exercises. Then, select the ones you want and click 'Add exercises'. You can still edit the series, reps, rest and methods. Click 'Save' to confirm the changes", "can_i_add_an_performance_method_such_as": "Can I add performance methods such as bi-set and tri-set to my workouts?", "yes_after_adding_exercises": "Yes, after adding exercises you can edit them just by clicking the one you want. You can still edit the series, reps, rest and methods.", "what_are_the_other_options": "What are the 'Other options'?", "there_are_two_options_available": "There are two option available. The first one allows you to turn this workout into a pre-set workout, so you will be able to add this workout to other members. It will be saved in the 'Workouts' tab in the lower bar of the app. The second option allows you to create a series pattern for all of the exercises in the workout, not needing to create one by one for each exercise.", "editing_program": "Editing program", "what_is_the_workout_program": "What is the workout program?", "the_workout_program_is_where_all_the_workouts_and_exercises": "The workout program is where all the workouts and exercises that you added to a member can be found. You can edit, remove and add new workouts in this section.", "what_are_the_planned_classes": "What are the planned areas?", "planned_classes_are_the_classes_that_the_member_is_expected_to_take": "Planned classes are the classes that a member is expected to take based on the duration of his or her workout program and how many days a week the member goes to the gym.", "does_the_name_of_the_program_change_anything": "Does the name of the program change anything?", "no_the_program's_name_can_be_whatever": "No, the program's name can be whatever you want. It does not have an affect on anything.", "wrong_data": "Wrong data.", "member_check_if_your_input_is_correct": "Member: Check if your input is correct. Ask your personal trainer if your data (phone number, e-mail and password) are correct. \nPersonal trainer: Check in the system if your data are correct.", "i_can't_find_my_sms_code": "I can't find my SMS code. What do I do?", "if_the_code_wasn't_sent_to_you_via_sms": "If the code wasn't sent to you via SMS might be because the Google's service we use is taking a little longer. In this case we have the following solutions: \nWait a few minutes and try again. \n\nTry signing in with your e-mail and the password registered in the system. \n\n\nWarning: If you try requesting the SMS code many times your phone number access can be blocked.", "where_do_i_get_my_email": "Where do I get my e-mail and password?", "member_you_can_get_your_email": "Member: You can get your e-mail and password with your personal trainer. \nPersonal trainer: You have to use the e-mail and password that you registered in the contract form.", "members": "Members", "is_it_possible_for_me_to_add_a_member": "Is it possible for me to add a member?", "yes_by_clicking_the_upper_right_button_you_will_navigate_to_the_register_form": "Yes, by clicking the upper right button you will navigate to the register form that you gotta fulfill with the details about the member. You can send an invite to someone for them to register their own information.", "what_are_these_filters": "What are these filters?", "they_are_the_easiest_way_to_group_members": "They are the easiest way to group members by status, which will help you manage the members and their workouts.", "members_added_through_the_browser_training_will_be_shown_here": "Members added through the browser training will be shown here?", "yes_every_member_will_be_exhibited_here": "Yes, every member will be exhibited here, no matter how they were registered.", "recommendations": "Recommendations", "how_can_i_get_a_bonus_for_recommending_to_friends": "How can I get a bonus for inviting friends?", "it_is_pretty_simple_you_send_your_code": "It is pretty simple. You send your code to your friends and every two purchases made using your code, you get a free month. So, the more you recommend the app, the bigger are the chances for you to get the prize.", "how_can_i_recommend": "How can I recommend?", "how_do_i_follow_my_progress_on_the_prize": "How do I follow my progress on the prize?", "in_the_recommendation_page_it_will_be_registered": "In the recommendation page it will be registered your progress and how much recommendations you need to get the prize. By clicking 'Details' you can check which were the recommendations that made you move on.", "meditation_and_focus": "Meditation and focus", "the_breathing_exercise_will_help_you": "The breathing exercise will help you regain your focus. Optimal when you need to set your mind free.", "current_weight": "Current weight", "hey_this_one_matches_your_plan": "Hey, this one matches your plan!", "it_has_the_same_quantity_of_calories": "It has the same quantity of calories that you need in your x Meal. Do you want to replace it?", "replace_it": "Replace it", "start": "Start", "daily_kcal": "daily kcal", "meals": "meals", "diversity": "variety", "more_meals": "More meals", "quick_tips": "Quick tips", "more_plans": "More plans", "insert_the_name_of_the_recipe": "Insert the name of the recipe", "ops_no_recipe_found": "Ops, no recipe found.", "check_if_there_are_no_typos": "Check if there are no typos and try again. Use different keywords if necessary.", "results": "Results", "calories_content": "Calories", "cooking_time": "Cooking time", "food_restriction": "Food restriction", "apply_filters": "Apply filters", "up_to": "up to", "your_plan": "Your plan", "schedule_plan": "Schedule plan", "when_do_you_want_to_begin": "When do you want to begin?", "goal": "Goal", "bmi": "BMI", "view_your_progress": "View your progress", "congratulations_you_reached_your_weight_objective": "Congratulations! You reached your weight objective! 💪", "no_weight": "No weight", "kcal_consumed": "kcal consumed", "valid_until": "valid until", "open_source_libraries": "Open source libraries", "HOMEFIT": "", "create_live_workout": "Create live workout", "step_2_of_2": "Step 2 of 2", "start_now": "Start now", "select_the_end_time": "Select the end time", "set_programar": "Set", "select_the_start_and_end_time": "Select the start and end time", "unable_to_complete_your_request": "Unable to complete your request", "create_workout": "Create workout", "save_changes": "Save changes", "step_1_of_2": "Step 1 of 2", "add_a_name_to_your_workout": "Add a name to your workout", "workout's_name": "Workout's name", "talk_to_your_members_about_the_live": "Talk to your members about the live", "description": "Description", "live's_url": "Live's URL", "next": "Next", "it_was_not_possible_to_delete_the_live": "It was not possible to delete the live stream", "add_video": "Add video", "video's_url": "Video's URL", "please_insert_the_video's_link": "Please insert the video's link", "exercise's_description": "Exercise's description", "the_description_will_be_presented_when_the_member": "The description will be presented when the member accesses", "record": "Record", "share": "Share", "my_workout_continues_in_the_app": "My workout continues in the app", "still_fitness_at_home": "Still fitness at home", "finished_in": "Finished in:", "create_new_tag": "Create a new tag", "insert_the_name": "Insert the name", "type_the_name_of_the_tag": "Type the name of the tag", "the_item_will_be_exhibited_at_the_tags_category": "The item will be exhibited at the tags category.", "you_need_to_insert_a_name": "You need to insert a name", "the_tag_has_been_saved": "The tag has been saved", "all_ready": "All ready!", "details": "Details", "difficulty_level": "Difficulty level", "muscles_used": "Muscles used", "equipments": "Equipments", "create_your_own_tags": "Create your own tags by clicking the add button (+)", "remove": "Remove", "create": "Create", "edit": "Edit", "online_workout": "Online workout", "add_the_title_that_best_describes_your_workout": "Add the title that best describes your workout", "describe_your_workout_for_the_members": "Describe your workout for the members", "*details": "*Details", "details_were_not_stated": "Details were not stated", "workout's_header": "Workout's header", "pick_an_image_that_describes_the_workout": "Pick an image that describes the workout", "attention": "Attention", "the_workout's_details_need_to_be_set": "The workout's details need to be set", "workout's_videos": "Workout's videos", "add_lower_case": "add", "personal_trainer": "Personal trainer", "workout's_details": "Workout's details", "workout_with_no_exercises": "Workout with no exercises", "1_exercise": "1 exercise", "exercises": "Exercises", "who_completed_the_workout": "Who completed the workout?", "it_was_not_possible_to_register_the_execution": "It was not possible to register the execution, try again.", "finish_workout": "Finish workout", "workout_completed": "Workout completed", "no_videos": "No videos", "1_video": "! video", "videos": "videos", "be_the_first_one": "Be the first one", "1_person_completed": "1 person completed", "people_finished": "people finished", "workout_by": "Workout by", "workout": "Workout", "save_in_the_camera_roll": "Save in the camera roll", "filters": "Filters", "clean_filters": "Clean filters", "keep_your_weight": "Keep your weight", "muscle_building": "Muscle building", "learn_more_about": "Learn more about:", "at_home_workout": "At home workout", "return_to_feed": "Return to feed", "exercise's_details": "Exercise's details", "the_video_has_no_description": "The video has no description", "select_the_customers": "Select the customers", "the_keywords_did_not_match_any_results": "The keywords you searched for did not match any results.", "select_the_events_for_the_inapp": "Select the events for the InApp", "no_results": "No results", "have_i_heard_prizes": "Have I heard prizes?", "recommend_the_app_for_other_personal_trainers": "Recommend the app for other personal trainers and get a free month.", "make_as_many_recommendations": "Make as many recommendations as you want.", "you_get_a_free_month_to_every_two": "You get a free month to every two personal trainers that subscribe to the app.", "looks_like_your_membership_is_frozed": "It looks like your membership is frozed. Get in touch with your gym to solve this", "invite_other_personal_trainers_to_the_app": "Invite other personal trainers to the app. You get a free month every 2 subscription.", "copied_to_clipboard": "Copied to clipboard", "share_your_code": "Share your code", "join_us_at": "Join us at {}. Enter the following link: ", "ops_seems_like_there_is_no_one": "Ops! Seems like there is no one around", "keep_recommending": "Keep recommending the app to increase your chances of getting the prize.", "prize's_validity": "Prize's validity", "free_month_is_valid_until": "Free month is valid until", "check_who_is_using_the_app_after_your_recommendation": "Check who is using the app after your recommendation down below", "you_have_not_recommended": "You have not recommended the app yet. Go ahead and share a recommendation link.", "recommend": "Recommend", "recommendation": "Recommendation", "how_does_it_work": "How does it work?", "you_share_your_link_however_you_want": "You share your link however you want and as much as you want. Every 2 personal trainers that subscribe through your link gives you a free month.\n\nYour free month will start one month after the second person's subscription started. Click 'Detail' to check everything about your free month. \nYou can get until 12 months total. The more people you recommend the app to, the bigger are your chances of getting the prize.", "campaign's_regulation": "Campaign's regulation", "unable_to_validate_the_qr_code": "Unable to validate the QR code", "acccess_with_qr_code": "Access with QR code", "position_the_camera_right": "Position the camera right", "point_the_camera_to_the_qr": "Point the camera to the gym's QR code and wait for it to be processed", "select_your_user": "Select your user", "welcome_to_the_workout": "Welcome to the Workout app", "an_easy_way_to_keep_your_routine": "A good solution for helping you keep your routine under control and follow your results.", "phone_number_not_found": "Phone number not found", "access_with_your_email": "Access with your e-mail", "you_need_to_save_the_changes": "You need to save the changes", "developer_mode": "Developer mode", "all_the_urls_must_end_with": "All the URLs must end with /", "turn_on_dev_mode": "Turn on dev mode", "login_without_a_password": "Login without a password", "use_discovery": "Use discovery", "reset_original": "Reset original", "select_your_unit": "Select your unit", "your_search_did_not_match_any_results": "Your search did not match any results", "forgot_password": "Forgot password", "we_will_send_you_an_email": "We will send you an e-mail \nfor you to recover your password", "you_will_receive_an_email": "You will receive an e-mail in a few moments", "alright": "Alright", "unable_to_find_this_email": "Unable to find this e-mail. Check if you have entered the correct e-mail", "there_has_been_an_error_sending_the_email": "There has been an error sending the e-mail. Check your connection and try again.", "tip_uppercase": "TIP", "check_your_spam_if_you_did_not_get_the_email": "Check your spam \nif you did not get the e-mail", "users_found": "Users found", "the_phone_number_inserted_is_different": "The phone number inserted is different from the one in the register. Request your phone number to be uptade.", "confirm_the_code": "Confirm the code", "the_6_digits_code": "Please provide the 6-digit code sent via SMS to the provided number.", "resend": "Resend", "reenviar_codigo": "Resend code", "invalid_code": "Invalid code", "my_result": "My result", "post_result": "Post result", "result_posted_successfully": "Result posted successfully", "you_are_in_the_rank": "You are number {} in the rank", "fill_out_the_fields": "Fill out the fields to continue", "category": "Category", "rest": "Rest", "select_a_time_bigger": "Select a time bigger than 00:00", "start_stopwatch": "Start timer", "progressive": "Progressive", "stopwatch_type": "Timer type", "select_the_type_of_stopwatch": "Select the type of the Timer", "time": "Time", "gym's_plans": "<PERSON><PERSON>'s plans", "no_plans_available_right_now": "No online plans available right now", "available_plans": "Available plans", "this_plan_includes": "This plan includes: ", "buy_now": "BUY NOW", "complete_purchase": "Complete purchase", "step_2_of_3": "Step 2 of 3", "my_credit_card": "My credit card", "cards_number": "Card's number", "name_on_credit_card": "Name on credit card", "expiry_date": "Expiry date", "step_1_of_3": "Step 1 of 3", "basic_info": "Basic info", "name": "Name", "date_of_birth": "Date of birth", "contact": "Contact", "phone_numner": "Phone number", "address": "Address", "zip_code": "ZIP code", "street_name": "Street name", "complement": "Complement", "fill_out_the_form_correctly": "Fill out the form correctly", "insert_the_quantity_of_rounds": "Insert the quantity of rounds", "stopwatch": "Timer", "select_a_time_bigger_to_rest": "Select a time bigger than 00:00 to rest", "exercise": "Exercise", "the_stopwatch_keeps_going": "The Timer keeps going while your app is open", "stay": "Stay", "go_back_to_home": "Go back to home", "percentage_calculator": "Percentage calculator", "reference_weight": "Reference weight", "percentage": "Percentage", "new_record": "New record", "no_description_for_this_exercise": "No description for this exercise", "you_have_not_informed_your_pr_yet": "You have not advised your PR yet", "always_keep_your_prs_updated": "Always keep your PRs updated", "so_you_can_calculate_based": "so you can calculate based on them, besides", "getting_rank_points": "getting rank points", "warmup": "Warmup", "stretching_mobility": "Stretching/Mobility", "skill_parte_tecnica": "Skill", "no_wod's_result": "No wod's result", "register_your_result": "Register your result", "register_pr": "Register PR", "personal_records": "Personal records", "search_for_a_wod": "Search for a wod or an exercise", "no_exercises_for_now": "No exercises for now", "search_for_another_keyword": "Search for another keyword", "ops_no_exercises_found": "Ops! No exercises found", "unable_to_load_the_list": "Unable to load the list, pull down to try again", "you_want_to_leave_without_posting": "You want to leave without posting your result?", "leave": "Leave", "unable_to_process_the_wods_list": "Unable to process the WODs list", "no_wods_for_now": "No WODs for now", "there_is_no_wod_today": "There is no WOD today", "standard_stopwatch": "Clock", "new_wod": "New WOD", "edit_wod": "Edit WOD", "you_must_insert_the_name_wod": "You must insert the name, the date and the type of WOD before you can continue", "upload_an_image": "Upload an image", "upload_an_square_image": "For a better view upload a square image", "replace_image": "Replace image", "wod's_name": "<PERSON><PERSON>'s name*", "my_wod": "My Wod", "wod's_type": "<PERSON><PERSON>'s type", "date": "Date", "wod's_date": "<PERSON><PERSON>'s date", "saved": "Saved", "the_wod_has_been_saved": "The {} has been saved", "creation": "Creation", "wod's_edition": "<PERSON><PERSON>'s edition", "aparelhos": "Equipments", "remarks": "Observations/Notes", "stretching": "Stretching", "cross_equipments": "Cross equipments", "add": "Add", "search_by_name": "Search by name", "error_consulting_the_equipments": "Error consulting the equipments", "cross_exercises": "Cross exercises", "daily_wod_haven't_been_registered": "Daily Wod haven't been registered yet", "click_the_upper_icon_and_register": "Click the upper icon and register one right now", "excluir": "Delete", "has_been_deleted": "{} has been deleted", "duplicate": "Duplicate", "type": "Type", "unable_to_load_data": "Unable to load data", "no_wods": "No WODs", "register_the_first_wod": "Register the first WOD by clicking the upper (+) button", "you_can_change_the_settings": "You can change the settings of your app anytime you want in the 'Settings' screen.", "customize_later": "Customize later", "start_customization": "Start customization", "choose_one": "Choose one\n", "main_color": "Main color", "choose_the_main_color_of_your_app": "Choose the main color of your app", "move_on": "Next", "select_your": "Select your\n", "the_logo_choice": "The logo choice is your business's identity. We suggest you use a transparent background image.", "move_on_without_a_logo": "Move on without a logo?", "dont_worry_you_can_insert_a_new_logo": "Don't worry, you can insert a new logo anytime you want in the settings.", "add_an_image_later": "Add an image later", "unable_to_save_your_changes": "Unable to save your changes. Try again later", "finally_choose_the": "Finally choose the", "name_app": " name ", "of_your_app": "of your app", "the_name_will_be_exhibited": "The name that will be exhibited to the gym's member", "insert_a_name": "Insert a name", "finish_setting": "Finish setting", "the_app_will_be_restarted_so": "The app will be restarted so your customization can be applied", "you_can_change_the_customization": "You can change the customization anytime you want, just access the sidebar menu", "alright_your_app": "Alright, \nyour app", "already_customized": "is already \ncustomized", "what_is_you_goal": "What is your goal?", "do_you_have_any_food_retriction": "Do you have any food restriction?", "how_many_meals_a_day": "How many meals a day?", "3_meals_and_2_snacks": "3 meals and 2 snacks", "3_meals_and_1_snack": "3 meals and 1 snack", "3_meals_and_3_snacks": "3 meals and 3 snacks", "generating_your_nutritional_plan": "Generating your nutritional plan", "wait_a_few_seconds": "Wait a few seconds", "fat_loss": "Fat loss", "unable_to_load_tips": "Unable to load tips", "unable_to_load_recipes": "Unable to load recipes", "no_recipes_found_for_these_keywords": "No recipes found for these keywords", "my_nutritional_plan": "My nutritional plan", "food_restrictions": "Food restrictions", "quantity": "Quantity", "thats_the_summary_of_your_nutritional_plan": "That's the summary of your nutritional plan's customization. You can edit it whenever you want.", "ops_there_has_been_an_error_nutrition": "Ops, there has been an error loading the nutritional plan", "my_day": "My day", "recipes": "Recipes", "week_maiusculo": "Week", "tips": "Dicas", "activate_right_now": "Activate right now", "enable": "Enable", "no_idea_of_what_you_should_eat": "No idea of what you should eat? Wanna try something different without leaving your diet? Check the exclusive recipes made by our nutritionist crew.", "ratings": "Ratings", "nutritional_information": "Nutritional information", "protein": "<PERSON><PERSON>", "lipids": "Lipid", "net_carb": "Net carb", "ingredients": "Ingredients", "preparation_method": "Preparation method", "responsible": "Responsible", "other_options": "Other options", "what_do_you_think_about_this_recipe": "What you think about this recipe?", "your_feedback_helps_us": "Your feedback helps us improve our recipes.", "the_creator_is_unknown": "The creator is unknown", "unable_to_load_the_creator": "Unable to load the creator", "thank_you": "Thank you", "your_subscription_is_already_active": "Your subscription is already active and you can enjoy all the features of your Premium plan.", "manage_your_fitness_routine": "Manage your fitness routine", "all_you_need_to_have_a_healthier": "All in one place to have a healthier day", "your_workout_allied_with_your_diet": "Your workout \nallied with your diet", "create_nutritional_plans": "Create nutritional plans that are in line with your goals", "breathing_and_focus": "Breathing and focus", "relax_and_meditate": "Relax and meditate so you can bo gack with full energy", "exclusive_content": "Exclusive content", "access_tips_and_info": "Access tips and info about nutrition, health and wealth", "the_ideal_hydration": "The ideal hydration", "set_the_optimal_amount": "Set the optimal amount of water for you and get regular reminders to stay hydrated", "your_purchase_cant_be_restored": "Your purchase can't be restored because it is no longer active, we suggest you buy a new plan", "we_cant_detect_your_purchase": "We can't detect your purchase", "view_plans": "View plans", "subscribe_1_month": "Subscribe 1 month", "subscribe_months": "Subscribe {} months", "limited_offer": "Limited offer!", "you_can_try_the_app": "You can try the app for 3 days and after that you get 63% off the annual subscription", "benefit_from_the_offer": "Benefit from the offer", "check_what_youll_get_access_to": "Check what you will get access to", "customized_nutritional_plans": "Customized nutritional plans", "automatic_shopping_list": "Automatic shopping list", "hydration_alert": "Hydration alert", "recipes_and_diets": "Recipes and diets", "breathing_control": "Breathing control", "step_tracker": "Step tracker", "health_and_nutrition_content": "Health and nutrition content", "subscriptions_available": "Subscriptions available", "already_a_member": "Already a member?", "restore_purchases": "Restore purchases?", "our_advantages": "Our advantages", "check_how_everything_becomes_easier": "Check how everything becomes easier", "feature": "Feature", "basic": "Basic", "classes": "Classes", "workouts_maiusculo": "Workouts", "physical_evaluation": "Physical evaluation", "nutritional_plans": "Nutritional plans", "fitness_routine": "Fitness routine", "health_content": "Health content", "3_days_free": "3 days free", "save_63%": "Save 63%", "/month": "/ month", "its_all_you_needed_to_get": "It's all you needed to get the results and stop self-sabotaging", "regular_questions": "Regular questions", "what_is_the_premium": "What is the Premium?", "its_the_part_of_the_app": "It's the part of the app that brings still more health to the everyday context by adding the nutrition plan to your workout. It even helps you manage your routine to have healthier habits, which contributes to getting faster results.", "which_features_i_will_get_access": "Which features I will get access to?", "youll_have_access_to_customized": "You will have access to customized nutritional plans, step tracker, recipes and a variety of diets, reminders to keep hydrated, resource for breathing control, health tips, and all your fitness routine in one place", "how_does_my_nutritional_plan_work": "How does my nutritional plan work?", "to_start_off_a_nutritional_plan": "To start off a nutritional plan you have to answer to a detailed health questionnaire with your preferences and needs. With that we generate a planning with all the meals you gotta have and a shopping list with the ingredients of each one of the meals. Then, you just have to check your meals and follow your progress. If you want to you can replace a meal for another one that you like better.", "how_much_does_it_cost": "How much does it cost for me to use all of the Premium's features?", "you_have_the_following_options": "You have the following options: the monthly plan R$9,90 (BRL) or the annual plan (R$49,90 (BRL) - R$7,49/month)). There are no extra charges. Cancel your subscription anytime you want.", "exclusive_nutritional_tips": "Exclusive nutritional tips", "fitness_schedule": "Fitness schedule", "breathing": "Breathing", "drink_water": "Drink Water", "create_customized_nutritional_plans": "Create customized nutritional plans allied with your workouts and needs.", "generated_according_to_the_ingredients": "Generated according to the ingredients that your nutritional plan take.", "quality_content_about_the_fitness": "Quality content about the fitness and wellness world made by specialists.", "access_a_diversity_of_recipes": "Access a variety of recipes and diets that you can try anytime.", "have_your_whole_fitness_routine": "Have your whole customized fitness routine in one place. Know what to do and when.", "control_your_breathing_so_you": "Control your breathing so you can improve your focus, meditate and even ease your stress.", "set_a_daily_objective": "Set a daily objective to follow your progress and check how many calories you burned.", "know_the_ideal_amount_of_water": "Know the ideal amount of water for your body and set reminders to help you remember to keep yourself hydrated.", "all_of_the_premium_features_ios": "All of the Premium features are included in this membership. Your subscription is charged just once and automatically renewed. You can cancel it for free anytime you want. After the choosen period your membership will be automatically renewed through your iTunes account, unless the automatic charge gets disabled by you at least 24 hours before the end of the current plan. The payment will be charged in your iTunes account after the purchase gets confirmed. The renewal's price is R$ 59,90 (BRL) for an annual plan and R$ 35,90 (BRL) for a biannual plan. The subscriptions can be managed by you by accessing 'Setting'. You will lost the rest of your free trial if you join the Premium while it has not ended yet.", "all_of_the_premium_features_android": "All of the Premium features are included in this membership. Your subscription is charged just once and automatically renewed. You can cancel it for free anytime you want. After the choosen period your membership will be automatically renewed through your Google Play account, unless the automatic charge gets disabled by you at least 24 hours before the end of the current plan. The payment will be charged in your Google Play account after the purchase gets confirmed. The renewal's price is R$ 59,90 (BRL) for an annual plan and R$ 35,90 (BRL) for a biannual plan. The subscriptions can be managed by you by accessing 'Setting'. You will lost the rest of your free trial if you join the Premium while it has not ended yet.", "all": "All", "championship": "Championships", "my_meals": "My meals", "terms_of_use_eula": "Terms of Use (EULA)", "language": "Language", "select_a_language": "Select a language", "are_you_sure_change_language": "Are you sure you want to change the language?", "the_app_will_be_restarted": "The app will be restarted", "trending": "Trending 🔥", "prof.": "Trainer", "exercise_video": "Exercise's video", "breathe_again": "Breathe again", "tip": "Tip: ", "stay_put": "\nStay put in a comfortable position. Keep your mind clean and pay attention to your breathing.", "you_can_come_back": "\nYou can come back everytime you need to get more focused.", "well_done": "Well done", "breathings": "breathings", "breathe_for_at_least": "1. Breathe for at least 1 minute daily to complete the circles. \n2. You can choose how many minutes navigating the app. \n3. After you start it you can cancel it by clicking the X.", "privacy_and_data": "Privacy and data sharing and usage", "by_turning_this_on": "By turning this on you get", "more_efficient_and_safer": "More efficient and safer error analysis", "the_best_experience": "The best experience with the app", "infos_that_match_your_interests": "Informations that match your interests", "you_can_change_your_prefferences": "You can change your prefferences anytime you want in the privacy settings of the Settings app", "select_your_country": "Select your country", "would_you_like_to_enter_cpf": "Would you like to enter using your social security number?", "mask_cpf": "***********", "no_register_found_with_this_cpf": "No register found with this\nsocial security number. Get in\ntouch with your gym.", "enter_another_cpf": "Enter another social security number", "contract": "Contract", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "data_por_extenso": "{mesExtenso} {dia}, {ano}", "muscles": "Muscles", "bones": "Bones", "fat": "Fat", "residue": "Residue", "right": "Right", "left": "Left", "others": "Others", "abs": "Abs", "new_post": "New post", "later": "Later", "no_permission_camera": "No permission to access the camera", "you_need_to_give_permission": "You need to go to the Settings in your cell phone and give permission to the app to use the camera.", "settings": "Settings", "default": "<PERSON><PERSON><PERSON>", "sepia": "Sepia", "gray": "<PERSON>", "violet": "Violet", "cold": "Cold", "use_filter": "Apply filter", "time_ago_language": "en_short", "dia_formato_aulas": "{mesTratado}/{diaTratado}", "locale_calendar": "en", "locale_item_mover_entre_dias": "en", "data_aula_tratada": "{mes}/{dia}/{ano}", "delete_exercise's_image": "Delete exercise's image?", "do_you_want_to_delete_img_exercice": "Do you want to delete the image of the {} exercise?", "no_equipments": "No equipments", "series": "Sets", "serie": "Set", "standard_performance": "Standard performance", "reps": "Reps", "duration": "Duration", "load": "Weight", "speed": "Speed", "cadence": "<PERSON><PERSON>", "distance": "Distance", "ps": "P.S. ", "exercise_has_no_series": "The exercise has no sets", "the_exercise_has_no_registered_series": "The exercise has no registered series. Talk to your trainer.", "adjusting": "ADJUSTING", "apply_to_all_series": "Apply to all sets", "apply_only_to_this_series": "Apply only to this set", "change_weight": "Change weight", "change_all_series": "Change all sets", "skip_rest": "Skip rest", "workout_paused": "WORKOUT PAUSED", "done_maiusculo": "DONE", "total_time": "TOTAL TIME", "minutes": "MINUTES", "my_records": "My records", "timer_finished": "Timer finished", "total_time_lower_case": "Total time", "post_your_result": "Post your result", "finish": "Finish", "ops_there_has_been_a_problem": "Ops, there has been a problem", "your_contract_has_not_been_activated": "Your contract has not been activated. Check your data and try again.", "plan_activated_successfully": "Plan activated successfully", "your_plan_expires_in": "Your plan{} expires in {} days.", "renew_your_plan_to_enjoy": "Renew your plan to enjoy everything your gym has got to offer.", "renew_now": "Renew now", "choose_another_plan": "Choose another plan", "select_your_level": "Select your level", "beginner": "<PERSON><PERSON><PERSON>", "amateur": "Amateur", "advanced": "Advanced", "intermediary": "Intermediary", "comment": "Comment", "optional": "Optional", "weight": "Weight", "level": "Level", "male": "Male", "female": "Female", "to": " to ", "congrats_you_finished_your_plan": "Congratulations! You finished your nutritional plan.", "see_results": "See results", "congrats_name": "Congratulations, {}!", "now_you_weigh": "Now you weigh", "of_this_plan_was_completed": "Of this plan\nwas completed", "lets_improve_ourselves_together": "Let's improve ourselves together", "sometimes_it_might_be_hard": "Sometimes it might be hard to follow the plan 100%, but you are in the right way. Just keep going.", "you_finished_the": "You finished the", "every_little_step": "Every little step in this walk is important, share you result with your friends.", "choose_a_new_plan": "Choose a new plan", "go_back_to_navigation": "Go back to navigation", "you_have_no_active_plan": "You have no active plan right now, what would you like to do?", "you_finished_your_plan": "You have finished your plan", "when_we_started": "When we started", "you_weighed": "you weighed", "confirm_data": "Confirm data", "before_you_start_a_new_plan": "Before you start a new plan you have to update your nutritinal data.", "begin_a_customized_plan": "Begin a new customized plan.", "recommended_plans": "Recommended plans", "insert_the_name_of_the_plan": "Insert the plan's name", "ops_no_plan_found": "Ops, no\nplan found", "check_if_there_is_any_typo": "Check if there is any typo and try again. Use another keywords if necessary.", "news!": "News!", "meals_maiusculo": "Meals", "diversity_maiusculo": "Variety", "whats_your_initial_goal": "What's your initial goal?", "whats_your_sex": "What's your sex?", "lets_kick_off_with_the_basics": "Let's kick off with the basics", "your_heigth": "Your height", "your_birth_date": "Your birth date", "birth_date": "Birth date", "your_current_weight": "Your current weight", "whats_your_target_weight": "What's your target weight?", "target_weight": "Target weight", "how_many_meals_do_you_normally": "How many meals do you normally have in a day?", "whats_the_diversity_of_meals": "What kind of food variety do you want for your plan?", "no_diversity": "No variety", "little_diversity": "Less variety", "big_diversity": "More variety", "select_your_level_of_physical": "Select your level of physical activities", "sedentary": "Sedentary", "i_dont_practice_exercises": "I don't practice exercises at all.", "1_to_2_times_a_week": "I practice from 1 to 2 times a week.", "3_to_5_times_a_week": "I generally practice from 3 to 5 times a week.", "i_practice_everyday": "I practice everyday.", "gluten": "Gluten", "fructose": "Fructose", "animal_origin_foods": "Animal origin foods", "meats": "Meats", "eggs": "Eggs", "seafood": "Seafood", "peanut_and_chestnuts": "Peanut and chestnuts", "milk_and_its_derivates": "Milk and its derivates", "fish": "Fish", "do_you_have_any_sort_of_restriction": "Do you have any sort of food restriction?", "change": "Change", "supper_meal": "Supper meal", "meals_time": "<PERSON><PERSON>' time", "calories": "Calories", "average_daily_consumption": "Average daily consumption", "carbs": "<PERSON><PERSON>", "proteins": "<PERSON>teins", "fats": "Fats", "water_goal": "Water goal: ", "more_info": "More Info", "height": "Height", "history": "History", "minimum_weight": "Minimum weight", "maximum_weight": "Maximum weight", "main_meals": "Main Course", "lunch_and_dinner": "Lunches and Dinners", "breakfasts": "Breakfasts", "supper_meals": "Supper meals", "lanches_da_manha_e_da_tarde": "Morning and Aftertnoon snacks", "jantares_e_almocos": "Dinners and Lunches", "more_recipes": "More recipes", "no_favorite_recipes": "No favorite recipes", "the_recipes_you_add_to_favorites": "The recipes you add to your favorites will be exhibited here.", "favorite_recipes": "Favorite recipes", "confirm_meals": "Confirm meals", "you_are_almost_there": "You are almost there!", "to_start_off_your_plan": "To start off your plan you need to confirm the recipes we generated for each one of your meals", "generating_your_meals": "Generating your meals...", "iniciar": "Start", "you_are_out_of_workouts": "you are out of workouts or your workouts are expired or you already performed all of them. Talk to your trainer to start working out.", "your_workout_is_expired": "Your workout is expired. Get in touch with your trainer to renew it.", "in_progress": "In progress", "view": "View", "your_workout_program": "Your workout program", "until": "until", "realizados": "Performed", "expected": "Expected", "workouts_performed": "Workouts performed", "wheres_my_workout": "Where's my workout", "yes_cancel_appointment": "Yes, remove", "so_pode_marcar_min_antes": "You can't check-in this class anymore because the period to do it has finished.", "position_the_cam": "Position the camera", "point_the_cam": "Point the camera to the member's QR code and wait for it to be processed.", "my_members": "My members", "no_workouts": "No workouts", "expired": "Expired", "inactives": "Inactives", "my_wallet": "My portfolio", "no_member_was_found": "No member was found", "verifique_se_digitou_o_nome_correto": "Check if you inserted the correct name and try again.", "crop_image": "Crop image", "serie_set": "Set", "your_weight": "Your weight", "you_gained": "You gained", "you_lost_weight": "You lost", "start_a_new_plan": "Start a new plan", "weights_progress": "Weight's progress", "your_focus_and_determination": "Your focus and determination will be mandatory for you to reach your expected goal.", "congrats,": "Congratulations,", "you_reached_your_goal_before": "You reached your goal before your nutritional plan finished. Now you can keep up with the current plan or change your goals start a new one ", "your_plan_has_reached_the_end": "Your plan has reached the end.", "start_a_new_plan_or_choose_an_existing": "Start a new plan right new or choose an existing one.", "apply": "Apply", "fill_up_the_data": "Fill up the data", "the_sheet_must_have_a_name": "The sheet must have a name and a frequency.", "no_goals_registered": "No goals registered", "unable_to_load_data_try_again": "Unable to load data, try again.", "age_not_informed": "Age not informed", "number_of_exercises": "Number of exercises:", "from_the_pre_defined": "from the pre-defined sheets?", "from_the_workout_program": "from the workout program?", "performance_type": "Performance type:", "alternated": "Alternated", "week_days": "Week days", "perform": "Perform", "go_back_pre_defined_sheets": "Go back to the list of pre-defined sheets?", "sim_voltar": "Go back", "select_the_member": "Select the member", "to_the_member": "to the members", "to_the_members": "to the members", "voltar_ou_add_mais_alunos": "do you want to come back or do you want to add it to another member?", "add_to_another_member": "Add to another member", "age": "Age: {}", "goals": "Goals", "register_new_sheet": "Register new sheet", "years_old_sex": "years old | Sex:", "expiration_date": "Expiration date: {}", "send_message": "Send message", "the_member_has_not_registered_phone_number": "The member has not registered a phone number yet", "unable_to_load_the_workout_program": "Ops! Unable to load the workout program", "try_again": "Try again", "workout_program": "Workout program", "ops_your_member_has_no_workout": "Looks like your member has no workout yet", "create_a_new_plan": "Create a new plan for your member to start his/her routine right now.", "create_a_new_one": "Create a new", "move_on?": "Go next?", "prosseguir": "Next", "by_clicking_next_your_program": "By clicking next your program will be renewed", "unable_to_renew_your_program": "Unable to renew your workout program", "sheet": "sheet", "beginning": "Beginning", "end": "End", "this_workout_program_end": "This workout program has come to an end", "the_changes_will_be_applied_to_your_member": "What do you want to do? The changes you choose will be aplied to the member immediately.", "renew": "<PERSON>w", "member:": "Member's name:", "workout_program_name": "Workout program's name", "sheets": "Sheets", "programs": "Programs", "there_has_been_an_error_accessing_workout": "There has been an error accessing this workout. Pull down to refresh and try again.", "add_sheet": "Add sheet", "sheets_data": "Sheet's data", "choose_member": "Choose member", "sheets_name": "Sheet's name", "category_sheet": "Category", "message_sheet": "Sheet's message", "exercises_sheet": " Sheet's exercises", "add_minusculo": "add", "from_the_workout": "from the workout", "no_picture": "No picture", "anaerobic": "Anaerobic", "aerobic": "Aerobic", "no_sets_registered": "No sets registered", "sets": "Sets", "with": "with", "sheet_with_no_exercises": "The sheet has no exercises", "add_a_new_one_exercise": "Add a new one by clicking add", "some_exercises_have_no_sets": "Some exercises have no sets. Do you want to save it anyway?", "yes_save_with_no_sets": "Yes, save with no sets.", "no_i_will_add": "No, I will add", "what_do_you_want_to_do": "What to you want to do?", "you_can_save_this_sheet": "You can save this sheet as a pre-defined sheet or just add it to the member", "sheet_saved": "Sheet saved", "add_sheet_to_member": "Do you want to add this sheet to a member?", "yes_add": "Yes, add", "no_go_back": "No, go back", "save_as_a_pre_defined": "Save as a pre-defined sheet?", "just_add_to_member": "Just add to member", "add_to_pre_defined": "Add to pre-defined sheets?", "sets_pattern": "Sets pattern", "anaerobico": "Anaerobic: ", "aerobico": "Aerobic: ", "video": "video", "select_exercises": "Select exercises", "no_exercises": "No exercises", "try_searching_with_another_name": "Try searching with another name or a different category.", "add_exercise": "Add {} exercise", "add_exercises": "Add {} exercises", "generate_sets_pattern": "Generate sets pattern", "by_clicking_save_all_the_exercises": "By clicking save, all the exercises in this workout will follow this pattern.", "for_anaerobic_exercises": "For anaerobic exercises", "for_aerobic_exercises": "For aerobic exercises", "performance_method": "Performance's method", "no_sets": "No sets", "use_the_options_above": "Use the options above to create sets real quick", "will_be_removed_from_exercise": "will be removed from the exercise", "speed_km_h": "Speed (km/h)", "distance_in_meters": "Distance in meters", "meters": "Meters", "observacao": "Note", "apply_to_the_exercise": "Apply to the exercise", "edit_set": "Edit set", "select": "Select", "the_exercise": "the exercise", "the_exercises": "the exercises", "the_bi_set_method": "The bi-set method is made by 2 exercises, choose the other exercise.", "the_tri_set_method": "The tri-set method is made by 3 exercises, choose other 2.", "the_bi_set_method_only_allows": "The bi-set method only allows 1 exercise to be included.", "the_tri_set_method_only_allows": "The tri-set method only allows 2 more exercises to be included.", "do_you_want_to_remove_this_post": "Do you want to remove this post?", "select_an_image_that_describes": "Select an image that describes the workout", "your_workout_has_been_edited": "Your workout has been successfully edited", "your_workout_has_been_created": "Your workout has been created", "choose_an_option": "Choose an option", "to_continue_you_must_add_a_name": "To continue you must add a name and an image to the sheet.", "to_continue_you_need_to_add": "To continue you need to add at least one exercise.", "your_sheet_was_executed": "Your sheet was executed successfully.", "you_already_have_a_plan_in_progress": "You already have a plan in progress", "save_in_the_gallery": "Save in the gallery", "are_you_sure_delete_workout": "Are you sure you want to delete this workout?", "do_you_want_to_remove_wod": "Do you want to remove", "new": "New", "workout_program_minusculo": "workout program", "programs_name": "Program's name", "type_the_name_of_the_program": "Type the name of the program here", "days_a_week": "Days a week", "from_1_to_7": "from 1 to 7 days", "expected_classes": "Expected classes", "from_1_to_...": "from 1 to ...", "start_date": "Start date", "end_date": "End date", "goals:": "Goals:", "ao_aluno": "to the member", "aos_alunos": "to the members", "just_one_member": "Just one member at a time", "you_can_only_add_a_workout": "You can only add a workout to one member at a time.", "facil_recognition": "Facial recognition", "members_goals": "Member's goals", "no_goals_set": "No goals set", "challenge": "Challenge", "add_a_new_sheet": "Add a new sheet to the workout program, if the member still doesn't have one create a new one then add a new sheet.", "skip": "<PERSON><PERSON>", "here_you_can_edit_info_member": "Here you can edit the member's basic info.", "want_to_invite_someone": "Do you want to invite someone to download the app? Click here.", "do_you_want_to_get_in_touch": "Do you want to get in touch with a member? Click here.", "here_are_the_members_goals": "Here are the members' goals, you can change them or add new ones by clicking edit.", "check_the_current_workout_program": "Check the current workout program, if the member doesn't have one you can create.", "the_historic_of_workout_executions": "Your member's historic of workout executions.", "here_are_all_the_remarks_you_added": "Here are all the remarks you added.", "execution_history": "Executions History", "the_member_hasnt_performed_a_workout": "The member hasn't performed a workout yet.", "there_are_no_remarks": "There are no remarks for this member", "30_days": "30 days", "15_days": "15 days", "7_days": "7 days", "horario_share_treino": "{horarioUs}", "save": "Save", "choose_if_you_want_to_start": "Choose if you want to start your plan right now or if you want to schedule it so you can prepare yourself.", "now": "Now", "schedule_it": "Schedule it", "recipes_confirmed": "Recipes confirmed", "confirm_all": "Confirm all", "confirm_recipes": "Confirm recipes", "dont_like_it": "Don't like it", "like_it": "Like it", "today": "Today", "meals_plan": "Meals plan", "switch_recipe": "Switch recipe", "according_to_your_meals_plan": "According to your meals plan, check other recipes to help you reach you goal.", "you_can_only_select_recipes": "You can only select {} recipes at a time. Uncheck the selected ones so you can check other.", "your_plan_doesnt_allow_classes_this_period": "This class is not included in your plan's current time. Do you want to use one of your trial classes?", "you_dont_have_access_to_this_class": "You don't have this modality in your plan. Do you want to use one of your trial classes?", "this_class_is_not_from_this_company": "This class is not from this unit", "unable_to_check_out_this_class": "Unable to check-out this class.", "erro_nao_tem_modalidade": "Oops! You don't have access to the service you're trying to schedule. For more information, please contact your unit.", "erro_marcar_aula_com_antecedencia": "You can't check-in this class yet. Try again when it's closer for it to start.", "erro_confirmando_presenca_aluno": "Unable to add member to this class", "erro_aluno_nao_possui_autorizacao": "Access to this class denied", "nao_pode_desmarcar_mais": "It's too late for you to check-out this class", "sem_permissao_para_add_em_aula_iniciada": "You are not allowed to add a member to a class that has already started/finished", "erro_checkin_aula": "There has been an error checking in this class", "erro_ja_foi_marcada_uma_aula": "It has been already scheduled a class this day for the same modality. The plan doesn't allow more from this modality.", "erro_atingiu_numero_de_aulas_semanais": "The limit of scheduled classes from this modality has already been reached.", "select_a_reason_to_continue": "Select a reason for vacation to continue", "error_scheduling_class": "Unable to schedule this class. Please go to the gym desk and try to do your check-in.", "erro_alterar_serie": "There has been an error", "falha_ferias": "Unable to take a vacation", "falha_cartao": "Unable to generate a token for the card", "error_checking_in_class": "There has been an error checking in this class", "unable_to_process_this_request": "Unable to process this request", "unable_to_process_this": "Unable to process this", "dinners": "Dinners", "no_member_found": "No member found", "frequency": "Frequency", "no_method_selected": "No method selected", "options": "Options", "your_sheet_has_been_deleted": "Your sheet has been deleted", "snacks": "Snacks", "important": "Important", "there_are_no_notes_yet": "There are no notes yet", "you_can_add_a_new_note_below": "You can add a new note below", "write_your_note": "Write your note", "ocorreu_um_problema_comentario": "There has been a problem processing your note. Pull down to update and try again.", "up_to_date": "up to date", "total_a_fazer": "total without workouts", "renewals": "renewals", "new_members": "new members", "from": "from ", "data_aula_formatada": "{dataIngles}", "ao_vivo": "live", "locale_data": "en", "hello_user": "Hello, {}", "cross": "Cross", "read_more": "Read more", "meals_app": "Meals", "partnerships_app": "Partnerships", "manage_your_classes": "Manage your classes", "next_classes": "Next classes", "warning_no_classes_today": "There is no classes for today", "see_classes": "See classes", "falha_carregar_aulas": "Couldn't load \nclasses list", "falha_servidor": "The server is taking a long time to respond. Wait a few moments and try again.", "agendar_montar_aulas": "There are no scheduled classes for the provided date. Access your classes and manage your next class.", "agendar_aulas": "Schedule", "item_refeicao_tela_inicial": "Accelerate your results \nwith the best diet for \nyour body type!", "monitor_passos": "Steps", "editar_app": "Edit", "meta_diaria": "Daily goal", "k_cal": "kcal", "kcal_queimadas": "Burned", "ative_agora": "Activate right now!", "text_banner_monitor_passos": "Our body wasn't made to stand still, just walk for a few minutes and keep your body active", "ativar_app": "Activate", "definir_meta_diaria": "What will be your daily goal?", "inserir_quantidade": "Enter the quantity here", "passos_app": "Steps", "oms_passos": "Tip: According to WHO, the recommended amount is 8000 (eight thousand) daily steps.", "salvar_app": "Save", "treino_dia": "Workout of the day", "Compartilhar_resultado": "Share result", "treino_concluido": "Workout completed", "ex_app_singular": "{} Exercise", "ex_app_plural": "{} Exercises", "alternado_app": "Alternated", "dias_semana": "Week days", "o_aluno_nao_tem_modalidade": "The member doesn't have access to the classes", "check_in_nao_permitido_nesse_periodo": "Operation not allowed. Period allowed to schedule class in the class {mensagemIngles}", "navibar": {"inicio": "Home", "feed": "Feed", "aulas": "Classes", "alunos": "Members", "treino": "Workout", "cross": "Cross", "emcasa": "At Home", "agenda": "Schedule", "nutri": "Nutrition", "perfil": "My Profile", "treino_casa": "Workout at home", "chat": "Cha<PERSON> with personal trainer"}, "tela_feed_strings": "tela_feed_strings", "minhas_pubs": "My posts", "instrucao_feed": "You can post at any time using the feed", "sem_pubs": "The feed is empty", "load_falha": "Failed to load", "feed_falha": "The feed cannot be loaded", "internet_off": "No internet connection", "item_feed_strings": "item_feed_strings", "comentar_item_feed": "Comment", "rm_post": "Remove this post", "atencao_app": "Attention", "rm_validacao": "Do you want to remove this post?", "rm_texto": "Remove", "rm_falha": "Was not possible complete this action, try again later", "comentar_texto": "Comment", "denunciar_texto": "Report", "bloquear_usuario_texto_args": "Block {}", "bloquear_usuario_texto": "Block", "bloquear_usuario_aviso": "Would you like to block this user? You will no longer receive posts from this user", "o_que_gostaria_de_fazer": "What would you like?", "cancelar_texto": "Cancel", "menu_perfil": {"configurar": "Settings", "cadastrar": "Register Wod", "matricula_args": "Registration: {}", "local": "I'm at:", "minha_conta": "My account", "minhas_publicacoes": "My posts", "sobre": "About the app", "aviso_logout": "By clicking on logout you will no longer be logged into the app and you unsaved data will be lost.", "sair": "Logout", "deseja_deslogar": "Do you want to logout?", "sair_app": "Logout", "meus_contratos": "My contracts", "avaliacao_fisica": "My physical avaliation", "avaliar_professor": "Rate instructor", "campanhas": "Campaigns In-App"}, "tela_turmas_strings": "tela_turmas_strings", "lista_filtros": {"todos": "All", "boxe": "Boxing", "crossfit": "Crossfit", "natacao": "Swimming", "spinning": "Spinning", "pilates": "Pilates", "yoga": "Yoga", "zumba": "<PERSON><PERSON>"}, "titulolocalization": {"aulas_turmas": "Lessons and Classes", "aulas": "Classes"}, "string_pesquisa": "Search by lessons or by modality", "aviso_erro": "Couldn't load \nclasses list", "aviso_timeout": "The server is taking a unexpected long time to respond. Try again later", "pausa_treino": "Taking a break from workout?", "contrato_pausado": "Your contract is on hold. In the meantime, it is not possible to mark, unmark or search classes. If you have more than one contract, try consulting the classes with another one.", "meu_contrato": "My contract", "erro_pesquisa_aulas_titulo": "Ops, we didn't find \nany class!", "erro_pesquisa_aulas_mensagem": "Check the selected filters or typos, then try again", "string_repor_aulas": " You have {} credit{}", "string_repor_aulas_turma": " You have {} classes to replace", "sem_aula_hoje": "Ops, there are no classes for today!", "sem_aula_hoje_mensagem": "We didn't find any classes for today, try searching on another date.", "calendario": "Calendar", "trocar_unidade": "Change gyn", "detalhes": "Details", "confirmar": "Confirm", "continuar": "Continue", "checkin_cancelado": "Checked-out successfully!", "cancelar_aula_aviso": "Are you sure do you want to cancel your attendance in this class?", "cancelar_checkin": "Check-out", "voltar": "Go back", "vaga_garantida_aula": "Your spot is guaranteed in this lesson, a QR Code has bem generated, present it to your instructor to confirm your attendance", "vaga_confirmada_aula": "All right, your spot has been confirmed and is guaranteed in this lesson", "ver_qrcode": "View QR Code", "checkin_sucesso": "Checked-in successfully!", "utilizar": "Use", "turma": "Class", "checkin": "Check-in", "professor": "<PERSON><PERSON><PERSON><PERSON>", "erro_carregar_alunos": "We couldn't load the member list", "tentar_novamente": "Try again", "alunos_matriculados": "+ {} members enrolled in this lesson", "disponivel": "Available", "ocupado": "Unavailable", "notificacoes": "Notifications", "seu_programa_treino_esta_vencido": "Your training program is expired. Contact your instructor to renew it", "seu_programa_de_treino": "Your training program", "em_execucao": "Running now...", "ver": "View", "fichas": "Your trainings", "treinos_realizados": "Completed trainings", "concluido": "Finished", "ate_args": "{} until {}", "treinos_previstos": "All workouts", "cartao_credito": "Credit card", "boleto": "Bank slip", "debito_em_conta": "Debit card", "tela_configuracoes": {"titulolocalization": "Settings", "configuracoesPersonal": {"tema": "Theme", "modos": {"sistema": "System", "dark": "Dark", "light": "Light"}, "descanso": "Break", "descanso_info": "Enable break time between sets in training", "medida": "Weight unit of measurement", "medida_info": "Select the application's default \nWeight unit of measurement", "aviso_sonoro": "Audible alert", "aviso_sonoro_info": "Play a sound alert whenever an exercise is completed", "countdown_info": "Countdown before finishing the exercise timer", "meus_dados": "My data", "meus_dados_info": "Allow other users to view data such as your height, weight, and age", "status_de_atividade": "Activity status", "status_de_atividade_info": "Allow other users to view your activity"}, "titulo_widget_tema": "Choose a theme"}, "unidade_medida": {"quilos": "Kilograms (Kg)", "libras": "Pounds (Lbs)"}, "tela_treinosModelo": {"mensagem_erro": "Was not possible load some basic informations, try again later", "texto_acao": "Ok", "titulo_appBar": "Workouts and Programs", "sem_fichas": "No pre-defined sheets", "nada_encontrado": "Nothing found by the searching key", "erro_filtros_aplicados": "There are no sheets for the applied filters", "criar_novo": "Click on + button to create a new one", "limpar_filtros": "Clean Filters", "sucesso": "Sucess!", "ficha_preDefinida": "The pre-defined sheet was removed", "modelo_treinoEmUso": "This model belongs to a training plan in use and cannot be remove", "erro_informacoesBasicas": "Was not possible load some basic informations, try again later", "nehuma_ficha_disponivel": "Ops, we didn't find \nany avaliable records", "msg_nenhuma_ficha": "You can try search by another term or select diferent filters."}, "tela_filtrosTreino": {"erro_mensagem": "Failed to query filters", "tentar_novamente": "Try again", "titulo_appBar": "Filter workouts", "sexo": "Gender", "sexos": {"masculino": "Male", "feminino": "Female", "nao_informado": "uninformed"}, "nivel": "Level", "categoria": "Category", "objetivos": "Goals"}, "tela_manterTreino": {"titulo_true": "Edit sheet", "titulo_false": "New sheet", "dados_basicos": "Basic data", "nome_ficha": "Sheet name", "ficha_hipertrofia": "Ex: Hypertrophy Sheet", "obs": "Note", "complementar_atividades": "Ex: Try to do all exercices", "tipo_execucao": "Type of execution", "escolha_dias": "Choose the days"}, "tela_campanhas": {"vigencia": "Validity:", "horariostr": "Show between:", "status_args": "Campaign status: {}", "erro_consulta": "Was not possible load that information", "titulolocalization": "No Ad-InApp", "mensagem": "There is no Ad's In-App"}, "tela_manterInApp": {"titulolocalization": "{} Campaign", "titulo_erro": "Was not possible show bacause:", "campanha_ativa": "Active campaign", "passos": {"1": "Step 1: Image upload, send square images", "2": "Step 2: Set campaign name", "3": "Step 3: Define the frequency and quantity of ? per user", "4": "Step 4: Link/screen to open", "5": "Step 5: set campaign period", "6": "Step 6:, When must be displayed", "7": "Step 7:, Display events", "8": "Step 8: In App public", "3_tip": "Deixe como 0 para ilimitado, quando for por click outros no mínimo 1 click", "extra": "(Extra) Blocking companies/showing only in companies"}, "inicio": "Beginning", "fim": "End", "range": "New Range", "mostrar": "Show between", "mensagem_erro": "At least one range must be kept", "eventos": "Select event", "mostrar_evento": "Show in event:", "publico": "Target audience:", "mostrar_aoPublico": "Show to the public:", "bloquear": "BLick In-App in companies", "add_cliente": "Add app client", "erro_mensagem": "When there are released apps it is not possible to select apps to block", "erro_mensagem_liberar": "When there are apps blocked it is not possible to select apps to release", "mostrar_app": "Show in app:", "mostrar_apenas": "Mostrar apenas in App nas empresas", "inApp_sucesso": "In App mantido com sucesso", "salvar_campanha": "Save campaign", "cadastrar": "Register"}, "tela_perfil": {"graduacao": "Graduation", "avaliacao": "Physical avaliation", "titulolocalization": "My Profile", "codigo": "Code: {}", "idade": "{} years old", "pontuacao": "Score {}", "resumo_avaliacao_fisica": "Physical avaliation summary"}, "tela_progresso_graduacao": {"titulolocalization": "Progress", "atual": "Current", "erro_progresso": "Failed to query progress", "tente_novamente": "Pull down to try again", "graduacao_nao_ativada": "Gradation module not activated", "erro_graduacao": "We haven't found any active gradutions recently"}, "info_IMC": {"indice_massa_corporal": "Body mass index (BMI)", "seu_imc_args": "Seu IMC: {}", "texto_imc": "The Body mass index (BMI) is a measure of body fat based on your height and weight", "abaixo_peso": "Under weight", "peso_normal": "Normal weight", "sobrepeso": "overweight", "obesidade": "Obesity"}, "tela_detalhes_assinatura": {"titulolocalization": "My subscriptions", "planos": {"id": "Plan: {}", "data_inicio": "Subscription made in {}", "data_fim": "Subscription valid until {}", "ativa": "Active Subscription: {}"}, "sim": "Yes", "nao": "No", "erro_consulta": "Failed do querry subscriptions", "erro_historico": "No purchase history avaliable so far", "consulte_mais_tarde": "Try again later", "conhecer_planos": "Know our plans"}, "altura": "Height", "peso": "weight", "gordura": "fat %", "retornar_texto": "Go back", "tela_detalhes_contrato": {"msg_compra_plano": "To be able to train and enjoy your app with everything your gym has to offer, choose a plan to get started.", "titulo_compra_plano": "Get ready to get started!", "titulolocalization": "Contracts", "vigencia_contrato": "Contract period", "titulo_modal": "Select a contract", "codigo": "code", "erro_contratos": "Failed to query the contracts", "titulo_sem_contratos": "No contracts", "msg_sem_contratos_args": "You haven't signed any contract with your gym yet. {}", "value": "Sign contract", "titulo_dialog_ferias": "Back from{} {}", "voltar_do": "Back from {}", "msg_dialog_ferias": "When you return, your contract status will be changed to active", "sucesso_solicitacao": "Sucess!", "ferias_agendadas": "Vacation Scheduled", "voltar_ativo": "When you return, your contract status will be changed to active", "trancamento_agendado": "Break Scheduled?", "parcelas": "Installments", "_sem_parcelas": "No installments", "parcela_args": "installments {}", "validade_args": "Validity: {}", "valor_base": "Contract base value", "contrato": "Contract value", "valor_nao_informado": "Value not informed", "duracao_contrato": "Contract period", "dias_args": "{} days", "horario": "Timetable", "condicao_pagamento": "Payment conditions", "modalidade": "Modality", "vez_args": "{} Time{}", "trancamento": "Take a time?", "ferias": "Vacation", "renovar": "<PERSON>w", "credito": "Credit", "acoes_rapidas": "Quick Actions"}, "valor_contrato": "Contract amount", "duracao": "Duration", "valor": "Amount", "validade": "Validity", "assinatura_ativa": "Active subscription", "assinatura_inativa": "Inactive subscription", "assiantura_feita_em": "Signature made at:", "assinatura_valida_ate": "Signature valid until:", "tela_trancamento_contrato": {"titulolocalization": "Contract break", "infos_contrato": "Contract information", "num_contrato": "Contract number", "plano": "Plan", "preco_contrato": "Contract Price", "dias_contrato": "Contracted days", "dias_bonus": "Extra days", "configurar": "Break Settings", "selecione_produto": "Select product", "motivo_trancamento": "Select a reason for break", "aviso_trancamento": "Your contract will be break tomorrow", "aviso_erro_produto": "It's necessary to select a product", "aviso_erro_motivo": "You need to select a reason for breaking", "avancar": "Next", "falha_consulta_trancamento": "Failure to consult breaking"}, "dropDown_titulo": "Select an option", "tela_renovar_contrato": {"erro_plano_alterado": "The plan has been changed and is no longer compatible for renewal through the app.", "verifique_o_balcao": "Please contact the front desk for more information", "titulolocalization": "Renewal Simulation", "aviso_renovacao": "The negotiation related to this operation will be exactly the same as the previous contract, maintaining the payment terms, duration, modalities and all other items referring to the contract in question. If you have any questions, please contact your gym", "sucesso_renovacao": "Your contract has been successfully renewed", "confirmar_renovacao": "Confirm <PERSON>wal", "falha_simular_renovacao": "Failed to perform a renew simulation"}, "tela_contrato_ferias": {"titulolocalization": "Vacation", "configurar_periodo": "Setting vacation period", "periodo_ferias": "Enter the period you want to take a vacation", "motivo_afastamento": "Select a reason for vacation", "descricao": "Description", "aviso": "By confirming your vacation, you declare that you are aware of and agree to all the conditions described in the", "termos_condicoes": "Terms & Conditions", "sucesso_solicitacao": "Your vacation request was successfully request.", "confirmar": "Confirm vacation", "falha_consulta": "Failed to query vacations", "ferias_info": "Vacation information", "dias_permitidos": "Days allowed for vacation", "dias_utilizados": "Days used", "dias_restantes": "Days left", "dias_minimos": "Minimum days for vacation"}, "notificacoes_novo": "What's new?", "pensando": "What's happening?", "botao_denunciar_post": "Report post", "aviso_denuncia": "This post will be analyzed and, if we find any violations, we will remove it and notify the user.", "tela_denunciar_titulo": "Report", "tela_comentario_titulo": "Comments", "comente_algo": "Comment your reply", "comecar_treino": "Start training", "finalizar": "Finish", "finalizar_execucao": "Do you want to finish the execution of this training?", "finalizar_treino": "Finish trainning", "fim_execucao": "Finish?", "equipamentos": "Equipments", "sem_equipamentos": "No Equipments", "preparese": "Get ready", "sem_notificacoes": "No avaliable notifications yet", "tela_sobre": {"titulo": "About", "about_args": "{} it's a tool that will assist you in your fitness goals, through which you'll have complete control over your workouts and access to a multitude of tools that will ensure you achieve a better quality of life.", "politica_privacidade": "Privacy politic", "topicos_politica_privacidade": "See all our privacy politic topics", "versao": "Version", "nome_empresa": "WAGI Technology LTDA", "biliotecas": "Open source libraries", "bibliotecas_utilizadas": "Libraries that we use"}, "modais_agenda": {"o_que_deseja_fazer": "What do you want to do?", "aviso_reagendar_desmarcar": "You can reschedule or cancel this appointment", "reagendar": "Reschedule", "desmarcar": "Cancel", "confirmar_agendamento": "Do you want to confirm the appointment?", "aviso_confirmacao": "By clicking in confirm, you will be added to the appointment"}, "tela_agendar": {"titulolocalization": "Schedule"}, "dias_horararios_disponiveis": " Days with avaliable scheduling", "erro_horarios_disponiveis": "OPs, we didn't find any avaliable scheduling", "tente_selecionar_outra_data": "Try using another date", "aulasE_turmas": {"selecione_numero_contato": "Select the contact number", "telefone": "Phone", "launchWhatsApp_message_args": "Hello {}, this is {}", "novo_modelo_confirmacao": "New confirmation model", "aviso_confirmar_presenca_qrcode": "Now you can confirm your member attendance by QR Code", "confianca": "Confidence ", "novo_modelo_alunos_presentes_confirmado": "With the new model, only present members will be confirmed", "permissao_camera": "Camera permission", "subtitulo_permissao_camera": "To use the feature, we need camera acess", "recurso_indisponivel": "Feature unavailable", "necessario_permissao_camera": "The feature needs camera permission and access to cell phone storage to be used", "encerrar_ou_continuar_confirmando_presencas": "Do you want to close or continue confirming attendance?", "continuar_confirmando": "Keep confirming", "encerrar": "Close", "presenca_confirmada": "Attendance confirmed sucessfully", "posicione_camera": "Position the camera", "aponte_camera": "Point the camera at the member's QR Code and wait for it to read", "deseja_cancelar_args": "Do you want to remove {} from the class?", "sim_desmarcar": "Yes, unmark", "escolha_data": "Pick a date", "turmas_agendadas": "Schedule classes", "selecione_unidades_args": "Select one of the {} units", "add_a_args": "{} has been added to {}", "add_aluno": "Add", "add_aluno_aula": "Add", "pesquise_por_nome": "Search by name", "sem_alunos": "No available", "sem_alunos_encontrados": "No members finded", "aulas_agendadas": "Schedule classes", "apresente_qrcode_confirme_presenca": "Present this code to your instructor\nto confirm your attendance to the class", "confirmar_presenca_titulo": "Confirm attendance", "não_encontramos_alunos": "Ops, we didn't find \nany member", "minhas_aulas_args": "My classes", "em_andamento": "In progress", "proximas_aulas": "Next Classes", "finalizadas": "Finished", "sem_aulas_turmas_dia": "No classes for today", "puxe_para_baixo_att": "Pull down to update"}, "confirmar_presenca": "Confirm member attendance", "tela_avaliacao_agendamento": {"titulolocalization": "Scheduling", "selecione_horario": "Arrange an appointment"}, "tela_avaliacao_fisica": {"titulolocalization": "Physical avaliation", "ultima_avaliacao_args": "last avaliation done in {}", "proxima_avaliacao_args": "Next physical evaluation scheduled for {}", "agendar_avaliacao": "Schedule Avaliation", "resumo_avaliacao": "Avaliation's overview", "falha_consulta_avaliacoes": "Falha ao consultar avaliações", "nao_possui_avaliacoes_args": "{} does not have physical evaluations", "itens_dropdown": {"direito": "Right", "esquerdo": "Left", "outros": "Others"}, "perimetria": "Perimetry", "dobras_cutaneas": "Skin Folds", "resistencia_muscular": "Muscle endurance", "bracos": "Arms", "abdomen": "Abdomen", "imc": "Body mass index (BMI)", "resultado": "Results", "situacao": "State", "comparativo_pesos": "Weight comparison", "peso_atual": "Current weight", "peso_ideal": "Ideal weight", "peso_gordo": "Fat weight", "peso_magro": "Thin weight", "composicao_corporal": "Body composition"}, "tela_detalhes_agendamento": {"titulolocalization": "Available hours", "confirmar_agendamento_args": "When confirming the appointment, a portion will be generated in the amount of {}", "sucesso_agendamento": "<PERSON><PERSON>uling performed successfully", "marcar": "<PERSON>"}, "tela_feedback": {"como_esta_sendo_sua_xp_com_app": "How is your experience with your app?", "isso_ajudara_entregarO_melhor_para_vc": "This will help to always deliver the best for you", "gostaria_de_deixar_um_comentario": "Would like to leave a comment?", "responder_depois": "Answer later", "seu_feedback_e_importante_para_nos": "Your feedback is important for us", "enviar_feedback": "Send Feedback", "o_que_esta_errado": "What is wrong?", "como_podemos_melhorar": "How can we do better?", "motivos": {"suporte": "Suport", "treinos": "Workouts", "lentidao": "Slowness", "travamentos": "Crashes", "usabilidade": "Usability", "outros": "Others"}}, "avaliar_professor": {"selecione_professor": "Select one instructor", "alterar_professor": "Switch instructor", "orientacao_avaliacao": "The rating stars must be between 0 and 5", "sua_avaliacao_foi_salva": "Your rating was done successfully :)", "titulo_lista_professor": "Select an instructor", "pesquise_por_nome": "Search by name", "nenhum_professor_encontrado": "No instructor found"}, "badges": {"titulolocalization": "30 days completed", "medalha_30_dias": "Medal obtained for training 30 consecutive days at the gym."}, "beber_agua": {"informe_quantidade": "Enter the amount", "titulo_tela_beber_agua": "Drinking water", "vamos_comecar": "Let's start", "configurar_meta_diaria": "To use Drinking Water you need to set your daily goal. Let's start?", "vamos_la": "Let's go!", "objetivo_args": "Goal: {}", "estatisticas_semana": "Week statistics", "data_nascimento": "Birth date", "acorda_as": "Wake up at", "sua_meta_diaria": "Your daily goal", "meta_diaria_indicada_args": "The recommended daily goal is {}", "ativar_lembretes": "Enable reminders?", "o_app_ira_exibir_lembretes": "The app will send reminders to remember you to drink water", "horarios_lembretes": "Reminder time band", "intervalo_minimo_lembretes": "Minimum interval of reminders", "beber_agua_esta_pronto": "The Drinking Water is ready to use", "tela_configurar_beber_agua_titulo": "Statistics", "tela_configurar_beber_agua_subtitulo": "Weekly consumption report", "hidratacao": "Hydration", "semana": "Week", "meta_semanal": "Week goal", "consumido": "Consumed", "sem_estatistcas": "No statistics yet"}, "editarColaborador": {"tela_minha_conta_titulo": "My account", "trocar_foto_perfil": "Change profile photo", "informacoes": "Information", "nome": "Name", "nome_de_usuario": "Username", "codigo_empresa": "Company code", "formas_de_pagamento": "Payment methods", "meios_registrados": "Payment methods registered", "nenhum_meio_registrado": "No payment methods registered", "add_novo_cartao": "Register a new credit card", "nao_foi_possivel_carregar_os_dados": "It was not possible to load the data", "verifique_conexao_tente_novamente": "Check your internet connection and pull down to try again", "conta_agencia_banco_args": "Acc: {} Ag: *** *** ***", "tela_manter_senha_titulo": "Change password", "senha_nao_pode_ser_vazia": "The password cannot be empty", "senha_nao_bate": "The password entered does not match with the password confirmation", "senha_atual_deve_ser_inserida": "The current password must be typed", "senha_alterada_com_sucesso": "Password changed successfully", "senha_atual": "Current password", "nova_senha": "New password", "confirmacao_nova_senha": "New password confirmation", "tela_dados_contato_titulo": "Contract data", "email_deve_ser_valido": "The e-mail must be valid", "telefone_deve_ser_valido": "The cellphone number must de valid", "dados_alterados_com_sucesso": "Data changed successfully", "cadastrar_cartao": "Register card", "editar_cartao": "Edit card", "meu_cartao_de_credito": "My credit card", "numero_cartao": "Card number", "nome_impresso_cartao": "Card holder's name", "adica_edica_cartao_realizada_args": "The {} of the credit card was performed", "tela_editar_perfil_titulo": "Edit profile", "trocar_foto": "Change photo", "digite_nome_aluno": "Type member name", "nome_aluno": "Name", "digite_email": "Type the e-mail", "e-mail": "E-mail", "numero_telefone": "Cell phone number", "celular": "Cell phone", "data_nascimento": "Birth date", "tudo_pronto": "All ready", "dados_editados_sucesso": "All data was successfully changed", "tela_creditos_contrato_titulo": "Contract credits", "sem_extratos_para_o_filtro": "No extracts for applied filter", "tela_confirmar_trancamento_titulo": "Confirm break", "infos_trancamento": "Break informations", "taxa_trancamento": "Break fee", "dias_trancados": "Breaked days", "dias_usados": "Used days", "contrato_trancado_sucesso": "Your contract has been successfully broken", "confirmar_trancamento": "Confirm break", "tirar_foto": "Take a photo", "escolher_foto": "Choose a photo", "remover_foto": "Remove a photo", "data_nasc": "Birth date."}, "editar_perfil": {"tela_editar_perfil_titulo": "Edit profile", "dados_basicos": "Basic Data", "dados_corporal": "Body information", "dados_pessoais": "Personal Data", "data_nascimento": "Date of Birth", "senha_atual": "Current Password", "nova_senha": "New Password", "confirmacao_nova_senha": "Confirmation of New Password", "senhas_nao_coincidem": "Passwords do not match", "e-mail": "Email", "insira_email_valido": "Enter a valid email", "celular": "Cellphone", "telefone": "Phone", "adicionar_cartao_credito": "Add Credit Card", "adicionar_cartao": "Add card", "editar_cartao": "Edit card", "erro_ao_solicitar_servico": "An error occurred while requesting the service", "sexo_biologico": "Biological Sex", "vencimento": "Expiration date", "nome_impresso_cartao": "Card holder's name", "data_vencimento_formato_invalido": "Expiration date is invalid", "data_vencimento_ja_expirou": "Expiration date has already expired", "treinos_por_semana": "Workouts per week"}, "crm": {"titulo_tela_detalhes_notificacoes": "Visualize", "sua_resposta": "Your reply", "preciso_texto_para_enviar_como_resposta": "It's necessary to put some text to send as a reply"}, "conversorDeMassa": {"titulolocalization": "Converter", "converta_kg_lbs": "Convert Kg to Pounds and vice versa", "kg": "Kilogram (Kg)", "lbs": "Pounds (Lb)"}, "chat": {"voce_pode": "You can start a new chat at any time.", "sem_conversas": "No chats", "tela_contatos_chat_titulo": "New chat", "nao_ha_resultados": "No results", "pesquise_por_outro_nome": "Search by another name"}, "cadastro_de_aluno": {"editando_aluno": "Editing member", "cadastrar_aluno": "Register member", "foto": "photo", "trocar_foto": "Change photo", "aluno_ativo": "Inactivated member", "se_desmarcado": "If unchecked the member will be inactivated", "os_campos_com": "Fields with * are required", "convite": "Let's invite {} to use your app", "convidar_via": "Invite by:", "salvar_edicoes": "Save changes", "sexo": "* Gender", "tela_manter_objetivo_titulo": "Pick a goal", "erro_conculta_objetivos": "Oops! it was not possible to consult the objectives", "sem_objetivos": "No goals registered", "objetivo_aluno_salvo": "Member goal has been saved", "salvar_objetivo": "Save goal"}, "pasta_contrato": {"titulo_form_dados_cartao": "Enter billing details", "num_cartao": "Card number *", "titular_cartao": "Cardholder *", "nome_que_consta_no_cartao": "Type the name on the card here", "cpf_titular": "Cardholder ID", "cpf_invalido": "Invalid ID", "validade": "due date *", "mes_ano": "Month / Year", "validade_invalida": "Invalid invoice due date", "digite_aqui": "Type here", "campos_obrigadorios": "Fill the fields with * to proceed", "infos_adicionais_titulo": "Additional information", "legenda": "Confirm and enter the necessary data", "vencimento_fatura": "Invoice", "selecione_dia_do_venvimento": "Invoice due date", "todo_dia": "Every day", "cupom_desconto": "Discount coupon", "form_dados_pessoais_titulo": "Enter the data", "nome_completo": "Full name", "data_nascimento": "Birth date", "cpf": "ID", "email_exemplo": "<EMAIL>", "num_celular": "phone number", "todos_os_campos_com_sao_obrigatorios": "All fields with * are mandatory", "resumo_seu_pedido": "Summary of your order", "plano_selecionado": "Selected plan", "1_parcela": "First parcel", "moeda_mes_args": "R$ {} / month", "duracao_contrato_args": "{} days", "1_parcela_args": "R$ {}", "valor_matricula_args": "R$ {}", "valor_anuidade": "R$ {}", "detalhar_parcelas": "Detail parcels", "ocultar_parcelas": "Hide parcels", "parcela_args": "{}° installment {}", "dados_pessoais": "Personal data", "num_de_telefone": "Phone number", "dados_cartao": "Card information", "tipo_cobranca": "Billing Type", "recorrencia_mensal": "Monthly recurrence", "ativar_agora": "Activate now", "li_e_aceito_os_termos_do_contrato": "I have read and accepted the contract terms", "leia_contrato": "Read the contract for this plan", "erro_acao": "Was note possible complet this action, try again later", "deseja_abandonar_compra_de_plano": "Do you want to abandon the plan purchase?", "passo_args": "Step {} of 4", "informe_dados_pessoais": "Enter the necessary data", "tela_contrato_do_plano_titulo": "Plan contract", "erro_consulta_contrato": "It was not possible consult the contract", "erro_tente_novamente": "We had a problem to consult the contract, pulldown and try again", "TelaOpcoesDeCompraNovoContrato_titulo": "Choose the best plan", "essas_sao_as_acoes": "These are your gym plan options. Choose the plan you want!", "erro_consulta_dados": "It was not possible consult the data", "sem_planos": "No plans", "nao_ha_planos_disponiveis": "Currently there are no plans available to purchase", "mes": "/ month", "duracoes": "Duration of {} months", "duracao": "Duration of {} month", "anuidade_args": "Annuity {}", "meses_arg": "{} month", "meses_args": "{} months", "esse_plano_inclui": "This plan includes", "com_esse_contrato": "With this contract you acquire {} credits. Check with your gym the conditions of use.", "escolher_esse_plano": "Pick this plan", "parcelas_vencidas": "Overdue", "valor_dolar_real": "$ {}", "total": "Total", "pagar_parcelas": "Pay installments", "valor_total": "Total amount", "pagamento_recusado_subtituloMensagem": "Verify your payment information and try again.", "pagamento_recusado_tituloMensagem": "Oops, something went wrong…", "pagamento_aprovado_subtituloMensagem": "Now you don't have any overdue payments", "pagamento_aprovado_tituloMensagem": "Payment made successfully!", "pagar_agora": "Pay now", "que_sera_cobrado": "What will be charged", "forma_pagamento": "Payment method"}, "showDialog_contrato": {"subtitulo_vencido_naoRenovar": "Go to the reception to renew your plan and get a head start!", "subtitulo_vencido": "Renew your plan to get back to enjoying all that your gym has to offer!", "titulo_vencido": "You plan {} has experied!", "renove_agora": "Renew now", "outro_plano": "Choose another plan", "subtitulo_Avencer": "Remember to renew it so you don't lose access to your gym's services. You can do this whenever you want right from your profile.", "subtitulo_Avencer_naoRenovar": "Remember to renew it so you don't lose access to your gym's services. You can do this at the front desk or you can choose another plan to start with.", "titulo_avencer_dias": "Your plan {} will expire in {} days", "titulo_avencer_dia": "Your plan {} will expire in {} day", "titulo_avencer_hoje": "Your plan {} will win today", "subtitulo_visitante": "To be able to train and enjoy all that your gym has to offer, first of all you need to choose a plan to start with", "titulo_visitante": "Get ready to start!", "visitante_recepcao": "Go to your gym's reception and choose the best plan for you", "subtitulo_cancelado": "Come back with full focus on achieving a healthier life and choose a new plan.", "subtitulo_cancelado_desistente": "Come back with full focus on achieving a healthier life! Go to the reception and activate your plan.", "titulo_cancelado": "You plan has experied!", "conferirplano": "Check Plans", "escolha_seu_plano": "Choose your plan"}, "historico_parcela": {"em_aberto": "Open", "canceladas": "Cancelled", "pagas": "Paid", "histórico_parcela": "Installments history", "sem_parcela": "No Installments at this time", "text_sem_parcelas": "You have no share in this situation.", "parcela": "Installment", "validade": "validity"}, "drawer_esquerdo": {"ver_perfil": "View profile", "meu_dia": "My day", "minha_agenda": "My shcedule", "deseja_deslogar": "Do you want to logout?", "ao_clicar_em_sair": "By clicking on logout you will no longer be logged into the app", "deslogar": "Logout"}, "exercicios": {"desativar": "Disable", "mensagem_app_bar": "{} of workout", "sem_foto": "No photo", "sem_nome": "no Name", "anaerobico": "Anaerobic", "aerobico": "Aerobic", "com_args": "{} with {}", "da_biblioteca_de_atividades": "{}, from the activities library", "biblioteca_de_imagens": "Image library", "add_imagem_args": "Add {} image{} to exercise", "grupos_musculares": "Muscle groups", "selecione_os_grupos": "Select groups for exercise", "parece_que_vc_esta_sem_conexao": "It appears that you have no internet connection", "tipo_atividade": "Activity type", "treino_livre": "Free workout", "com_equipamento": "With equipment", "link_video_youtube": "Video link (Youtube)", "metodo_de_execucao": "Execution method", "fotos_e_videos": "Videos and photos", "remover_video": "Remove video?", "grupos_musculares_s": "Muscle groups", "sem_grupo_definidos": "No defined group", "exercicios.preencha_os_campos_corretamente": "Fill the fields and try again", "atividade_foi_salva": "Activity has been saved", "cadastrar_outra_atividade": "Register another activity", "remover_imagem": "Remove image?", "TODAS": "ALL", "todos": "All", "SQ": "NE", "CQ": "WE", "sem_equipamento": "Without equipament", "sem_exs_encontrados": "No exercises found", "tente_pesquisar_por_outro_termo": "Try search by another term", "mensagem_args": "Activity {} has been deactivated"}, "dasBoardColaborador": {"media_execucas_dos_treinos": "Average of training executions", "nos_ultimos_30_dias": "in the last 7 days", "resultados_ultimos_7_dias": "Results in the last 7 days", "porcentagem_realizados_args": "{} executed ", "treinos_executados": "Workouts executed", "porcentual_execucao_por_aluno": "Percentage of execution per student", "alunos_encontrados_args": "{} members finded ", "treinos_concluidos_args": "{} of {} conclued workouts", "todas_avaliacoes": "All ratings", "numero_telefone_aluno_invalido": "This student's phone number is invalid", "avaliacao_dos_treinos_ultimos_7_dias": "Workout rating\nin the last 7 days", "ola_args": "Hello, {}", "falha_consulta_dashboard": "Failed to query dashboard", "parabens_vc_concluiu_os_desafios": "Congratulations!\nYou completed all challenges", "seja_mestre_no_args": "Master the {}!", "realizar_proximo_desafio": "Take next challenge", "meus_programas_de_treino": "My workout programs", "a_renovar": "Renovate", "programas_de_treino": "Workout programs", "da_minha_carteira": "of my collection", "nivel_de_satisfacao": "Satisfaction Level", "presquicoes": "Prescriptions", "total_a_fazer": "Total to do ", "renovacoes": "Renewals ", "sem_treino": "No Workout ", "vc_possui": "You have ", "alunos_args": "{} members ", "feedback_dos_treinos": "Workout feedback", "em_dia": "Up to date"}, "tela_concluir_treino": {"vc_concluiu_seu_treino_de_hoje": "You've completed your workout of today!", "avalie_seu_treino_de_hoje": "Rate your workout of today", "comente_algo": "Comment something"}, "ainda_nao_ha_novidades": "There's no news yet, but keep watching so you don't miss anything", "sucesso": "Sucess!", "gordura_simples": "fat", "obrigado": "Thank you", "depois": "Later", "genero": "Gender", "ajuda": "Help", "acesso": "Access", "senha": "Password", "usuario": "User", "contato": "Contact", "celular": "Cell phone", "e-mail": "E-mail", "endereco": "<PERSON>ress", "bairro": "Neighborhood", "CEP": "Postal Code", "numero": "Número", "complemento": "Complement", "valiade": "Validity", "nome": "Name", "extrato": "Extract", "creditos": "Credits", "periodo": "Period", "convidar": "Invite", "proximo": "Next", "resumo": "Summary", "matricula": "Registry", "anuidade": "Annuity", "CPF": "ID", "desconto": "Discount", "contratos": "Contracts", "opcoes": "Options", "adesao": "Adhesion", "novo": "New", "atividade": "Activity", "adicionar": "Add", "selecionar": "Select", "atividades": "Activities", "aguardando": "Waiting", "em_dia": "Up to date", "vencidos": "Overdue", "revisados": "Revised", "execucoes": "Executions", "enviar": "Send", "parabens": "Congratulations", "check_your_position_in_the_rank": "Check your position in the ranking", "add_the_sheet": "Add the sheet", "the_sheet_has_been_added": "The sheet has been added", "titulo_tela_imc": "Body Mass Index", "seu_imc": "Your BMI:", "o_imc_e_uma_medida": "Body mass index (BMI) is a measure of body fat based on your height and weight.", "abaixo_do_peso": "Under weight", "peso_normal": "Regular weight", "baixo": "Low", "sobrepeso": "Overweight", "obesidade": "Obesity", "informe_a_altura": "Enter your height", "informacoes_nutricionais": "Nutritional information", "adequado_para_dietas": "Appropriate for diets", "essa_combinou_com_seu_plano": "Hey, this one matches your plan!", "possui_a_mesma_quantidade_calorica": "It has the same amount of calories that you need in your meal. Would you like to replace it?", "foods": "Foods", "favorites": "Favorites", "portion": "portion", "portions": "portions", "fibers": "Fibers", "adicionar_legenda": "Add label", "mark_as_important": "Mark as important", "the_note_cant_be_empty": "The note can't be empty", "novo_pr": "New PR", "todays_result": "Today's result", "rankings": "Rankings", "your_current_plan_will_be_stoped": "Your current plan will be discontinued and replaced by this one, do you want to continue?", "nutritional_content": "Nutritional content", "guidelines": "Guidelines", "plans_rating": "Plan's rating", "some_recipes": "Some recipes from the plan", "meals_a_day": "meals a day", "lasts_for_x_days": "It lasts\nfor {} days", "seg": "Mon", "ter": "<PERSON><PERSON>", "qua": "Wed", "qui": "<PERSON>hu", "sex": "<PERSON><PERSON>", "sab": "Sat", "dom": "Sun", "no_message": "No message", "new_terms_and_policy": "New terms and policy", "in_order_to_use_the_app_features": "In order to use all the app features you need to agree with the terms and conditions and the privacy policy.", "i_agree_with_the": "I agree with the ", "concordo_com_as": "I agree with the ", "terms_and_conditions": "Terms and conditions", "politicas_de": "Privacy policies", "privacy_policies": "Privacy policie", "accept": "Accept", "could_not_add_workout": "Could not add workout to the member. Try again", "could_not_delete_workout": "Could not delete the workout. Try again", "edit_result": "Edit result", "register_result": "Register result", "no_register_yet": "No register yet", "be_the_first_to_register_a_result": "Be the first one to register\na result in this wod", "register": "Register", "there_are_no_registers": "There are no registers for the selected time range and filters", "so_all_of_the_app_features": "To protect your privacy and ensure the security of your personal information, by continuing you agree to:", "data_processing": "Data processing", "Execucoes_de_treino": "Training runs", "manha": "Morning", "tarde": "Afternoon", "noite": "Night", "maior_movimentacao": "Most Crowded", "menor_movimentacao": "Least Crowded", "nenhuma_execucao": "No execution yet", "text_empty_execucao": "as soon as an aluco executes a card you registered it will appear here.", "class": "Class", "lista_presenca": "Attendance list", "nenhum_aluno": "No students registered yet", "ativos": "Active", "inativos": "Inactive", "agendadas": "Scheduled", "avaliacoes_em_dias": "Assessments carried out:", "ativos_sem_avaliacao": "Assets without evaluation", "there_are_no_events": "There are no events", "matricula_arguments": "Registration number: {}", "prof_abreviacao": "Trainer: {}", "no_trainer": "No personal trainer", "argument_years_old": "{} years old", "hello_argument": "Hey, {}", "get_in_touch": "Contact", "member_history": "Member history", "no_goals": "No goals", "objetivo": "Goal", "member_level": "Member level:", "member_level_formated": "Member level", "no_level": "No level", "levels": "Levels", "workouts_history": "Workouts history", "SEG": "MON", "TER": "TUE", "QUA": "WED", "QUI": "THU", "SEX": "FRI", "SAB": "SAT", "DOM": "SUN", "procure_por_nome_ou_matricula": "Search by name or registration", "escreva_sua_obs": "Write down your remark", "remark": "Remark", "realize_uma_nova_avaliacao": "Perform a new assessment to keep your data and goals always up to date.", "dias_que_treinou": "Workouts by day", "mensagem_do_professor": "Trainer's message", "treino_em_dia": "Workout up to date", "treino_vencido": "Workout expired", "treinou": "Workout done", "agendamento": "Schedule a service", "alterou_status": "Schedule status updated", "renovou_treino": "Workout renewed", "acabou_treino": "Workout finished", "notificacao": "Notification", "montou_treino": "Workout prescription", "mudou_de_nivel": "Level changed", "fez_aula": "Class done", "realizou_avaliacao": "Physical evaluation done", "registrou_wod": "WOD registred", "agendou_aula_booking": "Checked-in class via Booking", "massa_magra": "Lean mass", "massa_gorda": "Fat mass", "abdominal": "Abdominal", "supra_iliaca": "Suprailiac", "peitoral": "Chest", "triceps": "Triceps", "coxa_medial": "Medial thigh", "subescapular": "Subscapular<PERSON>", "axilar_media": "Mid axillary", "biceps": "Biceps", "antebraco_dir": "Right forearm", "braco_dir_relaxado": "Relaxed right arm", "braco_dir_contraido": "Contracted right arm", "coxa_media_dir": "Right middle thigh", "panturrilha_dir": "Right calf", "coxa_distal_dir": "Right distal thigh", "coxa_proximal_dir": "Right proximal thigh", "antebraco_esq": "Left forearm", "braco_esq_relaxado": "Relaxed left arm", "braco_esq_contraido": "Contracted left arm", "coxa_media_esq": "Left middle thigh", "panturrilha_esq": "Left calf", "coxa_distal_esq": "Left distal thigh", "coxa_proximal_esq": "Left proximal thigh", "pescoco": "Neck", "ombro": "Shoulder", "quadril": "Hip", "torax_busto": "Chest / Relaxed bust", "cintura": "Waist", "gluteo": "Gluteal", "circunferencia_abdominal": "Abdominal circumference", "segunda_manha": "Monday morning", "segunda_tarde": "Monday afternoon", "segunda_noite": "Monday night", "terca_manha": "Tuesday morning", "terca_tarde": "Tuesday afternoon", "terca_noite": "Tuesday night", "quarta_manha": "Wednesday morning", "quarta_tarde": "Wednesday afternoon", "quarta_noite": "Wednesday night", "quinta_manha": "Thursday morning", "quinta_tarde": "Thursday afternoon", "quinta_noite": "Thursday night", "sexta_manha": "Friday morning", "sexta_tarde": "Friday afternoon", "sexta_noite": "Friday night", "sabado_manha": "Saturday morning", "sabado_tarde": "Saturday afternoon", "sabado_noite": "Saturday night", "domingo_manha": "Sunday morning", "domingo_tarde": "Sunday afternoon", "domingo_noite": "Sunday night", "minha_carteira": "My wallet", "na_academia": "At the gym", "na_academia_a_vencer": "At the gym with workout close to expire", "na_academia_vencido": "At the gym with workout expired", "na_academia_minha_carteira": "At the gym from my wallet", "na_academia_sem_treino": "At the gym without workout", "sem_treino": "No workout", "bracos": "Arms", "excelente": "Excelent", "abaixo_da_media": "Below average", "acima_da_media": "Above average", "media": "Average", "weak": "Weak", "status_do_aluno": "Member status", "evento": "Event", "by": "By:", "seu_programa": "Your program", "adicionar_ao_aluno": "Add to member", "programa_de_treino_adicionado": "Workout program added successfully", "nao_foi_possivel_remover_ficha": "Could not delete the selected workout, try again later", "ops_nao_foi_possivel_excluir_a_ficha": "Ops! Could not delete the workout", "ao_clicar_em_excluir_a_ficha_sera_excluida": "By clicking 'Delete' the workout will be permanently removed.", "atividades_args": {"one": "{} exercise", "two": "{} exercises", "many": "{} exercises", "other": "{} exercises"}, "sem_categoria": "No category", "salvo_automaticamente_as": "Saved automatically at {}", "selecionar_ficha_predefinida": "Select pre defined workout", "nenhuma_ficha_cadastrada": "No workout registered yet", "comece_adicionando_sua_primeira": "Start by adding your first workout sheet here to setup a workout program", "adicionar_ficha": "Add workout sheet", "programa_criado_com_sucesso": "Program created successfully", "o_que_fazer": "Now what?", "cadastrar_nova_ficha": "Register a new workout sheet", "seu_treino": "Your workout", "se_voce_sair_sem_salvar": "If you leave without saving, your changes will be lost. Are you sure you want to leave?", "sair_sem_salvar_alteracoes": "Leave without saving?", "sair_sem_responder": "Leave without answering?", "continuar_e_responder": "continue and reply", "voce_quer_add_a_ficha_a_aluno": "Do you want do add the workout sheet to another member?", "salvo_com_sucesso_as": "Saved successfully at {}", "vincular_atividades": "Link exercises", "series_args": {"zero": "0 sets", "one": "1 set", "other": "{} sets"}, "sem_serie": "No set", "alterar_metodo": "Change method", "editar_nome": "Edit name", "adicionar_nome_atividade": "Add a name to the exercise", "ficha_salva_automaticamente_as": "Saved automatically at {}", "selecione_as_atividades_para": "Select the exercises to {}", "selecione_as_atividades_para_com": "{} with {} and {}", "metodo_com_atividades": "{} with {}", "sem_serie_cadastrada": "No set registered", "velocidade_abreviado": "speed", "nada_por_aqui_ainda": "Nothing here yet", "comece_adicionando_atividades": "Start by adding exercises here to setup your workout sheet", "adicionar_atividades": "Add exercises", "configurar_padrao_de_serie": "Setup sets pattern", "rep": "Rep", "gerar_as_series_nesta_atividade": "Generate sets in this exercise", "nao_gerar_padrao": "Do not generate pattern", "adicionar_ao_treino": "Add to the workout", "pesquisar_por_nome": "Search by name", "exibir_padrao_de_serie": "Show set pattern", "esconder_padrao_de_serie": "Hide set pattern", "ops_nao_encontramos_atividade": "Ops, we dind't find any exercise!", "salvar_ficha": "Save sheet", "programa_de_treino_excluido": "Workout program deleted!", "busque_pelo_nome_programa": "Search by the program's name", "ops_nao_encontramos_programa": "Ops, we didn't find any program!", "resultado_args": {"zero": "Result", "one": "Result", "two": "Results", "other": "Results"}, "nenhum_programa_cadastrado_ainda": "No program registered yet", "comece_criando_sua_primeira_ficha": "Start creating your first workout sheet to setup your library", "comece_criando_seu_primeiro_programa": "Start creating your first workout program to setup your library", "criar_nova_ficha": "New workout sheet", "criar_novo_programa_de_treino": "New workout program", "nao_foi_possivel_excluir_programa_tente_novamente": "Could not delete the selected program, try again later.", "nao_foi_possivel_excluir_programa": "Ops! Could not delete the program", "ao_clicar_excluir_o_programa_sera_excluido": "By clicling 'Delete' the program will be permanently deleted.", "ficha_args": {"zero": "0 sheet", "one": "1 sheet", "other": "{} sheets"}, "todos_os_generos": "All genders", "voce_precisa_preencher_nome": "You need to insert the name before you can continue", "voce_precisa_preencher_quantidade": "You need to insert the number of expected classes before you can continue", "cade_o_nome": "Oops, where is the name?", "cade_as_aulas": "Oops, where is the number of expected classes?", "criar_programa": "Create program", "salvar_alteracoes": "Save changes", "nome_do_programa": "Program's name*", "dias_por_semana": "Days a week*", "aulas_previstas": "Expected classes*", "o_programa_foi_predefinido": "The workout program has been predefined", "voce_precisa_editar_o_nome_programa": "You need to edit the program's name, save and then predefine agains.", "ja_existe_programa_com_esse_nome": "Oops, there is already a program with this name.", "predefinir_esse_programa": "Predefine this program", "voce_precisa_preencher_o_nome": "You need to insert the name before you can continue", "criar_ficha": "Create workout sheet", "nome_da_ficha": "Workout sheet's name*", "a_ficha_foi_predefinida": "The workout sheet has been predefined successfully", "voce_precisa_editar_nome_da_ficha": "You need to edit the sheet's name, save and then predefine again.", "ja_existe_ficha_com_esse_nome": "Oops, there is already a workout sheet with this name.", "predefinir_ficha": "Predefine sheet", "selecionar_programa": "Select Program", "atividade_sem_foto": "Exercise has no image", "atividade_sem_nome": "Exercise has no name", "aplicar_padrao_nas_outras_series": "Apply this pattern to the other sets", "remover_serie_atual": "Remove current set", "adicionar_serie": "Add set", "digite_aqui_obs_exercicio": "Insert here an observation about the exercise", "nenhuma_obs_ainda": "No observation yet", "cadastre_uma_nova_observacao": "Register a new observation about this member", "nenhum": "None", "piramide_decrescente": "Descending pyramid", "piramide_crescente": "Crescent Pyramid", "circuito": "Circuit", "isometrico": "Isometric", "super_serie": "Super-set", "bi_set": "BI-Set", "tri_set": "TRI-Set", "drop_set": "Drop-Set", "ondulatorio": "Wavy", "progressao_dupla": "Dual Progression", "de_lorme": "<PERSON>", "erpad": "Erpad", "parcelado": "Installments", "duplamente_parcelado": "Double installed", "triplamente_parcelado": "Triple installed", "puxe_empurre": "Pull push", "repeticao_roubada": "<PERSON><PERSON>n rep", "repeticao_forcada": "Forced rep", "repeticao_parcial": "Partial rep", "pique_de_contracao": "Contraction Peak", "tensao_lenta_continua": "Slow and Continuous tension", "set_descendente": "Descending set", "isolamento": "Isolation", "serie_composta": "Composite set", "super_set_multiplo": "Multiple Super-Set", "pre_exaustao": "Pre-Exhaustion", "serie_gigante": "Giant set", "super_circuito": "Super-Circuit", "musculacao_intervalada": "Interval weight training", "pliometrico": "Plyometric", "repeticao_negativa": "Negative Rep", "pos_exaustao": "Post-Exhaustion", "exaustao": "Exhaustion", "contracao_isometrica": "Isometric contraction", "continuo": "Continuous", "combinado": "Combined", "alternado_simultaneo": "Alternate + Simultaneous", "2tempos": "2 Times", "3tempos": "3 Times", "ponto_zero": "Point zero", "execucao_normal": "Regular execution", "busque_pelo_nome_do_aluno": "Search by the member's name", "busque_pelo_nome_da_ficha": "Search by workout name", "nao_encontramos_nenhuma_ficha": "Ops, could not find any workout sheet", "ficha_de_treino": "Workout sheet", "justificativa": "Justification", "escreva_sua_justificativa": "Write your justification", "nao_foi_possivel_fazer_checkin_limite_pessoas": "Unable to check-in the class, all spots are taken", "contratos_a_vencer": "Renovate", "porcentagem_alunos": "{}% from the students", "a_data_inicio_nao_pode_ser_depois": "The start date can't be after the end date", "sua_academia_nao_permite_ferias_retroativas": "Your gym does not allow vacation for past days", "gostaria_de_habilitar_descanso": "Would you like to enable break between sets and manage your rest time with the app?", "descanso_entre_series": "Break between sets", "voce_pode_habilitar": "You can enable and disable it anytime you want on 'Settings'", "habilitar": "Enable", "ficha_sem_atividades": "Your workout does not have any exercises", "peca_ao_personal_para_add_atividades": "Ask your instructor to add exercises to your workout", "aula_cheia": "Full", "sign_here": "Sign here", "sair_sem_responder_formulario": "Leave without answering?", "certeza_sair_sem_responder_formulario": "Are you sure you want to leave without answering the form? Yo can talk to your instructor to get more info", "passo_x_de_y": "Step {} of {}", "adicionar_observacao": "Add note", "editar_observacao": "Edit note", "limpar_assinatura": "Clear signature", "assine_o_questionario_para_continuar": "Sign the form to move on", "preencher": "Sign", "formulario_preenchido_com_sucesso": "Form completed", "este_questionario_te_dira": "This form will tell you whether it is necessary for you to practice physical activities with supervision", "questionario_de_prontidao": "Physical activity readiness form", "responder": "Answer", "responder_agora": "Answer now", "sim": "Yes", "nao": "No", "acompanhar": {"titulolocalization": "Accompanying", "todos": "All", "executando_treino": "Performing Training", "nenhum_ aluno": "No students here yet", "adicione_aluno_aqui": "Add students here in your follow-up tab and get a closer look at their progress", "adicionar_alunos": "Add students to the list", "excluir": "Delete", "nenhum_aluno_treinando": "No students practicing now", "inicie_treino": "Start training one of your students, so that he appears here", "adicionar_obs": "Add observation", "treino_finalizado": "Workout successfully completed!", "deseja_finalizar_treino": "Do you want to finalize the training of this student?", "finalizar_treino": "End workout", "aluno_sem_programa": "Oops, no training program.", "aluno_com_treino_execucao": "Student already has a workout running!", "executar_treino": "Run Training", "aluno_selecionado": "selected pupil", "alunos_selecionados": "selected pupils", "busque_pelo_nome": "Search by name", "nao_encontramos_aluno": "Oops, we couldn't find any students!", "verifique_digitacao": "Check for typos and try again. If you need to, use other terms", "ficha_sem_atividade": "Oops, no registered activity.", "selecionar_ficha": "Select workout", "aluno_adicionado_sucesso": "Successfully added", "aluno_excluido_sucesso": "Successfully deleted", "deseja_remover_alunos": "Do you want to remove them all?", "sem_ficha": "Training program without a registered training sheet"}, "parece_que_todas_as_vagas_estao_preenchidas": "It looks like all spots are taken", "deseja_entrar_na_fila": "Do you want to get on the line for this class?", "voce_foi_inserido_na_fila": "You are now on the line", "seu_plano_esta_expirando": "Your subscription is\n close to expire", "seu_plano_ira_expirar_args": "Your plan {}\nwill expire in {} days. Would you like\nto renew it now?", "renovar_meu_plano": "Renew my plan", "renovacao": "Renewal", "renovar_contrato": "Renew contract", "por_mes": "/ month", "duracao_meses": {"zero": "Duration of {} months", "one": "Duration of {} month", "many": "Duration of {} months", "other": "Duration of {} months"}, "inicio_do_contrato": "Start date", "meses_args": "{} months", "fim_do_contrato": "End date", "personal_data": "Personal data", "renovando_contrato": "Renewing contract", "so_um_momento": "Just a moment while we take care of everything", "plano_renovado_com_sucesso": "Plan has been successfully renewed!", "continue_aproveitando_o_melhor_da_academia": "Keep enjoying everything your gym has got to offer", "tem_versao_nova_disponivel": "There is an update available", "atualizacao_necessaria": "Update available", "atualize_seu_app": "Update your app and get access to the \nnew features available now", "atualizar_app": "Update app", "proxima_aula_meia_hora": "Your next class will start in half an hour", "permitir_notificacoes": "Allow Notifications", "agora_nao": "Not now", "ative_notificacoes_rotina_saudavel": "Turn on notifications and receive all the reminders and tips to advance your healthy routine even further!", "com_lembretes_longe": "With reminders, you go further", "ative_notificacoes_treinos": "Turn on notifications to stay up to date with your workouts.", "fique_ligado": "Stay tuned in your training!", "ative_notificacoes_por_dentro": "Turn on notifications and stay on top of everything that happens in your app!", "quem_interagiu": "Find out who interacted with you!", "ative_notificacoes_aulas": "Turn on notifications to receive reminders for all classes and not miss the time!", "aula_nao_perder": "This class is not to be missed!", "ative_notificacoes_aulas_coletivas": "Turn on notifications to receive reminders of all classes and don't miss the time of your collective classes!", "a_turma_vai_sentir": "The class will miss you…", "ative_notificacoes_turbina_treino": "Turn on notifications to receive news, reminders and tips that boost your training routine!", "treino_mais_inteligente": "Your Smartest Training", "ative_notificacoes_wods": "Turn on notifications to receive news, reminders and tips that boost your WODs routine!", "ative_notificacoes_por_dentro_box": "Turn on notifications and stay on top of everything that happens in your box!", "acompanhe_records_box": "Follow the records of the Box!", "ative_notificacoes_evoluir": "Turn on notifications and receive all the reminders and tips to evolve even more with the help of your app!", "ative_notificacoes_compromisso": "Turn on notifications to receive all reminders and not miss any appointments!", "esse_e_imporante": "This is important...", "ative_notificacoes_melhorar_rotina_saudavel": "So, turn on notifications now to stay on top of everything in your app and improve your healthy routine!", "vai_ficar_para_proxima": "Will you stay for the next one?!", "ative_notificacoes_compromissos": "Turn on notifications to receive reminders of your appointments!", "memoria_falha_app_nao": "Memory fails, your app doesn't", "ative_notificacoes_plano": "Turn on notifications to receive all reminders and complete your plan successfully!", "lembre_se_proxima_refeicao": "Remember the next meal", "ative_notificacoes_alcancar_objetivo": "Turn on notifications to receive reminders and tips that help you reach your goal!", "ajuda_bem_vinda": "Extra help is always welcome!", "ative_notificacoes_lembretes_dicas": "Turn on notifications and receive all the reminders, news and tips to advance your routine even further!", "voce_vai_mais_longe": "With the app, you go further", "ative_notificacoes_acontece_app": "Turn on notifications and stay on top of everything that happens in your app!", "saiba_tudo_o_que_esta_rolando": "Know everything that is going on!", "ative_notificacoes_nao_perder_horario": "Turn on notifications to receive reminders for all classes and not miss the time!", "acompanhe_aulas": "Follow your lessons", "ative_notificacoes_participar_aulas": "Turn on notifications to know which students will join your classes and more!", "fique_atualizado": "Stay updated!", "ative_notificacoes_facilitar_rotina": "Turn on notifications and receive all the news and reminders to make your routine easier!", "acompanhe_atualizacoes": "Follow updates", "wow_turbine_app": "Wow! Turbine your app too", "ative_notificacoes_novidades_box": "Turn on notifications and stay on top of everything that happens in your box!", "acompanhe_recordes_box": "Follow the records of the Box!!", "fique_ligado_wod": "Stay tuned in WOD", "ative_notificacoes_concluir_plano": "Turn on notifications to receive all reminders and complete your plan successfully!!", "fechar": "Close", "escolher_plano": "Choose plan", "conferir_planos": "Check plans", "registrar_refeicao": "Register meal", "adicionar_mais": "Add more", "obter_meu_plano": "Get my plan", "reposicoes_args": {"zero": "{} restitutions", "one": "{} restitution", "other": "{} restitutions"}, "treino_em_casa": {"nao_possivel_validar": "We were not able to validate your situation with the company, Please contact the administration to resolve.", "contato_trancado": "Your contract is locked. Please contact the administration to resolve", "nao_possivel_autorizar": "You do not have authorization to access the Home Trainings. Please contact the administration to resolve", "contrato_ferias": "Your contract is on vacation. Please contact the administration to resolve", "contrato_atestado": "Your contract is filled. Please contact the administration to resolve"}, "water": "Water", "user_found": "User found", "data_maior_ou_igual": "The start date entered is greater than or equal to the end date. Check and try again.", "erro_agendamento": "You have already booked the maximum amount of events allowed by the gym.", "validacao_aluno_agendamento_erro": "You have already booked the maximum amount of events allowed by the gym.", "conta_excluida_inativa": "This account is inactive", "reativar_conta": "Reactivate account", "conta_excuida_termos": "You have requested a definitive exclusion from it and the process is in progress.", "excluir_conta": "Delete account", "nao_excluir_conta": "Cancel", "excluir_conta_termos": "This term establishes the conditions for deleting your account on our platform. Please read the items below carefully before proceeding with this action: \nBy requesting deletion of your account, it will be marked as inactive for the next 30 days. During this period, you may reactivate the features and services associated with your account; After the 30 day period, your account will be permanently deleted along with all data and information associated with it. This deletion is irreversible and it will not be possible to recover it after that; Account deletion does not cancel your contract with the gym. You remain subject to the terms and conditions of the master agreement and the financial obligations agreed upon therein remain unchanged. If you wish to cancel the contract with the gym, you must contact the responsible department directly.", "nenhum_treino_vencido": "No workout expire.", "nenhum_treino_avencer": "No workout to win", "nenhum_treino_emdia": "No workout in the day", "nenhum_treino_vencido_mensagem": "There are currently no expired workouts.", "nenhum_treino_avencer_mensagem": "At the moment there is no workouts to renew.", "nenhum_treino_emdia_mensagem": "At the moment there is no workouts is up to date.", "termo_polica": "Terms and policies", "texto_termos_politicas": "To continue using our application, you must agree to the Terms and Conditions and the Privacy Policies. By clicking the Accept button, you agree to all the terms described in the above documents.", "codigo_verificacao": "Verification code", "consulta_agendada": "Scheduled appointment", "sem_programa_treino": "Without training program", "seu_professor_ainda_nao_criou_seu_programa": "your instructor has not created your training program yet or it has expired.\nTalk to him/her to start training.", "agenda": "Schedule", "em_casa": "At home", "outros": "Others", "treinos_na_semana": "Workouts during the week", "treine_em_casa": "Train at home", "veja_mais_treinos_para_fazer": "Explore more workouts to do", "ver_treinos": "See workouts", "unidade": "Unit", "expandir_calendario": "Expand calendar", "retrair_calendario": "Collapse calendar", "aulas_disponiveis": "Available classes", "filtrar": "Filter", "ver_records": "View records", "em_execucao_": "In progress", "iniciar_timer": "Start timer", "treinar_acompanhado": "Training accompanied is even better!", "convide_seus_amigos": "Invite your friends to train and get exclusive benefits!", "convidar_agora": "Invite now!", "nao_seguir": "Don't follow", "seguir": "Follow!", "cafe_da_manha": "Breakfast", "vem_seguir_sua_dieta": "Come follow your diet to achieve incredible results!", "historico_fitness": "Fitness history", "indicadores": "Indicators", "verificar_agendamento": "Check appointment", "verificar_disponibilidade": "Check availability", "composicao_corporal": "Body composition", "elevado": "elevated", "ver_mais_dados": "See more data", "minha_evolucao": "My evolution", "comparativo_antes_depois": "Before and after comparison", "peso_ideal": "Ideal weight", "metabolismo_basal": "Basal Metabolic", "idade_corporal": "Body age", "a_taxa_metabolica_basal": "Basal metabolic rate (BMR) is the amount of energy required for the maintenance of vital functions of the body over 24 hours.", "a_idade_corporal_representa": "Body age represents your average basal metabolic rate compared to other people who are the same chronological age as you.", "valido_entre": "Valid between", "criado_por": "Created by", "configurar_o_app": "Set up the app", "encerrar_sessao": "Log out", "aparencia": "Appearance", "unidades_de_medidas": "Units of measurement", "execucao": "Execution", "privacidade": "Privacy", "peso_corporal": "Body weight", "normal": "Normal", "vencimento_da_fatura": "Due date of the invoice", "creditos_disponivel": "Available credits", "parcelas_vencidas": "Overdue installments", "parcelas_em_atraso": "Late installments", "pagar_todas": "Pay all", "parcela_atual": "Current installment", "ver_historico_parcel": "View installment history", "pagar_com_boleto": "Pay with a bank slip", "plano_atual": "Current plan", "vitio": {"plano_alimentar": "Meal plan", "plano_alimentar_info": "Unlock the next level in a healthy\nlife. Take control of your diet!", "proteinas": "protein", "carboidratos": "carbohydrates", "lipidios": "Lipids", "controle_de_hidratacao": "Hydration Control", "suporte": "Suport", "suporte_info": "Need help with something?", "controlar_doenca": "Manage\nillness", "ganho_de_massa": "Muscle\ngain", "qualidade_de_vida": "Quality of\nlife"}, "mensagem_aluno": "Message to the gym student", "mes_duracao": "Month in duration", "meses_duracao": "Months in duration", "valores": "Prices", "valor_mensal": "Price per month", "recorrencia": "Recurrence", "sobre_recorrencia": "You make a single contract and pay every month. Your card limit does not get blocked.", "sobre_cobranca_normal": "Make a single contract and pay the amount in installments. Your card limit gets blocked and is released as you make the installment payments.", "modalidades_inclusa": "Included modalities", "credito_contrato": "Contract credit", "sobre_credito": "With this contract, you acquire", "subtitulo_credito": "Check with your gym for usage conditions.", "cpf_nao_cadastrado": "Data not registered!", "subtitulo_cpf_cadastrado": "Please go to the reception to update your information in order to continue making the purchase and enjoy your app with everything the gym has to offer.", "comprar_plano": "Buy", "contratar_plano": "Hire plan", "sem_aparelho": "No equipment!", "nenhum_aparelho": "No equipment found", "aparelho_cadastrado": "No equipment registered", "sem_atividade": "No activities!", "nenhuma_atividade": "No activities found", "atividades_cadastradas": "No activities registered", "ver_ranking": "See full ranking", "situacao_aluno": {"ativo": "Active", "inativo": "Inactive", "visitante": "Visitor", "trancado": "Locked", "cancelado": "Canceled", "desistente": "Withdrawn", "vencido": "Expired", "avencer": "Due"}, "prescricao_treino": "Training prescription", "renovar_treino": "Renew training", "contato_interpessoal": "Interpersonal contact", "revisao": "Review", "texto_avaliacao": "See your progress and improve the performance of your workouts.", "text_prescicao": "Ideal for creating a new record with new goals.", "texto_renovar_treino": "Continue with the previously prescribed workout.", "texto_contato_interpessoal": "Schedule a time to talk to this professional.", "texto_revisao": "Reevaluate your workout and make corrections for its continuation.", "agendamentos": "Appointments", "marque_novo_agendamento": "Schedule a new appointment, and it will appear here.", "cancelar_agendamento": "Cancel appointment", "deseja_cancelar": "Do you want to cancel your appointment?", "confirmar_agendamento": "Confirm cancellation", "nao_cancelar": "Do not cancel", "agendamento_cancelado": "Appointment canceled!", "aguardando_voce": "It's waiting for you", "tem_um_agendamento": "You have an appointment for today", "selecione_professor": "Select a Trainner", "sem_horario_disponivel": "No available time slots", "buscar_por_nome": "Search by name", "selecionar_unidade": "Select unit", "vagas": "Vacancies", "vagas_": {"one": "{} Vacancy", "two": "{} Vacancies"}, "selecionar_professor": "Select trainner", "sem_professor_disponivel": "No trainner available", "data_horario": "{mesTratado}/{diaTratado} at {horas}", "acao_desfeita": "This action cannot be undone", "imagem": "Image", "upload_image": "Upload an image", "melhor_visualizacao": "For better viewing, upload square images", "salvar_wod": "Save Wod", "descricao_wod": "Wod Description*", "selecione_periodo": "Select period", "periodo_consulta": {"semana": "Week", "mes_atual": "Current month", "mes_anterior": "Previous month"}, "sem_historico": "No history", "neste_periodo_de": "In this period, from {} - {}, no records were found in your timeline.", "eventos_historico": {"aula_desmarcada": "Canceled class: {}", "fez_aula": "Attended class: {}", "registrou_wod": "Recorded WOD: {}", "notificacao": "Notification: {}", "treinou": "Executed training sheet: {}", "mudou_de_nivel": "Changed level: {}", "montou_treino": "New Training: {}", "realizou_avaliacao": "{}", "acabou_treino": "Finished Workout: {}", "renovou_treino": "Renewed Workout: {}", "alteracao_agendamento_servicos": "Appointment changed: {}", "agendamento": "Appointment: {}"}, "obs_excluido": "Observation deleted successfully!", "nao_add": "Do not add", "niveis_corporal": {"baixo": "Low", "normal": "Normal", "alto": "High", "sobrepeso": "Overweight", "obesidade": "Obesity", "elevado": "Elevated"}, "texto_residual": "Residual corresponds to the percentage of body components, excluding fat, muscles, and bones.", "muscular_baixo": "Increase muscle mass with a high-protein diet and hypertrophy exercises.", "muscular_normal": "Your percentage of muscle mass is at a normal level.", "muscular_bom": "Your percentage of muscle mass is excellent; keep it up.", "massa_magra_baixo": "Your lean mass is below the ideal level. It is recommended to follow a resistance training program and a protein-rich diet to promote muscle growth and bone health.", "massa_magra_normal": "Your lean mass is within the range considered healthy for your age and gender.", "massa_magra_alto": "Your lean mass is above average, which is a sign of good health and physical strength. Continue with your exercise program and balanced diet to maintain your optimal physical condition.", "gordura_bom": "Low fat percentage: attention, except for athletes.", "gordura_normal": "Normal percentage, keep it up!", "gordura_alto": "High fat percentage: health risk.", "ossea_baixo": "Low bone mass: health risk. Consume calcium.", "ossea_normal": "Your bone mass is at a normal level.", "ossea_alto": "High bone mass: a sign of strength and health. Continue with a balanced diet and regular exercise to keep your bone structure strong and resilient throughout life.", "nivelAgua_baixo": "Your body's water level is well below healthy levels; improve your hydration routine.", "nivelAgua_normal": "Your body's water level is at a normal level.", "nivelAgua_bom": "Your body's water level is at a very good level; keep it up!", "imc_baixo": "Your BMI is low (18.5), as is the body fat percentage.", "imc_normal": "Your BMI is normal (25), as is the body fat percentage.", "imc_elevado": "Your BMI exceeds the limit (30), as does the body fat percentage.", "imc_muitoElevado": "Your BMI greatly exceeds the limit (35), as does the body fat percentage.", "visceral_baixo": "Your visceral fat level is low; keep it up.", "visceral_normal": "Your visceral fat level is at a normal level.", "visceral_alto": "Your visceral fat level is high. Maintaining a healthy routine can help with this.", "ultimaAvaliacao": "Last physical assessment", "sem_aulas_disponiveis": "No available classes", "nenhuma_aula_encontrada": "No classes found for the selected day and filters.", "transmissao_online": "Class link", "acessar_aula": "Access class", "local_aula": "Class location", "sala_principal": "Main room", "vagas_turma": "Availability", "minhas_aulas": "My classes", "meus_tickets": "My tickets", "fila_espera": "Waiting list", "editar_aluno": "Edit member", "aulas_finalizadas": "Classes completed", "clique_veja_aulas": "Click to see the list of classes", "termino_programa": "End", "sem_permissao": "No permission!", "aluno_sem_permissao": "Students do not have permission to perform this operation. Please go to the reception to cancel your physical assessment.", "wod_nao_cadastrado": "Today's WOD has not been registered yet", "selecionar_metodo": "Select execution method", "metodo_execucao": "Execution method", "aplicar_padrao": "Apply standard to other sets", "segundos": "seconds", "atividades_selecionadas": "Selected activities", "deseja_limpar_selecao": "Do you want to clear your selection?", "todos_itens_selecionados": "All selected items will be deleted. Are you sure you want to do this?", "programa_adicionado": "Program added!", "add_programa_atual": "Would you like to add the current program to other students?", "cade_programa_treino": "Oops! Where's the workout program?", "aluno_sem_programa": "The student doesn't have a workout program to add to the selected record. Please register a program first to proceed.", "movimento": "Crowded", "aluno_sendo_acompanhado": "The student is already being monitored.", "intervalo": "Break", "prescricao_ia": "AI Workout", "prescricao_por_ia": "AI Workout", "recurso_ia": "The AI Workout Prescription feature is not enabled at your unit. Access the Pacto App and enable the feature.", "serie_removido_sucesso": "Series removed successfully!", "aula_experimental": "Trial Class", "diaria_gympass": "Gympass Daily", "desafio": "Challenge", "domingo": "S", "segunda": "M", "terca": "T", "quarta": "W", "quinta": "T", "sexta": "F", "sabado": "S", "agenda_avaliacao_permissao": "No permission to schedule a physical evaluation. Please go to the front desk for more information.", "sem_permissao_avaliacao": "You do not have permission to view your physical evaluation.", "detalhes_do_plano": "Plan details", "sem_email_cadastrado": "No email registered", "nome_do_cartao": "Name on the card", "numero_de_parcelas": "Number of installments", "data_de_cobranca": "Billing date", "cupom_de_desconto": "Discount coupon", "aplicar_cupom": "Apply coupon", "para_poder_aproveitar_tudo": "In order to take full advantage of everything your gym has to offer, first of all, you need to choose a plan to get started.", "acessar_com_outro_usuario": "Access with another user", "va_na_recepcao_de_sua_academia": "Go to the reception of your gym and choose the best plan for you.", "resumo_de_compra": "Purchase summary", "teste": "TEST", "antes_de_finalizar_o_pagamento": "Note: Before completing the payment, it is necessary to view the terms and conditions of the contract.", "li_os_termos_e_aceito": "I have read the terms and I accept", "mais_de_um_cadastro_encontrado": "More than one registration found with this email/CPF", "data_futura_pr": "It is not possible to register a PR with a future date. Please correct the date and try again.", "nivel_muscular_info": {"baixo": "Increase muscle mass with a protein-rich diet and hypertrophy exercises.", "normal": "Your percentage of muscle mass is within a normal range.", "bom": "Your percentage of muscle mass is within a normal range.", "muito_bom": "Your percentage of muscle mass is excellent, keep it up!"}, "nivel_gordura_corporal": {"bom": "Low body fat percentage: caution, except for athletes.", "normal": "Normal percentage, keep it up!", "alto": "Normal percentage, keep it up!", "muito_alto": "High body fat percentage: health risk."}, "residual_info": "Residual corresponds to the percentage of body components, excluding fat, muscles, and bones.", "nivel_imc": {"baixo": "Your BMI is low (18.5), as well as the body fat percentage.", "normal": "Your BMI is normal (25), as well as the body fat percentage", "elevado": "Your BMI exceeds the limit (30), as well as the body fat percentage.", "muito_elevado": "Your BMI significantly exceeds the limit (35), as well as the body fat percentage."}, "texto_peso_ideal": "Your ideal weight is {} kg. However, it is still considered healthy if it varies between {} and {} kg.", "sobre_o_contrato": "About the contract", "termos_do_contrato": "Terms of the contract", "historico_parcel": "Installment history", "ano": "year", "anos": "years", "informe_seu_telefone": "Provide your phone number", "informe_seu_telefone_info": "Please provide the phone number used in your gym registration to access the app.", "usuarios_recentes": "Recent users", "fichas_de_treino": "Training sheets", "meus_wods": "My Wods", "vaga": "slot", "criado_por_prof": "Created by: Prof. {}", "exercicios_literal": "Exercises", "iniciar_treino": "Start workout", "primarios": "Primary", "secundarias": "Secondary", "visualizar_por": "View by", "horario": "time", "modalidade": "modality", "concluir_serie": "Finish series", "adicionar_minha_imagem": "Add my image", "cadastrar": "Register", "encerrar_treino": "End workout", "tem_certeza_encerrar_treino": "Are you sure you would like to end your training without completing all the exercises?", "desfazer": "Undo", "cronometro": "Stopwatch", "emom": "<PERSON><PERSON>", "tabata": "<PERSON>bat<PERSON>", "progressivo": "Progressive", "regressive": "Regressive", "regressivo": "Regressive", "tempo": "Time", "nutricionista": "Nutritionist", "escolha": "<PERSON><PERSON>", "sair_sem_salvar": "Exit without saving", "sair_sem_salvar_registro": "Are you sure you would like to exit without saving this record? It will be lost", "manter_registro": "Keep Record", "sem_usuario": "no user", "salvar_imagem": "Save image", "com_prof": "with prof.", "historico_atividades": "Activity history", "renove_seu_plano": "Renew your plan to start enjoying everything your gym has to offer again!", "renove_seu_plano_na_recepcao": "Renew your plan at the reception to start enjoying everything your gym has to offer again", "para_poder_treinar": "To be able to train and enjoy your app with everything your gym has to offer, choose a plan to get started.", "para_poder_treinar_na_recepcao": "To be able to train and enjoy your app with everything your gym has to offer, choose a plan at the reception to get started.", "volte_com_foco_total": "Come back with full focus on achieving a healthier life and choose a new plan.", "volte_com_foco_total_na_recepcao": "Come back with full focus on achieving a healthier life and choose a new plan at the reception.", "aceitar": "Accept", "aconteceu_algum_problema": "Did something happen?", "seu_feedback_nos_ajuda": "Your feedback helps us improve the app.", "relatar_um_problema": "Report a problem", "agitar_o_telefone_para_relatar_um_problema": "Shake the phone to report a problem", "mova_o_switch_para_desabilitar": "Move the switch to disable", "calorias_gastas": "Calories \nBurned", "quantidade_de_passos": "Number \nof steps", "meu_peso": "My Weight", "consumo_de_agua": "Water intake", "seu_programa_esta_sendo_criado": "Your training program is being created!", "sua_anamnese_foi_enviada": "Your medical history has been sent to your instructor, and in a few minutes, you will receive your training sheet.", "qual_o_seu_objetivo_atual": "What is your current goal?", "emagrecimento": "weight \nloss", "emagrecimento_nao_espacado": "weight loss", "objetivos": "Goals", "ganho_de_massa": "Muscle \ngain", "ganho_de_massa_nao_espacado": "Muscle gain", "qualidade_de_vida": "Quality\n of life", "qualidade_de_vida_nao_espacado": "Quality of life", "controlar_doenca": "Manage \ndisease", "controlar_doenca_nao_espacado": "Manage disease", "qual_a_sua_altura": "What is your height?", "quantos_dias_voce_pratica_atividade": "How many days per week do you engage in physical activity?", "qual_sua_idade": "What is your age?", "qual_seu_nivel_treino": "What is your current training level?", "qual_o_seu_peso_atual": "What is your current weight?", "meus_indicadores": "My indicators", "indicadores_incluidos": "Included indicators", "indicadores_disponiveis": "Available indicators", "explique_o_que_aconteceu": "Briefly explain what happened or what is not working.", "dica_tente_relogar_ou_reiniciar": "Tip: If the problem persists, try logging out and reinstalling your application.", "outras_fichas": "Other sheets", "mensagem_para_o_professor": "Message for the professor:", "avalie_seu_treino": "Evaluate your workout", "frequencia_cardiaca": "Heart rate", "calorias_totais": "Total calories", "series_concluidas": "Completed sets", "obs_os_dados_de_calorias": "Note: The step count data is provided by your device's Health app.", "do_treino_concluido": "from the completed workout", "prepare_se_a_seguir": "Get ready, next:", "aplicar_a_todos": "Apply to all", "aplicar_mudancas_para_outras_series": "Apply change to other sets?", "sem_agendamento": "No appointment", "marque_um_novo_agendamento": "Schedule a new appointment and it will appear here", "reagendar": "Reschedule", "horarios_disponiveis": "Available time slots", "sem_horarios_disponiveis": "No available time slots", "sem_professores_com_disponibilidade": "No instructors available", "reposicoes": "Replacements", "nao_foram_encotradas_aulas": "No classes were found for the selected day and filters.", "meus_creditos": "My credits", "minhas_reposicoes": "My replacements", "saldo_de_reposicao": "Replenishment balance", "seu_contrato_e_a_base_de_credito_proevolution": "To find out how to use your group class credits, go to the front desk of your unit and check the usage policy.", "seu_contrato_e_a_base_de_credito": "Your contract is credit-based. Use these credits to check in for classes. When your credits run out, you will be unable to check in.", "saldo_proveniente_de_uma_reposicao": "Balance from a rescheduling. Use these credits to check-in for group classes. When your balance runs out, you will be unable to check-in.", "saldo_atual": "Current balance", "selecionar_contrato": "Select Contract", "notamos_que_voce_tem_mais_de_um_contrato": "We noticed that you have more than one contract, please select one to continue", "modalidades": "Modalities", "local_da_aula": "Class location", "minha_aulas": "My classes", "vagas_disponiveis": "Available slots", "historico_de_aulas": "Class history", "credito": "Credit", "dados_do_cartao": "Card information", "nome_cartao": "Card Name", "escanei_este_codigo_para_pagar": "Scan this code to pay", "pague_e_sera_creditado_na_hora": "Pay and it will be credited immediately", "ou_copie_este_codigo": "Or copy this code to make the payment", "escolha_pagar_via_pix": "Choose to pay via Pix using your Internet Banking or payment app. Then, paste the following code:", "dados_do_boleto": "Dados do boleto", "historico_de_peso": "Weight history", "suas_fichas": "Your trainings", "exercicio_sem_imagem": "Exercise without image", "em_andamento": "In progress", "dias_que_treino": "Days trained", "ultima_execucao": "Last execution", "gordura_visceral": "Visceral fat", "ver_historico": "View history", "ocultar": "<PERSON>de", "checkin_realizado": "Check-in completed", "oque_voce_esta_procurando": "What are you looking for?", "ops_nao_encontramos_nenhuma_unidade": "Oops, we didn't find any unit", "verifique_se_nao_ha_erros": "Please check for any errors in the entered term and try again", "juros_e_taxas": "Interest and fees", "total": "Total", "dados_de_pagamento": "Payment details", "voce_tem_ate": "You have until", "copie_o_codigo_abaixo": "Copy the code below to make the payment at your bank.", "nao_foi_possivel_gerar_boleto": "Unable to generate the invoice", "copiar_codigo_de_barras": "Copy barcode", "copiar_codigo_com_sucesso": "Code copied successfully!", "o_boleto_podera_ser_compensado": "The payment slip can be processed within 72 hours after payment, excluding weekends and holidays.", "mes_de_duracao": "Month duration", "meses_de_duracao": "Months of duration", "reposicao_disponivel": "Available replenishments", "voce_faz_um_unico_contrato": "You make a single contract and pay every month. Your card limit is not blocked.", "faca_um_unico_contrato": "Make a single contract and pay the amount in installments. Your card limit is blocked and released as the installments are paid.", "modalidades_inclusas": "Included modalities", "credito_do_contrato": "Contract credit", "com_este_contrato_voce_adquire": "With this contract, you acquire", "data_da_cobranca": "Billing date", "digite_aqui": "Type here", "vigencia_do_contrato": "Contract duration", "ferias_agendada": "Scheduled Vacations", "de_a": "from  {} to {}", "detalhes_do_contrato": "Contract details", "dias_permitidos": "Allowed days", "trancamento_agendado": "Scheduled suspension", "valor_nao_informado": "Value not informed", "novo_contrato_disponivel": "New contract available", "assinar_contrato": "Sign contract", "sair_do_app": "Exit the App", "ao_clicar_em_sair_voce_nao_estara_mais_logado": "By clicking on 'Exit,' you will no longer be logged into the app, and any unsaved data will be lost.", "ops_ainda_nenhum_contrato": "Oops, there are no contracts here yet", "voce_ainda_nao_firmou_nenhum_contrato": "You haven't signed any contract with your gym yet.", "voce_ainda_nao_firmou_nenhum_contrato_vamos_fazer_agora": "You haven't signed any contract with your gym yet. Shall we create one now?", "retornar_das_ferias": "Return from vacation", "contrato_trancado": "Contrato suspended", "retornar_do_trancamento": "Return from suspension", "trancamento_vencido": "Expired suspension", "o_seu_contrato_esta_com_o_trancamento_vencido": "Your contract's suspension has expired, please visit the reception!", "o_seu_boleto_esta_vencido": "Your invoice is expired", "fale_com_o_seu_consultor": "Speak to your consultant and request a new payment slip.", "ops_boleto_nao_gerado": "Oops! Invoice not generated", "fale_com_o_seu_consultor_solicite_geracao_boleto": "Speak with your consultant and request the generation of your payment slip.", "o_seu_plano_nao_tem_um_contrato": "Your plan is not tied to a contract.", "termo_de_exclusao_de_conta": "Account Deletion Notice", "alterar_foto_no_cadastro": "Change photo in the registration", "voce_deseja_usar_essa_foto_no_seu_perfil": "Do you want to use this photo as your user profile picture? Note: It will be changed on the profile used via computer.", "reportar_esse_comentario": "Would you like to report this comment on the post as offensive or spam?", "metricas_de_treino": "Training metrics", "intensidade_media": "Average intensity", "seu_imc_excede_o_limite": "Your BMI exceeds the limit (25.0), as well as the body fat percentage.", "niveis": "Levels", "nivel": "Level", "ultimas_atividades": "Recent activities", "veja_seu_resumo_semana": "See your weekly summary", "veja_suas_conquistas": "See your achievements and track your progress", "veja_seus_treinos_salvos": "See your saved workouts", "todos_os_treinos": "All workouts", "veja_todos_os_treinos_disponiveis": "See all available workouts", "nenhum_treino_encontrado": "No workouts found!", "desculpe_nenhum_treino_encontrado": "Sorry, no workout found with that name. Please try again", "o_telefone_nao_foi_encontrado": "The phone was not found", "o_numero_de_telefone_nao_foi_encontrado": "The phone number {} was not found in our gym registrations. Please check if you entered it correctly.", "corrigir_o_numero_de_telefone": "Correct the phone number", "fazer_login_com_usuario": "Log in with username", "seu_treino_esta_quase_pronto": "Your workout is almost ready!", "ative_as_notificacaos_para_ser_avisado": "Enable notifications to be notified when your workout is ready.", "essas_informacoes_sao_importantes": "This information is important for your instructor to create the best fitness plan for you.", "obter_meu_treino": "Get my workout.", "sua_evolucao_comeca_aqui": "Your progress starts here!", "gordura_corporal": "Body fat", "massa_ossea": "Bone mass", "registrar_evolucao": "Register progress", "vincular_a_avaliacao_fisica": "Link the physical assessment", "vincular_a_qual_avaliacao_fisica": "Link to which assessment?", "fotos_ja_vinculadas": "Photos already linked", "registrar_com_a_camera": "Record with the camera", "utilizar_fotos_da_galeria": "Use photos from the gallery", "selecionar_o_angulo_da_proxima_imagem": "Select the angle of the next image", "frente": "front", "lado_direito": "Right side", "lado_esquerdo": "Left side", "costas": "Back", "concluir_incompleta": "Incomplete conclusion", "selecionar_fotos": "Select photos", "selecionar_2_fotos_para_comprar": "Select 2 photos to compare", "falha_ao_salvar_foto": "Failed to save photo", "infelizmente_nao_foi_possivel_fazer_essa_operacao": "Unfortunately, it was not possible to perform this operation", "pular_etapa": "Skip step", "deseja_pular_o_registro_da_foto_dessa_etapa": "Do you want to skip recording the photo for this step?", "configuracao_de_camera": "Camera Settings", "mascara_de_silhueta": "<PERSON><PERSON><PERSON><PERSON>", "temporizador": "Timer", "salvar_configuracao": "Save settings", "tudo_pronto_para_salvar_a_sua_evolucao": "All set to save your progress", "registre_sua_evolucao": "Record your progress!", "registre_imagens_de_sua_evolucao": "Capture images of your progress and track your development.", "a_balanca_nao_e_o_unico_jeito": "The scale is not the only way to track your progress!", "com_comparativo_voce_pode_acompanhar_antes": "With the comparison, you can track the before and after to notice changes that don't show up on the scale.", "capture_4_fotos_do_seu_corpo": "Capture 4 photos of your body quickly and easily", "utilize_nossa_silhueta_guia": "Use our silhouette guide to assist you in capturing images, adjusting the shoulder and waist size for easy framing!", "dicas_para_ajudar_no_comparitivo": "Tips to help with the comparison:", "tire_a_foto_em_local_bem_iluminado": "Take the photo in a well-lit area", "de_preferencia_para_fotos": "Preferably, choose photos with white backgrounds or minimal information", "utilize_as_mesmas_roupas_ou_similares": "Use the same clothes or similar ones", "selecionar_o_angulo": "Choose an angle", "treinos_pendetes_para_renovacao": "Pending workouts for renewal", "renove_o_treino_dos_seus_alunos": "Renew your students' workout.", "no_momento_nao_existe_nenhum_aluno_ativo": "Currently, there are no active students", "veja_seus_records": "See your records", "sem_ranking_por_enquanto": "No ranking for now!", "ainda_nao_temos_resultados_do_wod": "We don't have WOD results yet. Be the pioneer and show your progress to motivate the community!", "veja_seu_records": "See your records", "salvar_resultado": "Save Result", "seja_o_primeiro_a_registrar": "Be the first to register!", "poste_seu_resultado_agora_mesmo": "Post your result right now and be the first on the leaderboard! Share and spread the word with friends on social media!", "deixe_seus_prs_sempre_atualizados": "Keep your PRs always up-to-date to perform calculations based on them and improve your score in the rankings", "timer_concluido": "Timer completed!", "contagem_antes_de_iniciar_o_time": "Countdown before starting the timer", "sons_do_timer": "Timer Sounds", "habilitar_efeitos_sonoros": "Enable sound effects", "excluir_pr": "Delete PR", "tem_certeza_que_gostaria_de_excluir_pr": "Are you sure you want to delete your Personal Record?", "manter_pr": "Keep PR", "editar_pr": "Edit PR", "excluir_personal_record": "Delete Personal Record", "pr_excluido_com_sucesso": "PR excluído com sucesso", "nome_do_wod": "WOD Name*", "meu_wod": "My WOD", "faca_upload_de_uma_imagem": "Upload an image", "para_melhor_visualização_envie_imagens_quadrada": "For better viewing, please upload square images", "data_de_inicio": "start date", "descricao_do_wod": "WOD Description*", "aparelho": "<PERSON><PERSON>", "esta_aguardando_voce": "Waiting for you", "selecionar_metodo_execucao": "Select execution method", "adicionar_nome_na_atividade": "Add name to the activity", "serie_removida_com_sucesso": "Series successfully removed!", "ops_voce_nao_tem_permissao": "Oops! You don't have permission to perform this operation.", "nova_ficha_de_treino": "New training card", "novo_programa_de_treino": "New training program", "editar_programa": "Edit program", "ultima_avaliacao_fisica": "Última evaluación física", "resistencia_muscular": "Muscular endurance", "selecionar_periodo": "Select period", "temos_um_metodo_muito_facil": "We have a very simple method for you to exchange your registered card and update your Vitio package or subscription:\n\nAccess the link", "central_de_ajuda": "Help center", "entre_em_contato_conosco": "Get in touch with us", "inativo": "Inactive", "oberservacao_excluida_com_sucesso": "Note successfully deleted!", "prepare_se_para_comecar": "Get ready to start", "para_poder_aproveitar_tudo_o_que_sua_academia": "In order to take advantage of everything your gym has to offer, first and foremost, you need to choose a plan to get started.", "contrato_do_plano": "Plan contract", "nao_ha_horarios_disponiveis": "There are no available time slots, please try on another date.", "consulta_concluida": "Consultation completed", "como_foi_sua_consulta_com": "How was your consultation with:", "o_nutri_ira_te_responder_assim_que_possivel": "The nutritionist will respond to you as soon as possible, within 1 business day.", "baixando_o_arquivo": "Downloading the file: ", "o_codigo_de_5_digitos_foi_enviado": "The 5-digit code has been sent by email to the provided email address.", "conectando": "Connecting...", "editar_email": "Edit email", "editar_cpf": "Edit CPF", "informe_o_seu_email_utilizado_no_vitio": "Please provide the email used for purchasing Vitio.", "historico_de_consulta": "Consultation history", "sem_nome": "No Name", "essa_semana": "This week", "mes_atual": "Current month", "mes_anterior": "Previous month", "nenhuma_consulta_por_aqui": "No consultations here yet.", "que_tal_garantir_um_horario_agora": "How about securing a time now?", "agendar_consulta": "Schedule a consultation", "ja_tenho_um_plano": "I already have a plan", "criar_conta": "Create account", "garanta_sua_consulta_agora": "Secure your consultation now!", "escolha_um_dos_melhores_nutricionistas": "Choose one of the best nutritionists in the country to guide you to your best version.", "garantir_agora": "Secure now", "agende_sua_primeira_consulta": "Schedule your first consultation!", "escolha_o_profissional_que_mais_combina_com_voce": "Choose the professional that best suits you.", "seu_plano_alimentar_ja_vem": "Your meal plan is already included!", "o_plano_alimentar_esta_em_producao": "The meal plan is in production. It will be available for you within up to 4 business days.", "nao_deixe_esse_novo_capitulo_da_sua_jornada": "Don't let this new chapter of your fitness journey end here.", "assine_o_vitio_agora": "Subscribe to Vitio now and get 4 consultations with your nutritionist, meal plans, and much more!", "adquira_seu_pacote": "Get your Nutrition Package now", "musculo": "Muscle", "medida": "Measurement", "veja_uma_analise_detalhada": "See a detailed analysis of each aspect of your body.", "visualizar_avaliacao": "View assessment", "voce_ainda_tem_1_moeda": "You still have 1 non-accumulative coin to use for consultations this month.", "voce_nao_possui_mais_moedas": "You no longer have Vitio coins. Check below for when more will arrive!", "novas_moedas_em": "New coins on:", "aguarde_suas_moedas": "Wait for your coins", "consultas_disponiveis": "Available consultations", "voce_ainda_tem_2_moedas": "You still have 2 non-accumulative coins to use for consultations this month.", "adquira_seu_pacote_de_nutrica": "Get your Nutrition Package", "aumentar_saldo": "Top up balance", "mais_receitas": "More Recipes", "descubra_receitas_incriveis": "Discover incredible recipes", "descubra_agora": "Discover now", "conheca_quem_vai_mudar_a_sua_vida": "Meet those who will change your life:", "os_melhores_nutricionistas_do_pais": "The best nutritionists in the country will give you the body and health you've always wanted", "aqui_no_vitio_voce_tera": "Here at Vitio you will have:", "os_7_passos_para_alcancar_sua_melhor_versao": "The 7 steps to achieve your best version:", "perguntas_e_respostas": "Questions and Answers", "consultas_online": "Online \nConsultations", "plano_alimentar": "Meal \nPlanr", "evolucao": "Evolution", "tudo_o_que_voce_presica_esta_aqui": "Everything you\n   need is here:", "so_treinar_nao_da": "Training isn't enough.", "economize": "Save", "chat_com_nutricionista": "Chat with a nutritionist", "a_nutricao_te_faz_avancar": "Nutrition propels you forward.", "seu_nutricionista_analisa_seu_caso": "Your nutritionist analyzes your case and charts the ideal journey to reach your goal with quality and speed.", "tenha_resultados_duradouros": "Achieve lasting results with affordable and intelligent nutrition!", "tenho_plano_de_saude": "I have health insurance. Is <PERSON><PERSON><PERSON> still for me?", "o_vitio_e_uma_solucao_inovadora": "Vitio is an innovative solution to provide the highest quality nutritional support, tailored to the patient's routine, all for a much lower investment than any other available today.", "como_faco_a_consulta": "How do I schedule a consultation?", "e_so_escolher_o_nutricionista": "You just need to choose the nutritionist with a profile that fits your case, schedule the date and time of the consultation, be on the app at the appointed time to join the video call, and participate in the consultation. All within your app.", "a_cobranca_e_mensal": "Is the billing monthly?", "e_feita_a_assinatura_do_pacote": "A subscription to the package is made at the full value to ensure annual support. However, it's divided into monthly installments to facilitate the patient's enrollment without occupying the card limit.", "por_que_o_vitio_e_melhor": "Why is <PERSON>iti<PERSON> better than in-person consultation?", "porque_tem_toda_a_facilidade": "Why is <PERSON>iti<PERSON> better than in-person consultation?", "qual_o_seu_objetivo_com_o_acompanhamento": "What is your goal with nutritional guidance?", "proxima_refeicao": "Next meal", "ate_amanha": "See you tomorrow!", "voce_completou_a_dieta_do_dia": "You've completed today's diet. Come with full focus tomorrow!", "sem_titulo": "Untitled", "sem_descricao": "No Description", "consumido": "Consumed", "nao_segui_a_refeicao": "I didn't follow the meal", "desempenho_atual": "Current performance", "otimo": "Great", "95%_a_100%": "95% to 100%", "85%_a_94_9%": "85% to 94.9%", "75%_a_84_9%": "75% to 84.9%", "60%_a_74_9%": "60% to 74.9%", "40%_a_59_9%": "40% to 59.9%", "20%_a_39_9%": "20% to 39.9%", "0%_a_19_9%": "0% to 19.9%", "do_plano_seguido": "from the followed plan", "muito_bom": "very good", "bom": "good", "regular": "average", "meu_desempenho": "My performance", "opcao": "option", "desempenho_plano": "Plan performance", "proximo_horario_disponivel": "Next available time", "perfil_do_nutricionista": "Nutritionist's profile", "sem_bio": "No Bio", "filtrar_por": "Filter by", "pendencias_encontradas": "Issues found", "existem_parcelas_vencidas_no_seu_contrato": "There are overdue installments on your contract and it's not possible to proceed with the training. \nPlease contact your consultant.", "seu_programa_de_treino_chegou_ao_fim": "your training program has come to an end. Please speak with your instructor or schedule a renewal.", "seu_treino_chegou_ao_fim": "Your training has come to an end.", "todos_os_treinos_ja_foram_realizados": "all training sessions have been completed. Please contact your instructor or schedule a renewal.", "o_seu_professor_ainda_nao_gerou_seu_programa": "Your instructor has not yet generated your training program. Please contact your instructor to start training.", "nao_foi_possivel_validar_programa_de_treino": "We couldn't validate your training program. Please try again later.", "ops_nao_foi_possivel_consultar": "Oops! It wasn't possible to retrieve.", "analise_segmentar": "Segmental Analysis", "peso_real": "Actual weight", "aguarde_x_segundo": "Wait {} second", "aguarde_x_segundos": "Wait {} seconds", "desfazer_edicao_atual": "Undo current edit", "regiao_muscular": "Muscular region", "treinos_disponiveis": "Available workouts", "nao_compartilhar": "Do not share", "avaliacao_enviada": "rate sent", "voce_gostaria_de_compartilhar": "Would you like to share your result on the feed?", "confirmar_presenca?": "Confirm attendance?", "voce_sera_incluido_na_aula": "You will be included in class {} at {} on the {}.", "cheia": "full", "busque_pelo_nome": "Search by name", "fila_de_espera": "Waiting line", "aluno_ja_se_encontra_na_fila_de_espera": "The student is already in the waiting list!", "novo_personal_record": "New Personal Record", "agendamentos_disponiveis": "Available appointments", "veja_seu_progresso_e_melhore": "See your progress and enhance your training performance", "ideal_para_montar_uma_nova_ficha": "See your progress and enhance your training performance.", "continue_a_execucao_do_treino": "Continue with the previously prescribed workout.", "marque_um_horario_para_conversar_com_esse_profissional": "Schedule a time to talk with this professional.", "reavalie_seu_treino_e_realize": "Reevaluate your workout and make adjustments for your continued progress.", "prescricao_de_treino": "Workout prescription", "selecione_um_professor": "Select a Teacher", "as": "at", "sem_grupos_musculares": "Without muscle groups", "exercicio_em_serie": "Exercise in sets", "enviar_avaliacao": "Submit review", "exercicio_sem_serie": "Exercise without sets", "nao_ha_avaliacao_fisica_disponivel": "There is no physical assessment available. Please try again later", "falta_x_dias_para_realizar_sua_avaliacao": "There are {} days left until your evaluation. Please come to the specified location and follow the evaluator's instructions.", "falta_1_dia_para_realizarmos_sua_avaliacao": "One day left for your evaluation. Please come to the specified location and follow the assessor's instructions.", "a_sua_consulta_e_hoje": "Your appointment is today. Please come to the specified location and follow the assessor's instructions.", "o_percentual_magro": "The lean percentage encompasses other fat-free tissues.", "o_percentual_gordo": "The fat percentage refers to the total percentage of body fat.", "gordo": "Fat", "magro": "lean", "comparar": "compare", "nivel_gordura_visceral_baixo": "Your level of visceral fat is low, keep it up", "nivel_gordura_visceral_normal": "Your level of visceral fat is within a normal range", "nivel_gordura_visceral_elevado": "Your level of visceral fat is elevated. Trying to maintain a healthy routine can help with this", "dados_indisponiveis": "Data unavailable", "parcela": "Installment", "parcelas": "Installments", "Mes": "Month", "nenhum_cartao": "No card", "trancar_plano": "Freeze plan", "produto": "Product", "motivo": "Reason", "periodo_de_ferias": "Vacation period", "cobranca": "Billing", "previsao_da_foto": "Photo preview", "habilita_uma_foto_previa": "Enable a preview photo of the exercise in the running list", "concluir_a_execucao": "Automatically finish training execution after being inactive for:", "suporte": "Suport", "agite_o_telefone": "Shake the phone to report", "permitir_que_ao_agitar": "Allow shaking the phone on any screen to open the app support.", "portugues": "Portuguese", "espanhol": "Spanish", "ingles": "English", "falha_ao_solicitar_as_ferias": "Failed to request vacation!", "renovar_plano": "Renew plan", "ola": "hello", "programa_de_treino_chegou_fim!": "A training program has come to an end!", "text_fale_professor_agende_renovacao_treino": "Speak with your teacher or schedule the renewal of your training.", "text_fale_professor_solicite_renovacao_treino": "Talk to your teacher and request the renewal of the training.", "programa_de_treino": "Training program", "valido_entre_ate": "Valid from {} to {}", "ultimos_treinos": "Last workouts", "sem_treinos_executados": "No workouts executed!", "text_nao_realizou_treino_periodo": "You haven't completed any workouts during this period.", "encerrar_treino?": "Finish workout?", "text_certeza_encerrar_treino_parcialmente": "Are you sure you want to finish the workout partially?", "as_com_espaco": " at ", "direita_abrev": "L", "esquerda_abrev": "R", "meus_pontos": "my points", "ponto": "point", "pontos": "points", "acumule_pontos_no_clube_de_vantagens": "Accumulate points in the gym's Advantage Club to exchange for exclusive benefits. Keep exercising and make the most of it!", "horario_de_inicio": "Start time", "sem_avaliacao_fisica!": "No physical assessment!", "vc_nao_possui_aval_fis_registradas": "You don't have any recorded physical assessments.", "portugues_pt": "Portuguese (Portugal)", "portugues_br": "Portuguese (Brazil)", "Em Aberto": "Open", "Pago": "Paid", "Vencida": "Overdue", "data_consulta_dias": "*The consultation date spans {} days", "inf_importantes_prof_planejamento": "This information is important for your coach to define the best fitness plan for you.", "ative_not_treino_pronto": "Enable notifications to be notified when your workout is ready.", "seu_treino_quase_pronto": "Your workout is almost ready!", "seu_plano_disponivel_comecar": "Your training plan has been created and is now available to start.", "seu_treino_esta_pronto": "Your workout is ready!", "voce_tem_um_agendamento_para_hoje": "You have an appointment for today", "strings": "strings", "SIM": "YES", "NO": "NO", "reposicao_disponiveis": "available replacements", "o_seu_contrato_esta_disponivel": "Your contract with code {} is available and requires your signature. Sign it now and gain access to all the app features.", "agendamento_cancelado_com_sucesso": "Appointment successfully canceled", "clicking_the_button_recommend": "By clicking the button labeled '<PERSON><PERSON>,' you will be directed to the page containing your code. You can copy it or send it via your preferred method, selecting an option below (SMS, Whatsapp, or others).", "muscles_worked": "Worked Muscle Groups", "repeat_tutorial:": "Repeat the guided tutorial", "TelaVerMiaProgramaTreino": "ScreenSeeMoreWorkoutProgram", "holandes": "Dutch", "personal": "Personal", "records": "Records", "titulo_sem_not": "No notifications", "comece_sua_nova_jornada": "Begin your new journey", "novo_contrato_disponivel_para_voce": "New contract available for you!", "o_seu_contrato_cod": "Your contract code {} is available and needs your signature.", "siga_seguintes_passos": "Follow the next steps:", "revise_dados_contrato": "Review the contract details.", "cadastre_foto_perfil": "Register your profile picture", "sair_aplicativo": "Exit the application", "ao_clicar_sair_dados_serao_perdidos": "By clicking exit, you will no longer be logged into the app, and unsaved data will be lost.", "atualizar_dados": "Update data", "pendente": "Pending", "contrato": "Contract", "confira_termos_contrato_assine_acesso_recursos": "Check the contract terms and sign to access all app features", "termos_contrato": "Contract terms", "assinatura_contrato": "Contract signature", "opcional": "Optional", "atualize_foto_perfil": "Update your profile picture", "atualizar_foto": "update photo", "acesso_unidade": "Access to unit", "titulo_personal_records": "Personal Records", "o_que_vc_esta_procurando": "What are you looking for?", "frances": "French", "concluir_todos": "Complete all", "nao_gostei_programa_treino_ia": "I didn’t like my program", "gerar_novo_programa_ia": "Generate a new workout program with AI", "renovar_treino_ia": "Renew training by AI", "refazer_amamnese_treino": "Redo training assessment to generate workout", "voce_possui_problemas_listados_abaixo": "Do you have any of the problems listed below?", "problemas_cardiacos": "Heart problems", "problemas_nas_costas_coluna": "Back or spinal problems", "problemas_joelhos": "Knee problems", "lesoes_fisicas": "Physical injuries", "restricao_medica_ativ_fis": "Medical restriction for physical activity", "outras_restricoes_limitacoes_ativ_fis": "Other restrictions or limitations for physical activities", "sim_possu_restricoes_listadas_acima": "Yes, I have some of the restrictions listed above.", "nao_possuo_restricoes": "No, I don't have any restrictions.", "atestado_aptidao_fisica": "Physical fitness certificate", "voce_atesta_nao_possuir_restricoes_fis": "Do you certify that you have no physical restrictions for physical activities?", "revisar": "Review", "selecionado": "Selected", "ainda_nao_treino": "I still don't train", "nao_tenho_coord_suf_exe_aju_correcoes": "I don't have sufficient coordination to do exercises and need help or corrections", "nao_consigo_sentir_musculo_traba_durante_exerc": "I can't feel the target muscle working during the exercise", "consigo_exec_satisf_exer": "I can execute all exercises satisfactorily", "consigo_sentir_musculos_corret": "I can feel the muscles working correctly", "possuo_forca_signif": "I have significant strength", "executo_perf_todos_exerc_consigo_musc_trabalhando": "I execute all exercises perfectly and can feel the target muscles working", "compreendo_exe_exerc_alinhar_corpo_musculos": "I understand the execution of exercises and how to align the body to target specific muscle parts", "ja_exper_tec_intensidade": "I already have experience with various high-intensity techniques", "como_voce_classifica_exp_treino": "How do you rate your training experience?", "Nunca treinou": "Never trained", "Estou voltando a treinar agora": "I'm returning to training now", "Treino a menos de 6 meses": "Training for less than 6 months", "Já treinei antes estou voltando": "I've trained before, I'm coming back", "Já treino entre 6 meses e 1 ano": "I've been training for 6 months to 1 year", "Já treino entre 1 ano e 2 anos": "I've been training for 1 to 2 years", "Já treino a mais de 2 anos": "I've been training for more than 2 years", "Sou bodybuilder ou atleta profissional ": "I'm a bodybuilder or professional athlete", "alemao": "German", "Parcela 1": "Installment 1", "Parcela 2": "Installment 2", "Parcela 3": "Installment 3", "Parcela 4": "Installment 4", "Parcela 5": "Installment 5", "Parcela 6": "Installment 6", "Parcela 7": "Installment 7", "Parcela 8": "Installment 8", "Parcela 9": "Installment 9", "Parcela 10": "Installment 10", "Parcela 11": "Installment 11", "Parcela 12": "Installment 12", "sem_dados": "No data", "1 série": "1 set", "2 séries": "2 sets", "3 séries": "3 sets", "4 séries": "4 sets", "5 séries": "5 sets", "6 séries": "6 sets", "7 séries": "7 sets", "8 séries": "8 sets", "9 séries": "9 sets", "10 séries": "10 sets", "voce_possui_reposicoes_disponiveis": "You have available replacements", "ate_o_final_do_seu_plano": "until the end of your plan", "infelizmente_gerar_treino": "Unfortunately, we cannot \n generate  your workout", "entre_contato_prof_info_restricoes_treino_personalizado": "Contact your instructor, provide your medical restrictions, and request a personalized workout.", "desculpe_ocorreu_incosc_login": "Sorry, an inconsistency occurred during login. Please try again.", "revisar_treino_ia": "Review I.A. Workout", "aprovar": "Approve", "aprovar_treino": "Approve Workout", "deseja_aprovar_treino_sem_revisar": "Do you want to approve the workout without reviewing it?", "todos_treinos_foram_revisados": "All workouts have been reviewed!", "no_momento_nao_treino_revisao": "Currently, there are no workouts awaiting review.", "expira_em": "Expires in:", "deseja_fechar_app": "Do you want to close the app?", "ao_clicar_fechar_app_dados_perdidos": "By clicking close app, unsaved data will be lost.", "fechar_app": "Close App", "nao_foi_possivel_excluir_treino": "Unable to delete the workout, please try again later.", "para_adicionar_video_add_url": "To add the video, you need to enter a valid URL.", "falha_salvar": "Failed to save", "insira_url_valida": "Enter a valid URL", "voce_nao_possui_aut_acessar_treinos_entre_contato_adm": "You don't have authorization to access home workouts. Contact the administration to resolve.", "revistar_programa": "Review Program", "programas_gerados": "Generated Programs", "inicio": "Start", "termino": "End", "//////////////\\\\\\\\\\\\\\\\\\\\ ↓↓↓ ATENÇÃO ↓↓↓": "↓↓↓ ATENÇÃO ↓↓↓", "/////\\\\ INFO=>": "Tradução atualizada!!!. Se estiver adicionando novas traduções, inclua-as abaixo. Ao realizar modificações ou adicionar traduções, certifique-se de replicar essas alterações para os demais idiomas.", "//////////////\\\\\\\\\\\\\\\\\\\\ ↑↑↑ ATENÇÃO ↑↑↑": "↓↓↓ ATENÇÃO ↓↓↓", "procure_por_professor": "Search for teacher", "nao_foi_possivel_cadastrar_tente_novamente": "Registration was not possible. Please try again.", "excluir_programa_aluno": "Your user does not have permission to delete a training program.", "editar_programa_aluno": "Your user does not have permission to edit a training program.", "incluir_programa_aluno": "Your user does not have permission to include a training program.", "renovar_programa_aluno": "Your user does not have permission to renew a training program.", "consultar_programa_aluno": "Your user does not have permission to view a training program.", "renovacaoAutomatica": "Your plan is already set for automatic renewal, preventing manual renewal.", "visualizacao_das_aulas": "Class Viewing", "agrupar_por": "Group by", "mostar_aulas_cheias": "Show full classes", "horario_das_aulas": "Class schedule", "conversorDePeso": {"Conversor_de_peso": "Weight converter", "Conversor_Kg_lb": "Lb / Kg converter", "clique_nos_cantos_ou_desliza": "Click on the corners or slide to calculate", "converter": "Convert"}, "de_ativas": "{} out of {} active", "aulas_favoritas": "Favorite classes", "e_preciso_conceder_permissao_para_acessar_o_armazenamento": "Permission is required to access internal storage", "acesse_as_configuracoes_para_conceder_permissoes": "Access settings to grant necessary permissions", "ja_concedi": "I already granted", "aulas_agendadas_hoje": "Scheduled classes for today", "cref_vencido": "Your CREF has expired.", "cref_vencido_texto": "Rectify your situation to gain access. If it's already rectified, please contact administration.", "cadastro_inativo": "This user is deactivated.", "cadastro_inativo_texto": "Your user has been deactivated by the gym. If you need assistance or have questions about the process, please contact the administration of the unit you are affiliated with.", "ha": "there is", "sem_registro_acesso": "No recent input records", "todas_as_carteiras": "All portfolios", "sua_carteira": "Your portfolio", "filtro": "Filter", "ultimos_acessos": "Last accesses", "nivel_atual": "Current level:", "alunos_ja_acompanhados": "Some students are already being accompanied. Would you like to replace these students with new ones?", "subistituir_alunos": "Replace the students", "manter_alunos": "Keep the students", "primeiro": "First", "segundo": "Second", "terceiro": "Third", "treinar": "Train", "ficha_sem_execucao": "Sheet without execution", "avaliar": "Evaluate", "avaliar_wod": "Evaluate the WOD", "avaliar_geral_wod": "General evaluation of the WOD", "percepcao_esforco": "What is your perception of effort?", "menos_esforco": "Less effort", "mais_esforco": "More effort", "muito_leve": "Very light", "pouco_leve": "A little light", "leve": "Light", "leve_moderado": "Light to moderate", "moderado": "Moderate", "moderado_intenso": "Moderate to intense", "pouco_intenso": "A little intense", "intenso": "Intense", "muito_intenso": "Very intense", "extremo": "Extreme", "ops_parece_que_esta_faltando_algo": "Oops, it looks like something went wrong!", "tamanho_resolucao_imagem": "Image size and resolution", "text_capa_personalizada": "The image will be used as a preview for the workout. We recommend that the custom cover has", "text_resolucao_min": "Resolution of 1280x720 (with a minimum width of 640 pixels)", "text_formato": "Format in JPG or PNG", "text_resolucao_max": "Maximum resolution of 2 MB", "text_proporcao": "Aspect ratio of 16:9", "ok_entendi": "Ok, got it!", "capa_teino": "Workout cover", "adicionar_outro_video": "Add another video", "url_video_youtube": "YouTube video URL", "url_live_youtube": "YouTube Live URL", "nome_treino": "Workout name", "criar_treino_ao_vivo": "Create live workout", "criar_treino_online": "Create online workout", "cadastrar_treino": "Register workout", "treino_ao_vivo": "Live workout", "validar_checkin": "Validate Check-in", "validar_checkin_totalPass": "Validate Check-in TotalPass", "falha_validar_totalPass": "TotalPass validation failure", "nao_existem_dados_validos_termos": "No valid data exists to accept the terms and conditions. Please go to the reception to update your data or documents.\n\nTo access the application, you must accept the terms and policies.", "titulo_alerta_marcacao": "Are you sure you want to mark attendance for all students?", "subtitulo_alerta_marcacao": "By proceeding, you acknowledge that all students on the list will be confirmed as present in the class.", "marca_presenca_todos": "Mark attendance for all", "nenhuma_aula_encontrada_empty_state": "No classes found", "nao_foram_encontradas_aulas_no_periodo": "No classes were found registered for the selected period.", "em_aberto": "Open", "respondido": "Answered", "botao_responder_parq": "Answer Par-Q", "botao_ver_meu_historico": "View My History", "texto_formulario_historico_parq": "Par-Q History", "parcelas_selecionadas": "Selected installments", "tipo_agendamento": "Type of scheduling", "veja_suas_aulas_realizadas": "View your completed lessons", "nivel_de_dificuldade": "Difficulty level", "anilhas": "Weight plates", "halters": "Dumb<PERSON>s", "fita_supensa": "Suspension strap", "barra_fixa": "Pull-up bar", "corda": "<PERSON><PERSON>", "superband": "Superband", "miniband": "Miniband", "colchonete": "Mat", "tornozeleira": "Ankle weight", "bola_suica": "Swiss ball", "roda": "Ab wheel", "criar_treino": "Create workout", "usar_imagem_recente": "Use recent image", "realizou": "performed", "detalhes_do_treino": "Workout details", "quem_realizou_o_treino": "Who performed the workout", "excluir_treino": "Delete workout", "deseja_realmente_excluir_este_treino?": "Do you really want to delete this workout?", "cancelar": "Cancel", "execucoes_realizadas": "Executions performed", "manter_peso": "Maintain weight", "grupos_musculares": "Muscle groups", "descricao": "Description", "editar_treino": "Edit workout", "assistir_treino": "Watch workout", "data_do_treino_ao_vivo": "Live workout date", "confirmar_data": "Confirm date", "hora_inicio_treino_ao_vivo": "Live workout start time", "hora_inicio_treino": "Workout start time", "confirmar_horario": "Confirm time", "carregando": "Loading", "encerrado": "Closed", "programado": "Scheduled", "Ganhar massa": "Gain mass", "Emagrecer": "Lose weight", "Manter o peso": "Maintain weight", "Iniciante": "<PERSON><PERSON><PERSON>", "Intermediario": "Intermediate", "abdomen": "Abdomen", "Abdomen": "Abdomen", "Braços": "Arms", "Costas": "Back", "Sem equipamentos": "No equipment", "Anilhas": "Weight plates", "Halters": "Dumb<PERSON>s", "Fita suspensa": "Suspension strap", "Barra fixa": "Pull-up bar", "Corda": "<PERSON><PERSON>", "Superband": "Superband", "Miniband": "Miniband", "Colchonete": "Mat", "Tornozeleira": "Ankle weight", "Bola suíça": "Swiss ball", "Roda": "Ab wheel", "url_ja_adicionada": "URL already added", "texto_validar_codigo_email": "Enter the 5-digit code sent to your email to validate your account.", "remover_fila": "Remove from the queue", "deseja_remover_fila": "Do you want to remove from the queue?", "posicao": "Position", "deseja_sair_fila": "Do you want to leave the queue?", "falha_remover_fila": "Failed to remove from the queue", "Sair_da_fila": "Leave the queue", "acesso_camera_microfone": "Unable to access the camera because permissions are disabled.", "acesso_camera_microfone_subtitulo": "Go to your device settings to grant the necessary permissions and try again", "abrir_ajustes": "Open settings", "dependente": "Dependent", "recente": "Recent", "falha_reservar_equipamento": "Failed to reserve equipment", "editar_reserva": "Edit reservation", "selecionar_equipamento": "Select equipment", "reserva_aviso": "The arrangement of the equipment is illustrative and may be changed without prior notice.", "reservador": "Reserved", "equipamento_selecionado": "Selected equipment", "cancelar_reserva": "Cancel reservation", "salvar_edicao": "Save edit", "confirmar_reserva": "Confirm reservation", "reserva_text": "Select equipment to reserve.", "reserva_de_equipamentos": "Equipment reservation", "equipamento_reservado": "Reserved equipment", "equipamento_livre": "Free equipment", "erro_ao_carregar_observacoes": "Error when querying observations, please try again later", "alterar_nivel": "Change level", "Avançado 1": "Advanced 1", "Avançado 2": "Advanced 2", "Iniciante 1": "Beginner 1", "Iniciante 2": "Beginner 2", "Intermediário 1": "Intermediate 1", "Intermediário 2": "Intermediate 2", "meus_treinos": "My workouts", "fez_um_treino_extra": "Did an extra workout?", "ultimos_treinos_realizados": "Last workouts performed", "histroico_treinos": "Workout history", "registrar_exercicio_extra": "Log extra exercise", "data_do_treino": "Workout date", "calorias_gastas_kcal": "Calories burned (kcal)", "duracao_treino_min": "Workout duration (min)", "distancia_mt": "Distance (m)", "voltas": "Laps", "escolha_um_exercicio": "Choose an exercise", "tempo_calorias_distância": "Time, calories, and distance", "tempo_calorias": "Time and calories", "tempo_calorias_voltas": "Time, calories, and laps", "selecione_ficha_programa_atual": "Select the current program sheet", "alongamento": "Stretching", "arremesso_de_disco": "Discus throw", "artes_marciais": "Martial arts", "basquete": "Basketball", "caminhada_ao_ar_livre": "Outdoor walking", "caminhada_com_carrinho_de_bebe": "Walking with a stroller", "caminhada_com_peso": "Weighted walking", "caminhada_em_esteira": "Treadmill walking", "caminhada_em_montanhas": "Mountain hiking", "ciclismo_ao_ar_livre": "Outdoor cycling", "ciclismo_indoor": "Indoor cycling", "corrida_ao_ar_livre": "Outdoor running", "corrida_na_esteira": "Treadmill running", "danca": "Dancing", "eliptico": "Elliptical", "escalada": "Climbing", "escalador": "Stair climber", "esportes_de_raquete": "Racquet sports", "futebol": "Soccer", "golfe": "Golf", "hiit": "HIIT", "hoquei_no_gelo": "Ice hockey", "ioga": "Yoga", "meditacao": "Meditation", "mergulho": "Diving", "natacao_em_aguas_abertas": "Open water swimming", "natacao_em_piscina": "Pool swimming", "patinacao": "Skating", "pilates": "Pilates", "pular_corda": "Jump rope", "remo_indoor": "Indoor rowing", "treinamento_funcional": "Functional training", "treinamento_de_forca": "Strength training", "volei": "Volleyball", "outros_personalizado": "Others (customized)", "informe_cartao_completo": "* To edit the information, it is necessary to fill in the card details. This ensures the security and validation of the changes. Enter the data correctly to continue.", "login_rapido_titulo": "Please wait a moment!", "login_rapido_subtitulo": "We're logging in and getting everything ready for you.", "login_rapido_info_sem_usuario": "After logging in with another user or accessing a dependent's profile, they will be displayed here to facilitate quick access between profiles.", "midias_enviadas_e_recebidas": "Sent and received media", "midias_enviadas": "Sent media", "midias_enviadas_e_recebidas_vazio": "No media has been shared in this conversation. When you send or receive files, photos, or videos, they will appear here.", "copiar": "Copy", "resumo_de_aulas": "Class summary", "aulas_realizadas": "Completed \nclasses", "aulas_do_mes": "Classes this \nmonth", "semanas_consecutivas_com_presenca": "Consecutive weeks with attendance", "texto_semanas_consecutivas_com_presenca": "Unable to load completed class data. Please refresh the page.", "recarregar": "Reload", "clube_de_vantagens": "Rewards Club", "ver_brindes_disponiveis": "View available rewards", "brindes_disponiveis_para_resgate": "Rewards available for redemption", "nao_encontramos_nenhum_brinde_disponivel": "We couldn't find any rewards available for redemption at the moment.", "text_subtitulo_brindes": "Redeem your reward at the reception desk! Let them know which reward you want and show your accumulated points.", "lista_de_presenca": "Attendance list", "confirmar_presenca_do_aluno_selecionado": "Confirm student's attendance", "confirmar_presenca_dos_alunos_selecionados": "Confirm attendance of {} students", "confirmado": "Confirmed", "aguardando_confirmacao": "Awaiting confirmation", "pagar_parcelas": "Pay installments", "refazer": "Redo", "concluida": "completed", "concluidas": "completed", "meus_creditos_descricao": "From reschedules and credits", "aulas_coletivas": "Group Classes", "utilizados": "Used", "expirados": "Expired", "turmas": "Groups", "tenis": "Tennis", "tenis_de_mesa": "Table Tennis", "beach_tennis": "Beach Tennis", "padel": "Pa<PERSON>", "badminton": "Bad<PERSON>ton", "squash": "Squash", "frescobol": "Frescobol", "selecione_uma_ficha": "Select a sheet", "titulo_avaliacao_professores": "Teacher Evaluation", "info_avaliacao_professores": "You can only evaluate teachers linked to your profile once every 30 days.", "avaliacao_pendente": "Pending Evaluation", "avaliacao": "Evaluation", "data_avaliacao": "Evaluation Date", "comentario_opcional": "Comment (optional)", "hint_comentario_opcional": "Type your comment here. Describe your experience with the teacher. Maximum 500 characters.", "salvar_avaliacao": "Save Evaluation", "por_favor_avalie_professor": "Please evaluate the teacher", "nao_existem_niveis_cadastrados_ou_ativos_no_momento": "There are no registered or active levels at the moment.", "chat_maisc": "Cha<PERSON>", "msn_chat": "Hello! It seems that no chat has been started. If you need help or have any questions, please start a new conversation. We are here to help!", "iniciar_chat": "Start chat", "erro_ao_carregar_chats": "Error loading chats.", "nenhum_resultado_encontrado": "No results found", "text_pesquisa_chat": "We couldn't find any chat with the searched term. Please try again.", "boleto_existente_titulo": "A boleto has already been generated and is awaiting payment!", "boleto_existente_descricao": "You can pay the boleto by copying the barcode below. To generate a new one, request the cancellation of the current boleto.", "copiar_codigo": "Copy code", "boleto_vencimento_texto": "You have until {0} of {1} to pay.", "boleto_gerado_sucesso": "Boleto generated!", "visualizar_boleto": "View boleto", "boleto_compensacao_aviso": "The boleto may be cleared within up to 72 hours after payment, excluding weekends and holidays.", "gerar_boleto": "Generate boleto", "clique_gerar_boleto": "Click the button below to generate the boleto", "sem_alunos_disponiveis": "No students available", "titulo_adicionar_aluno": "Add student to class", "confirma_adicionar_aluno": "Are you sure you want to add student {al<PERSON>} to class {aula}?", "recarregar_consulta": "Reload", "titulo_boleto_indisponivel": "No boleto available at the moment", "mensagem_boleto_indisponivel": "To pay via boleto, please contact the facility to request it or choose another available payment method.", "codigo_copiado_sucesso": "Code copied successfully!", "renovar_criar_meu_programa_de_treino": "Renew/Create my workout program", "nao_gostei_do_meu_programa": "I didn't like my program", "atalho": "Shortcut", "saude_e_bem_estar": "Health and wellness", "dietas_e_alimentacoes": "Diets and nutrition", "motivacoes_e_inspiracoes": "Motivations and inspirations", "imagens_e_referencias": "Images and references", "sites_e_blogs": "Websites and blogs", "meu_link_titulo": "My link", "botao_salvar": "Save", "botao_excluir": "Delete", "mensagem_link_salvo": "<PERSON> saved successfully", "mensagem_link_removido": "Link removed successfully", "descricao_link_privado": "Your link is private and visible only to you. Use this feature to quickly access personalized links such as physical assessments, meal plans, exercise videos, and more.", "label_nome_link": "Link name*", "hint_nome_link": "My meal plan", "label_url": "URL*", "hint_url": "https://www.example.com", "botao_colar": "Paste", "label_icone_atalho": "Shortcut icon", "titulo_icone_atalho": "Shortcut icons", "excluir_link": "Delete link", "excluir_link_mensagem": "Are you sure you want to delete this link?", "voce_ainda_nao_realizou_nenhum_treino": "You haven't completed any workouts yet", "assim_que_voce_completar_seu_primeiro_treino_ele_aparecera_aqui": "Once you complete your first workout, it will appear here.", "checkin_validado_sucesso": "Check-in validated successfully", "avaliacao_professor_sucesso": "Teacher evaluation completed successfully", "pr_lancado_sucesso": "PR launched successfully", "senha_recuperada": "Password recovered successfully", "treinoOnline_excluido_sucesso": "Online workout deleted successfully", "plano_inativo_titulo": "Inactive plan", "plano_inativo_mensagem": "This plan is currently inactive and cannot be renewed. Please contact reception for more information.", "hipertrofia": "Hipertrophy", "forca": "Strength", "nunca_treinei_ou_ja_treinei_e_estou_voltando_agora": "Never trained or I'm coming back now", "ja_treino_entre_6_meses_e_1_ano": "I've been training for 6 months to 1 year", "ja_treino_ha_mais_de_1_ano": "I've been training for more than 1 year", "professores": "Teachers", "outras_opcoes": "Other options", "aditivo": "Addendum", "aditivos": "Addendums", "confira_os_termos_do_aditivo_e_assine_para_ter_acesso_a_todos_os_recursos_do_app": "Check the addendum terms and sign to get access to all app features", "termos_do_aditivo": "Addendum terms", "assinatura_aditivo": "Addendum signature", "erro_ao_processar": "Error processing", "ocorreu_um_erro_ao_processar_a_assinatura_tente_novamente": "An error occurred while processing the signature, please try again", "assinatura_pendente": "Pending signature", "nao_foi_possivel_enviar_a_assinatura_verifique_sua_conexao_e_tente_novamente": "It was not possible to send the signature, check your connection and try again", "assine_o_contrato_para_continuar": "Sign the contract to continue", "ver_mais_aditivos": "View more addendums", "contrato_assinado_com_sucesso": "Contract signed successfully!", "aditivo_assinado_com_sucesso": "Addendum signed successfully!", "contrato_precisa_assinatura_responsavel": "You are a minor and therefore cannot sign the contract alone. Please come to the gym reception accompanied by your guardian to complete the signature and enable app usage.", "novo_aditivo_disponivel_para_voce": "New addendum available for you!", "o_seu_aditivo_cod": "Your addendum code {} is available and needs your signature. Sign now and get access to all app features", "assinar_aditivo": "Sign addendum", "tempo_de_aula": "Class duration", "gasto_calorico": "Calorie expenditure", "potencia_media": "Average power", "menos_detalhes": "Less details", "mais_detalhes": "More details", "parq_positivo": "Positive Par-Q", "selfie_validada_com_sucesso!": "<PERSON><PERSON> successfully validated!", "envie_uma_selfie_com_um_documento_oficial": "Send a selfie with an official document.", "escolha_uma_forma_de_autenticacao_de_dois_fatores": "Choose a two-factor authentication method.", "assine_aditivo": "Sign the addendum", "assine_contrato": "Sign the contract", "adicione_uma_foto_de_perfil_opcional": "Add a profile photo. (Optional)", "selfie_com_documento": "Selfie with document", "autenticar": "Authenticate", "autenticacacao_de_dois_fatores": "Two-factor authentication", "autenticar_assinatura": "Authenticate signature", "text_esolha_autenticacao": "Choose a way to receive the authentication code to validate your signature.", "sms": "SMS", "nova_foto": "New photo", "verificando": "Verifying...", "verificando_documento_e_rosto": "Verifying document and face...", "analisando_a_imagem": "Analyzing the image...", "procurando_por_rosto": "Looking for face...", "text_falhou_imagem_rosto": "No face detected in the image. Please make sure your face is visible.", "text_iluminacao_imagem": "The image may be poorly lit or we can't clearly see your face or the document. Try again in a well-lit place and make sure both are visible.", "procurando_por_texto_no_documento": "Looking for text on the document...", "text_falhou_imagem_doc": "We couldn't identify document text in the image. Make sure your document is visible.", "verificacao_concluida_com_sucesso": "Verification completed successfully!", "texto_instrucoes": "To ensure your safety, we ask you to take a photo with your face next to your ID document. Make sure both are well lit and visible in the image.", "acessar_galeria": "Access gallery", "verefique_seu_documento_rosto": "Check if the document and your face are visible:", "falha_na_verificacao": "Verification failed", "confirmar_foto": "Confirm photo", "iniciando_camera": "Starting camera...", "camera_nao_disponivel": "Camera not available", "erro_ao_iniciar_camera": "Error starting the camera. Please check permissions and try again.", "text_alerta_instrucoes": "Please try again following the instructions.", "erro_ao_acessar_a_galeria": "Error accessing the gallery. Please check permissions and try again.", "text_erro_ao_acessar_a_galeria": "Could not access your photos. Check the app permissions and try again.", "text_erro_sms": "Could not send the verification code. Please check if the number is correct at your gym's front desk.", "erro_ao_enviar_sms": "Error sending SMS", "ocorreu_um_erro_ao_processar_sua_solicitacao": "An error occurred while processing your request. Please try again later.", "erro": "Error", "enviando_codigo_por_email": "Sending code by email...", "erro_ao_enviar_codigo": "Error sending code", "tente_novamente": "Try again.", "enviando_codigo_sms": "Sending SMS code...", "sem_dados_de_email": "No email data", "sem_dados_de_telefone": "No phone data", "text_forma_receber_cod": "Choose a way to receive the authentication code to validate your signature.", "enviaremos_um_codigo_numero": "We will send a code to the number {} so you can authenticate your contract signature.", "enviaremos_um_codigo_email": "We will send a code to the email {} so you can authenticate your contract signature.", "codigo_reenviado_email": "Code resent to email!", "codigo_reenviado_sms": "Code resent by SMS!", "erro_reenviar_sms": "Error resending SMS", "dados_autenticao_incompletos": "Incomplete authentication data. Try again.", "codigo_invalido_verifique": "Invalid code. Check and try again.", "codigo_invalido": "Invalid code", "cod_inf_nao_e_valido": "The code provided is not valid. Please check and try again.", "autenticacao": "Authentication", "autenticar_com_sms": "Authenticate with SMS", "autenticar_com_email": "Authenticate with email", "solicitacao_de_autenticacao_enviada": "Authentication request sent!", "inf_cod_seis_digitos": "Enter the 6-digit code sent to {} {}", "verificar_cod": "Verify code", "reenviar_cod": "Resend code", "aguarde_segundos": "Wait {} seconds", "aditivo_do_contrato": "Contract addendum {}", "validando_cod": "Validating code...", "cod_invalido_verifique_tente_novamente": "Invalid code. Check and try again.", "erro_ao_verificar_codigo": "Error verifying code", "processando_assinatura": "Processing signature {} of {}", "processando_assinaturas": "Processing signatures", "contrato_args": "Contract {}", "erro_no_processamento": "Processing error", "ocorreram_erro_assinaturas": "Errors occurred while processing some signatures", "veja_seus_recordes_pesos": "See your weight records", "ficha_salva_sucesso": "Workout saved successfully!", "creditos_de_treino": "Training credits", "transferir_creditos_de_treino": "Transfer training credits", "transferencia": "Transfer", "informe_o_aluno_e_quantos_creditos_deseja_tranferir": "Enter the student and how many credits you wish to transfer", "aluno_destinatario": "Recipient student", "quantidade_de_creditos": "Number of credits", "voce_possui_de_credito_de_treino": "You have {} training credits", "transferindo": "Transferring", "para:": "To:", "email": "Email", "cpf": "ID", "data_da_transferencia": "Transfer date", "transferir": "Transfer", "creditos_de_treino_transferido_com_sucesso!": "Training credits transferred successfully!", "cpf_ou_email": "ID or Email", "aluno_nao_encontrado": "Student not found", "text_aluno_nao_encontrado": "We couldn't find the student. Please check the entered data and try again. The email or ID may be incorrect.", "seu_perfil_sem_permissao": "Your profile doesn't allow access to this feature.", "aguarde_alguns_instantes": "Please wait a moment and try again", "seu_treino_esta_em_revisao": "Your workout is under review", "carrossel_treinos_aprovacao_erro_carregar": "Error loading workouts", "carrossel_treinos_aprovacao_tentar_novamente": "Try again", "carrossel_treinos_aprovacao_todos_revisados": "All workouts have been reviewed", "carrossel_treinos_aprovacao_aguardando_revisao": "Awaiting review", "carrossel_treinos_aprovacao_ver_todos": "View all", "carrossel_treinos_aprovacao_ops": "Oops", "carrossel_treinos_aprovacao_expira_em": "Expires in {}", "carrossel_treinos_aprovacao_atualizando": "Updating...", "carrossel_treinos_aprovacao_auto_30s": "Auto 30s", "carrossel_treinos_aprovacao_auto_countdown": "Auto {}s", "cancelar_contrato": {"erro_titulo": "Error cancelling", "botao_ok": "Ok", "erro_mesmo_dia": "It is not possible to cancel a contract on the same start date", "confirmacao_titulo": "Confirmation", "confirmacao_headline": "Confirm contract cancellation", "confirmacao_body": "Contract cancellation is final and cannot be undone. The cancellation date will be recorded as today {}.", "botao_voltar": "Back", "botao_confirmar": "Confirm cancellation", "botao_avancar": "Next", "titulo": "Cancellation", "atencao_titulo": "Attention", "atencao_body": "You are about to cancel your contract. This action cannot be undone and the cancellation date cannot be changed, being valid from today.", "atencao_justificativa": "To continue, select a reason for cancellation.", "data_cancelamento": "Cancellation date", "comentario_label": "Comment*", "comentario_hint": "Tell us more about why you are cancelling.", "sucesso_envio": "Request sent successfully.", "aluno_receptivo": "Receptive Student", "cliente_receptivo_nao_pode_ser_marcado_presente": "Receptive client cannot be marked as present in the app."}, "seu_treino_estara_disponivel_quando_aprovado": "Your workout will be available when approved by the trainer"}