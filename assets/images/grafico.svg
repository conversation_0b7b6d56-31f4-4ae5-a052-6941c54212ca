<svg width="145" height="152" viewBox="0 0 145 152" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_5278_45732)">
<rect x="44" y="30" width="37" height="43.1667" rx="8" fill="#DCDDDF"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M67.4264 43.4271C67.436 43.4617 67.4457 43.4962 67.4552 43.5304C67.7785 44.6885 68.0596 45.6807 68.3256 46.2854C68.3332 46.3005 68.3404 46.3144 68.3473 46.3272C68.3579 46.3272 68.3696 46.3271 68.3823 46.327C68.6067 46.3256 69.1553 46.3223 69.7128 46.5977C70.3381 46.9066 70.7672 47.4414 71.0808 48.075C71.3552 48.594 71.5936 49.2663 71.8 49.9179C72.015 50.5966 72.2269 51.3575 72.4281 52.0796L72.4324 52.0952C72.5085 52.3683 72.5827 52.6343 72.6552 52.8904C72.6883 52.7871 72.7217 52.6833 72.7552 52.5802C73.0165 51.7751 73.8737 51.3366 74.6698 51.6008C75.4659 51.865 75.8995 52.7318 75.6383 53.5369C75.5858 53.6986 75.5332 53.863 75.4795 54.0308L75.4638 54.0797C75.2403 54.7781 74.9861 55.5723 74.7176 56.1938C74.5828 56.506 74.406 56.8676 74.1757 57.1741C74.0017 57.4057 73.4844 58.0328 72.6018 58.0328C72.0401 58.0328 71.6341 57.7645 71.4103 57.5678C71.1855 57.3701 71.0265 57.1442 70.9194 56.9703C70.7032 56.6193 70.526 56.1927 70.38 55.7931C70.0865 54.9906 69.7957 53.9465 69.5244 52.9723L69.5118 52.9271C69.3057 52.1873 69.1073 51.4758 68.9103 50.854C68.7068 50.2113 68.5346 49.7633 68.3997 49.512L68.3856 49.4857L68.3726 49.459C68.3611 49.4353 68.3503 49.4143 68.3403 49.3956C68.3297 49.3956 68.318 49.3957 68.3052 49.3958C68.0808 49.3971 67.5323 49.4005 66.9747 49.1251C66.3407 48.8119 65.9084 48.2664 65.5937 47.6213L65.5824 47.5981L65.5719 47.5747C65.2566 46.8695 64.9647 45.8909 64.7043 44.9686C64.4605 46.4691 64.2114 48.2223 63.9566 50.016L63.95 50.0622C63.5483 52.8904 63.1301 55.8348 62.7072 57.8911C62.4781 59.2019 62.2337 60.2459 61.9668 61.037C61.7284 61.7434 61.3778 62.5559 60.7551 63.0422C60.3783 63.3365 59.8571 63.5435 59.2571 63.4554C58.7078 63.3747 58.311 63.0771 58.0644 62.8335C57.6171 62.3916 57.3002 61.7646 57.0655 61.1991C56.6681 60.4378 56.3038 59.4286 55.9908 58.5617L55.9641 58.4878C55.8334 58.1258 55.7102 57.7857 55.5921 57.474C55.4529 57.8417 55.3082 58.2447 55.1561 58.6694L55.1347 58.7289C54.8358 59.5636 54.5015 60.4972 54.1628 61.1969C53.5333 62.5962 52.8699 63.2911 52.3172 63.8682C51.8908 64.3134 51.592 64.6296 51.2829 65.2633C50.9122 66.0232 50.0026 66.3353 49.2511 65.9605C48.4997 65.5856 48.191 64.6657 48.5617 63.9058C49.0855 62.8319 49.658 62.2342 50.1266 61.7449L50.1437 61.7271C50.6093 61.2409 50.9916 60.8418 51.4084 59.9095L51.4189 59.886L51.4302 59.8629C51.6894 59.3315 51.9736 58.5444 52.3031 57.6244L52.3204 57.5762C52.6165 56.7493 52.9491 55.8207 53.2899 55.0971C53.4611 54.7336 53.6739 54.3343 53.9351 54.0043C54.1358 53.7507 54.6808 53.1267 55.5697 53.1267C56.4004 53.1267 56.9399 53.6555 57.1831 53.9406C57.4604 54.2657 57.6787 54.6577 57.8497 55.008C58.1879 55.7005 58.5091 56.59 58.7949 57.3817L58.8142 57.4351C59.0219 58.0101 59.2121 58.533 59.3925 58.9775C59.5004 58.5214 59.6111 57.9774 59.7231 57.3351L59.7272 57.3114L59.732 57.2878C60.1347 55.3367 60.5421 52.4732 60.9531 49.5798L60.9552 49.5652C61.2347 47.5972 61.5148 45.625 61.7906 43.9841C62.0562 42.404 62.3464 40.9378 62.6825 40.111L62.7135 40.0349L62.7523 39.9626C62.8838 39.7174 63.1162 39.353 63.5141 39.096C64.0101 38.7755 64.6228 38.7154 65.1777 38.9592C65.6196 39.1534 65.8937 39.4833 66.0363 39.6802C66.1969 39.9016 66.324 40.1437 66.4248 40.3617C66.7939 41.1602 67.1328 42.3749 67.4264 43.4271ZM56.3041 55.9216C56.3354 55.882 56.3347 55.8916 56.302 55.9243C56.3027 55.9234 56.3034 55.9225 56.3041 55.9216ZM54.8876 55.9473C54.8719 55.9317 54.8711 55.928 54.8853 55.9446C54.886 55.9455 54.8868 55.9464 54.8876 55.9473Z" fill="#03BE61"/>
<defs>
<filter id="filter0_d_5278_45732" x="0" y="0" width="145" height="151.167" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="10" dy="24"/>
<feGaussianBlur stdDeviation="27"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5278_45732"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5278_45732" result="shape"/>
</filter>
</defs>
</svg>
