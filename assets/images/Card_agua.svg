<svg width="146" height="144" viewBox="0 0 146 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_5278_45740)">
<g filter="url(#filter1_d_5278_45740)">
<rect x="44" y="30" width="37.092" height="35.9881" rx="4" fill="#008643"/>
</g>
<line opacity="0.5" x1="49.3737" y1="54.5" x2="69.0225" y2="54.5" stroke="#DCDDDF" stroke-width="3" stroke-linecap="round"/>
<line opacity="0.5" x1="49.3737" y1="59.0298" x2="61.4729" y2="59.0298" stroke="#DCDDDF" stroke-width="3" stroke-linecap="round"/>
<path d="M54.8974 34.1286C54.7801 34.0448 54.6417 34 54.5 34C54.3583 34 54.2199 34.0448 54.1026 34.1286C53.8897 34.2786 49 37.8783 49 43.1879C49 44.7294 49.5795 46.2077 50.6109 47.2977C51.6424 48.3877 53.0413 49 54.5 49C55.9587 49 57.3576 48.3877 58.3891 47.2977C59.4205 46.2077 60 44.7294 60 43.1879C60 37.7883 55.1032 34.2711 54.8974 34.1286Z" fill="#F5F5F5"/>
</g>
<defs>
<filter id="filter0_b_5278_45740" x="39" y="25" width="47.092" height="45.988" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5278_45740"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_5278_45740" result="shape"/>
</filter>
<filter id="filter1_d_5278_45740" x="0" y="0" width="145.092" height="143.988" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="10" dy="24"/>
<feGaussianBlur stdDeviation="27"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5278_45740"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5278_45740" result="shape"/>
</filter>
</defs>
</svg>
