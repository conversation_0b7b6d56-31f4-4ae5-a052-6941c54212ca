<svg width="189" height="242" viewBox="0 0 189 242" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M184.153 4.87793H4.8291V241.853H184.153V4.87793Z" fill="url(#paint0_linear_3290_31073)"/>
<g filter="url(#filter0_d_3290_31073)">
<path d="M4.8291 4.87793H184.153" stroke="white" stroke-miterlimit="10" stroke-linecap="round"/>
</g>
<g filter="url(#filter1_d_3290_31073)">
<path d="M4.8291 4.87793H184.153" stroke="white" stroke-miterlimit="10" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_d_3290_31073" x="0.329102" y="0.37793" width="188.324" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0705882 0 0 0 0 0.913725 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3290_31073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3290_31073" result="shape"/>
</filter>
<filter id="filter1_d_3290_31073" x="0.329102" y="0.37793" width="188.324" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0705882 0 0 0 0 0.913725 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3290_31073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3290_31073" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3290_31073" x1="94.4961" y1="241.853" x2="94.4961" y2="4.87793" gradientUnits="userSpaceOnUse">
<stop stop-color="#0088F7" stop-opacity="0"/>
<stop offset="0.48" stop-color="#0088F7" stop-opacity="0"/>
<stop offset="0.65" stop-color="#0088F7" stop-opacity="0.04"/>
<stop offset="0.77" stop-color="#0088F7" stop-opacity="0.08"/>
<stop offset="0.87" stop-color="#0088F7" stop-opacity="0.15"/>
<stop offset="0.96" stop-color="#0088F7" stop-opacity="0.24"/>
<stop offset="1" stop-color="#0088F7" stop-opacity="0.3"/>
</linearGradient>
</defs>
</svg>
