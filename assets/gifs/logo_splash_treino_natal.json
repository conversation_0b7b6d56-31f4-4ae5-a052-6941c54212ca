{"v": "5.9.0", "fr": 60, "ip": 0, "op": 140, "w": 1400, "h": 1080, "nm": "logo treino", "ddd": 0, "assets": [{"id": "image_0", "w": 117, "h": 117, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHUAAAB1CAMAAABH2l6OAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAARVBMVEVHcEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADm5ub///8AAADv7+8UFBT4+Pg0NDSXl5d8fHxYWFi2trbS0tLf399Yc1A1AAAACnRSTlMAgBBwl1sgwOs5GZS6VwAAA9JJREFUaN7tmu2WoyAMhqtVQSgKiN7/pW4F1NrKhxhmztnp+3ftPJs3ISLkdvvqq6/+WzVtUSGEH1oYoapom7zE9r7g9sLo3uZCVofElVzV8L4WXuQCBvW6qV7+Nh+kUiPVGpWSA3/5RwTmdIM2olT0QEpyYO4WJxeMOsXEsHKv+3y3+ezkRAOaZGe5xcW6teZ2gkZJdAA220Bjma/c5HBLm1HJ6Akx6zMqr5QuH+lJTaagcYrLrXFX0gSJVKyBdoomaTQun+2RtYZyRhPFeAK21b8ZkqFPDaexxt6BXpLBxue2gYDamoouqRKBQCmVGhu5bisgqDUZxbXBa9W7E49tjrqSOhioXUARqdVJVRRIul2EU3tPboOeQi5iFg2ngNIeNxH1O0JSpy5Yxy2wv+uqbUOlBFW/q0LBNvN/SwBDTUG1/qx20FCzaNEPh/rcKnvLuMgSKiVkzmzlomL4AtaxkrmMsW/ZTDmoo2dbUUG3pZVKBrfFOEstzXklwmmxNpjloep6al1vmxwGa6rTYpSngmk/U6WrUWDIt/k7Vc3pczUmmqeE3Yltc6XVUPnxii2gtqNHxWQSWxz3iGzF5CwnlLOYTDn9IJVsVHy8cMZ8VHK8dDItHPZ3qD35DSr7FSr5DSoLUbOsHBKi5ugSL6E6ugSC3zX1ZE9Fju7PcoWq92vI8aYjfSbo5HjT6bc6IXn8NW/1u2MHQwjLEqp7a6p3awTM4z10dOzWzM6UQGH3/k7CtTO9m8TCYPdQzy5cf3GMMNh3qE5r7f66mh9h4FDhSqv9kjRP9bBQbbDjZKJeLb6G/YSOnecwHtsqvoZlH1Dz+Vr6Dn6WJxkc1H8aYk5+1mdh3F0+6Fr/gd72dA8SqOnBKHB4uQVLWH89UBtqHTq9HF9+cSK7/XGgJlQcPqnd/YZdZOqvucAd1kewcT67mWatBm5XdBnzj596wR6kbUvBW47i02MfuPcjbQeuIm9Wjv8EY33fL7iesQBx8Tfi0kwX1HtqUzXG3iIZjzkMdYjzd/V4AIPG3Q7eSgyENRewsfMp5qpZgEBPXDbba3UI6JmL9fo6dkgYIqjtXEjykhnSBiYMVl1Zp4/zk2x2OEQmpbRLhK6DMOddtu7itBmn0o5UnQx3mTRKniMrlkmueKayc3PVLV3W5QcX55j42qBeacN9llUwv6NY5gOr8nZR2zTi4A1YDB3oRGK7crtBquMoV+QDg42bbtwnmUupVrZSQvLuZeITdMS1qaKmW+HneWvkB6O6vGVRWyEHMcPw8J5cF2iLGiNUZJuT/uqrr/6u/gE0e1gCO6Kt0gAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_1", "w": 336, "h": 262, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_2", "w": 360, "h": 372, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_3", "w": 133, "h": 275, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIUAAAETBAMAAADqvNjlAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAG1BMVEVHcEz///////////////////////////////8W/R0OAAAACHRSTlMAfsQ7n+gZWOtaLNYAAAMeSURBVHja7Zo/i9tAEMX152K7VJIiKsM1p9IhjUtzEHBpSAqXukCISx+kcHm2rPN+7ASOwBlp597MrIVkz36AH0jzZubtzEZR6/lw6z9ZhJ3C+Q/KIBBuiiFGFKPEGGMCUYOfEhOMPci4IRgHkJEQjB3ImBCMCmTMCMYTyNgSjCXIyAnG5w6lviIYmwBSB9MlpRgmdZHU1wTjeWBSXxCM+bA6Qwip96UzhJD6O4JxDCD1LjtDCKnnlyP1ECYoNRNkJshMUJcmKL4SE4Q2mhnGOEiljlbFBcjYCNMFTT8QQZnMFGXsZCYI1X2MMojAJDBjLjJBaG1dw4xKK3UyMDnMqAMw/GovnD4wOMIbmBGDcdSmC1GGYgajLvkmCA1MwmHM+Z0BLUMzpw/MlsPYq6XuLUMFi5GxTRDYH0YsRHtgUh5jp5W6rwwxGa0eL2EyMq3UPWVozWRU2nTxlKHc6QOzYDLaylDBZcxVncEXmJTNOMpNEFGGYjajWYYSPmMjN0H+wKz5jGet1NvKUO70gREwGoEpBIxMK/Wm2kcSRqVNl2ZgYgmjVpggT3+g0uXw3nemnU6ChrYj6MkkqC87gnNPgvqyI+hyHdaXHcG1rMMG9lCiL+uwvuwIzr0Ou6QdQTmsznAt6zAzQWaCLqgzmAkyE2QmyEyQmSAzQWaCzASZCTITZCbITJCZIHst2tN06ZIx6QljdmaGC8GY6tMWbVG50zvL3OnrKcmoSz0DLGQ0Y1/qGe57AIa7C8BwPz7qGf+ic3u6S/8kYLx5xxMwQrwcqVh5e75XMEuO00YTYBLgVZDgdVIWgDHl9Eq0UvNffNU8/4F2nUItdcFrviNv6oAaX/bTsyfeNAit9WO11CPuS95245trpS7I/jJS/9Sa7bZRg8X7Ie3Tv1/adGErpOJPDdA7HutjloI7DGqNtkqpcwvzRjKLQb3zjaIz8P8IcZcYr1RSfzk/VVLnZR45zkixZkVP/7Bf8sZ15GGlkPr/83sl6wyn/2QhTpdXtfWPWOqvv+ebVOonlPsA44xo9OW+UF69X37v18e2N7yNy8xfsuel5dF82GYAAAAASUVORK5CYII=", "e": 1}, {"id": "image_4", "w": 163, "h": 224, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKMAAADgBAMAAACdjstxAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAG1BMVEVHcEz///////////////////////////////8W/R0OAAAACHRSTlMAw4IfRKdm4KA2yOEAAAM6SURBVHja7Zq9b9pQFMUNNh8jLBVjG6mKR1N1YLTaqMqIWqliJFnKyNCBEZwA/rMbVa1Ckud73r0+RpC8u+bp5N3n+/M5eiaKePX59uNwOHxP07v6MCr/Fkvyyz+9h8pdfx8Pq8u5h2+PgmWZuVZcl9XlkIwvn6xwdpEKki/b+rR6smDjlJxpJG+eLSickitBUm76oXZOSUHxWVvxy4buXYqJILlFiuWdS7IrSBZIsVy6JNuC5GFbsXMyBi7JliB52NYv38GNor4gedDW2L1i7pKcCJIDeDx2HpOR3+BiHudokZ3Hsd/g/q8R5jHxG1wfHvfwbJw8xpjHsd/geiFewH+6tvG48Brcg+ogSem14uaxh9qShszNYx+0JXVRYbkSj1O0STfiC/mk5E3uLZYrb3Kj5jEDmzTxKG/SYrlgk3rLLcRnZ+Jx1wWKBh4nSHKqtlykqLfcDZTMtZaLS225uNQ8wtJbLqytOgLDUvOIS80jLnUExqWOwLjUEbjk84hrzucxDzx6Wi6s4kg8xnwe6yG+PhKPnVqSU20ErqrHG585AfHNxVWGbqVUPH7/6nPRtfAX/JlFXuWN+Db3vY7z5fFd5qvoy+MPxaUhXzGhK/rx+Ft1WdoxvxQry8Ny95lOsk8+SC/L3WkvnjHic61kyn3aXpabqyVH7JPEPKpPEvK41W+ya3HAeohneskW/eEgHg19Ix4NfQMeC4MisNylRXJGnnPE48b0UU0fcVHF/KNM+EfZ1l8j1rJc01TKEfjOJNlX5/B6POYRnUfb5+OU6xGIx/uIzuOaL2l74An/gXfZJiHzaHtZipZrI1zk0ThDE/4MXZNTAeDR+JORGX0swYceNo8241H8EILCo3HS2/xJb9HtUeRxzedxwOfRCM+CHbGOzmN0KjzGfOdJzoLHDl+yx+exf1wep3wejYin5+6PDSB+Ojx2yZc5r8JylzbJ87Dc9LiWm/ERPxke4xCBaTz2QgSm8Xj2lvtmI7DRcjtnwWMDEXgSIjCNxxXdcuMQgUMEDhE4ROAQgUMEDhE4ROAQgUMEDhG4QR4bsNwQgUMErh2BG0D8NUfgJETg8OH1bUTg24vqsin+AQeDhA1SG4tsAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_5", "w": 228, "h": 228, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_6", "w": 95, "h": 221, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF8AAADdBAMAAAAvGrMVAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAHlBMVEVHcEz////////////////////////////////////JATChAAAACXRSTlMAwECA6VgoEKDSTpk1AAABKElEQVRo3u3ZqxKCUBSF4YOgQNNIY2w2q81Ko9o02qw0Ks0biG/rA7jZM1uPnIuLfP62Zn8zqtT7d1gMf2vivdo9hz8y2DBBTgUZE+ypoGAC6n3MvL9QwYQJblQQ6gyuVBAwwYMKUibYUsGRCeZUsNK5jFLLMjLhMpR0GTNpMDW6jI4KIiY4U0FldBm/PzIn4TLuWpah9ci0WpYRSJeRSpchPjLgB/zYyE/+h/y0HvBTO8JP4T4/ic4j4wM/pRF+lM5l9I7wE3nATwN+bOAnBT/gB/yAH/AzHj/iI9ODnz/mJwY/Xx6ZGvx8yI/4yCT28VOBH/BjiB/xkWnAD/gBP1bw03nAz9p9fvDj23j8GP2D2UJ+bh7w48qPb5n7/ITgxwp+lsxHvX8B9yBAiV1qJMsAAAAASUVORK5CYII=", "e": 1}, {"id": "image_7", "w": 238, "h": 224, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAO4AAADgBAMAAAD2ykdhAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAHlBMVEVHcEz////////////////////////////////////JATChAAAACXRSTlMAfcShQOpZJxDrDSgYAAAE5UlEQVR42u2az28SQRTHoUCB2xJjlNuKMbo3atLG3qgHE27EJm25kbSN9kaqHrihiSa9lYUF5r81WpuUsjvzvm/eLj+cd2V3vszsm8+878zkcmJRPKo17uJF7aKfyya+PgnUQnzca6WvevRKxcTLT1gr143k8OJUA5UQxz1E90olx7Lur33N4+pgSNeta9pZ6sC50scx/TMHmmYe5Wl5X5ki+kzV7WpaeTTGgTJH9Jyoq2kjXHjwS1cpOeGiponxgqwiRuRRdHc1LYwePPdekSOiJFdB08DkwSxXQIwJ02lH8/4cH+S7ODHrljSvd+4f+t7FdNU7o+6N5m3/PgdQWRUaV6i25u3mP1wECo4TAUw+VYwwTaZDzbstPJXj5z6IZ+bHXfhIDExGf36vHPJkDR0um968Vtxo2mCSO8qmDuc1L04NaWfTYR2eZ7ncDwvZv/+bg8nbXCWw0VWadamqxeQ3K1l1ysRkuWunGzKrWMvu6jJLl68fura6ExYmr2xlVTTkVLHW3U1elSoq3ZgxqliJCBmYFIkWjkmR6OBVrEhMcUyKRBSvO0hbV/VgTMqED1exac7gw9R1xzAm00wspVaSWMUMdJtgFSsV81VgMoFYOxnojsEqNj4ajb3a20bXMqFvsCmxd5G4EUzf9DNVsUtL+OKmZ+Wc2mnPCpMHS3+bsFuZNJHImIyexVHnkrv0Uz/SOKFcuWROYKpsn7Mrk1y8l603oyhbTCMmJrXbnAUOOPLceQBVLCEPz68N+8jmaczCpHFPd4DrEqrYyHgmZE6SFgOTp3YHQfG6hJwg7JwX4MQ0Y/IN4aSg0kV1A4numj+XB1exlO6aB7qJmn1ad40D7aNV7JnA0WaMrgmTEfX4/Cema/ouU+rRZgHT3bFaEOjrqY9hMqSfXQeQ7sAakbTE8jFMenTdAaRblxpmQ6b4EJ5ngG4B0g2khtmwBvtIFRsBVzEME8lHMDnNIdEFRm6Xs7HImcAegmforo0+RT0g+SPsclEd0C1h5oKv2wLM/hmm2wZ027yTH1y3D4xNX1AXyMEwJ6cbAXNuKqgbApicC+qOAaY2BXVHACZ7grpTAJNDQd0Jfa1G01mrO6fjeYTqXtFXtqpQjWNkkE8fmo6kbpM+NOg00rKvR/+LHqob0Jcj4FHJS6fIo1aXTiO62Yenb5mO5yL9UblLpwZMwtjI0/FcoBPVzh/NOPdTBfzgLR2TMK6qdEwOAANntRwBmPQklyMPvp8qsyy06CTvS+J5CN5PFcJzRCcqjEmAuaKYBBrbXREm81lhckIn2yxFTJboj9phskM3+zAmB3RMAkTNDJOeJCZ7dEzC1STAXICodnhmF552VWxILzzDTcGkUBU7lcQzYPZFMTljE9UcJTomq5KYBJjb3oYqti6DZ1FMAma/nyImt93sp1nFboDZz6yKdWZ/JZhsO7OfDib7bKJug9kvS2JyHc3+aEWYdGY/G7Pvs4m6FWY/syrWmf1VmP2iM/t8PDuzv91HVkJmvyeJSWf2ndl3Zt+ZfWf2pcx+SxKTzuw7s8+sYp3Zd2bfmX1n9p3Zd2afx1xn9p3ZF8HkcC3MvmgVuyqzX5TBpDP72VSxG2P2B87s/69mX6WISWf2166Kzczsz9fD7FezwmSaZr8tg8k0zb4oJjfA7Nc0AeOZ3thv4RLRxsdRq1AAAAAASUVORK5CYII=", "e": 1}, {"id": "image_8", "w": 235, "h": 228, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "nm": "natal", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "bolinha", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [425.762, 684.579, 0], "to": [-11, 0, 0], "ti": [19.667, 2.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [359.762, 684.579, 0], "to": [-19.667, -2.333, 0], "ti": [-2.667, -4.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [307.762, 670.579, 0], "to": [2.667, 4.667, 0], "ti": [-11.333, -7, 0]}, {"t": 28, "s": [375.762, 712.579, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [58.047, 58.047, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 16, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20, "s": [110, 110, 100]}, {"t": 24, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 40, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "aro", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [601.705, 518.495, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [167.785, 130.675, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [68, -58], [0, 0]], "o": [[0, 0], [-68, 58], [0, 0]], "v": [[302.08, 66.18], [114.08, 154.18], [42.08, 234.18]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Máscara 1"}], "ef": [{"ty": 22, "nm": "Traçado", "np": 13, "mn": "ADBE Stroke", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "<PERSON><PERSON><PERSON>", "mn": "ADBE Stroke-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "<PERSON><PERSON> as m<PERSON><PERSON><PERSON>", "mn": "ADBE Stroke-0010", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Traçar sequencialmente", "mn": "ADBE Stroke-0011", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 2, "nm": "Cor", "mn": "ADBE Stroke-0002", "ix": 4, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}}, {"ty": 0, "nm": "Tamanho do pincel", "mn": "ADBE Stroke-0003", "ix": 5, "v": {"a": 0, "k": 93.5, "ix": 5}}, {"ty": 0, "nm": "Dureza do pincel", "mn": "ADBE Stroke-0004", "ix": 6, "v": {"a": 0, "k": 0.75, "ix": 6}}, {"ty": 0, "nm": "Opacidade", "mn": "ADBE Stroke-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 0, "nm": "Início", "mn": "ADBE Stroke-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Fim", "mn": "ADBE Stroke-0009", "ix": 9, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}], "ix": 9}}, {"ty": 7, "nm": "Espaçamento", "mn": "ADBE Stroke-0006", "ix": 10, "v": {"a": 0, "k": 15, "ix": 10}}, {"ty": 7, "nm": "Estilo de pin<PERSON>", "mn": "ADBE Stroke-0007", "ix": 11, "v": {"a": 0, "k": 3, "ix": 11}}]}], "ip": 0, "op": 40, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "gorro", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [21]}, {"t": 20, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [648.13, 562.795, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [313.54, 241.785, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 9, "s": [61.257, 61.257, 100]}, {"t": 20, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [70, -240], [0, 0]], "o": [[0, 0], [-70, 240], [0, 0]], "v": [[277.41, 168.99], [87.41, 208.99], [1.41, 500.99]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Máscara 1"}], "ef": [{"ty": 34, "nm": "Marionete", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Mecanismo de marionetes", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Refinamento de giro da malha", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "Em transparente", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": "", "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "Formas com traçado automático", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": "", "nm": "<PERSON><PERSON>", "np": 2, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": [{"ty": "", "nm": "Malha 1", "np": 8, "mn": "ADBE FreePin3 Mesh Atom", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "<PERSON><PERSON>", "mn": "ADBE FreePin3 Mesh", "ix": 1, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE FreePin3 Mesh Tri Count", "ix": 2, "v": {"a": 0, "k": 50, "ix": 2}}, {"ty": 0, "nm": "Densidade", "mn": "ADBE FreePin3 Mesh Tri Density", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3}}, {"ty": 0, "nm": "Expansão", "mn": "ADBE FreePin3 Mesh Expansion", "ix": 4, "v": {"a": 0, "k": 3, "ix": 4}}, {"ty": "", "nm": "Deformar", "np": 5, "mn": "ADBE FreePin3 PosPins", "ix": 5, "en": 1, "ef": [{"ty": "", "nm": "Pino da marionete 4", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "Deslocamento do vértice", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Índice do vértice", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 27, "ix": 2}}, {"ty": 7, "nm": "Tipo de fixação", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Posição", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [245.41, 124.99], "ix": 4}}, {"ty": 0, "nm": "Escala", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotação", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pino da marionete 3", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Deslocamento do vértice", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Índice do vértice", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 28, "ix": 2}}, {"ty": 7, "nm": "Tipo de fixação", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Posição", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [63.41, 358.99], "to": [3.667, -1.333], "ti": [12, 4.667]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [85.41, 350.99], "to": [-12, -4.667], "ti": [6.667, -2.333]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [-8.59, 330.99], "to": [-6.667, 2.333], "ti": [-9, -5.667]}, {"t": 28, "s": [45.41, 364.99]}], "ix": 4}}, {"ty": 0, "nm": "Escala", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotação", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pino da marionete 2", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 3, "en": 1, "ef": [{"ty": 3, "nm": "Deslocamento do vértice", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Índice do vértice", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 29, "ix": 2}}, {"ty": 7, "nm": "Tipo de fixação", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Posição", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [87.41, 224.99], "to": [-3.667, 0], "ti": [2, -1.333]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [65.41, 224.99], "to": [-2, 1.333], "ti": [0.333, -0.667]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [75.41, 232.99], "to": [-0.333, 0.667], "ti": [2, 0.667]}, {"t": 28, "s": [63.41, 228.99]}], "ix": 4}}, {"ty": 0, "nm": "Escala", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotação", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pino da marionete 1", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 4, "en": 1, "ef": [{"ty": 3, "nm": "Deslocamento do vértice", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Índice do vértice", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 30, "ix": 2}}, {"ty": 7, "nm": "Tipo de fixação", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Posição", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [163.41, 92.99], "ix": 4}}, {"ty": 0, "nm": "Escala", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotação", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}]}, {"ty": "", "nm": "Sobreposição", "np": 1, "mn": "ADBE FreePin3 HghtPins", "ix": 6, "en": 1, "ef": []}, {"ty": "", "nm": "<PERSON><PERSON><PERSON>", "np": 1, "mn": "ADBE FreePin3 StarchPins", "ix": 7, "en": 1, "ef": []}]}]}]}]}, {"ty": 22, "nm": "Traçado", "np": 13, "mn": "ADBE Stroke", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "<PERSON><PERSON><PERSON>", "mn": "ADBE Stroke-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "<PERSON><PERSON> as m<PERSON><PERSON><PERSON>", "mn": "ADBE Stroke-0010", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Traçar sequencialmente", "mn": "ADBE Stroke-0011", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 2, "nm": "Cor", "mn": "ADBE Stroke-0002", "ix": 4, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}}, {"ty": 0, "nm": "Tamanho do pincel", "mn": "ADBE Stroke-0003", "ix": 5, "v": {"a": 0, "k": 155.2, "ix": 5}}, {"ty": 0, "nm": "Dureza do pincel", "mn": "ADBE Stroke-0004", "ix": 6, "v": {"a": 0, "k": 0.75, "ix": 6}}, {"ty": 0, "nm": "Opacidade", "mn": "ADBE Stroke-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 0, "nm": "Início", "mn": "ADBE Stroke-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Fim", "mn": "ADBE Stroke-0009", "ix": 9, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [0]}, {"t": 22, "s": [100]}], "ix": 9}}, {"ty": 7, "nm": "Espaçamento", "mn": "ADBE Stroke-0006", "ix": 10, "v": {"a": 0, "k": 15, "ix": 10}}, {"ty": 7, "nm": "Estilo de pin<PERSON>", "mn": "ADBE Stroke-0007", "ix": 11, "v": {"a": 0, "k": 3, "ix": 11}}]}], "ip": 9, "op": 49, "st": 9, "bm": 0}]}, {"id": "comp_1", "nm": "Treino original", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "t", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [699.802, 537.845, 0], "to": [-75.707, 0, 0], "ti": [75.707, 0, 0]}, {"t": 30, "s": [245.557, 537.845, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [66.302, 137.367, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 140, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pré-composição 1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [700, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-52, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1253.286, 379.105], [259.286, 379.105], [245.286, 403.105], [205.286, 623.105], [249.286, 677.105], [277.286, 677.105], [277.286, 709.105], [1253.286, 709.105]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Máscara 1"}], "w": 1400, "h": 1080, "ip": -25, "op": 140, "st": -25, "bm": 0}]}, {"id": "comp_2", "nm": "Pré-composição 1", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "r", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 116, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-666.252, 563.436, 0], "to": [176.036, 0, 0], "ti": [-176.036, 0, 0]}, {"t": 120, "s": [389.962, 563.436, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [81.401, 111.759, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "e", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"t": 114, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-494.041, 565.029, 0], "to": [176.036, 0, 0], "ti": [-176.036, 0, 0]}, {"t": 118, "s": [562.174, 565.029, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [113.849, 113.743, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "i", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0]}, {"t": 112, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-335.227, 565.02, 0], "to": [176.036, 0, 0], "ti": [-176.036, 0, 0]}, {"t": 116, "s": [720.988, 565.02, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [47.4, 110.174, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "n", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [0]}, {"t": 110, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-186.389, 563.453, 0], "to": [176.036, 0, 0], "ti": [-176.036, 0, 0]}, {"t": 114, "s": [869.826, 563.453, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [118.964, 111.759, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "o", "refId": "image_8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 108, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [44.287, 565.029, 0], "to": [176.036, 0, 0], "ti": [-176.036, 0, 0]}, {"t": 112, "s": [1100.501, 565.029, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [117.287, 113.743, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "natal", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -21, "ix": 10}, "p": {"a": 0, "k": [204, 448, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [43, 43, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1080, "h": 1080, "ip": 100, "op": 140, "st": 100, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Treino original", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [700, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1400, "h": 1080, "ip": 0, "op": 140, "st": 0, "bm": 0}], "markers": []}