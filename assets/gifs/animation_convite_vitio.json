{"v": "5.9.4", "fr": 30, "ip": 0, "op": 61, "w": 500, "h": 500, "nm": "Badge", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Star 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [0]}, {"t": 50, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [155.519, 164.081, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [70, 70, 100]}, {"t": 50, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.306, -0.082], [0, 0], [0.878, -0.237], [0, 0], [0.082, -0.306], [0, 0], [0.237, 0.878], [0, 0], [0.306, 0.082], [0, 0], [-0.878, 0.237], [0, 0], [-0.082, 0.306], [0, 0], [-0.237, -0.878], [0, 0]], "o": [[0, 0], [0.878, 0.237], [0, 0], [-0.306, 0.082], [0, 0], [-0.237, 0.878], [0, 0], [-0.082, -0.306], [0, 0], [-0.878, -0.237], [0, 0], [0.306, -0.082], [0, 0], [0.237, -0.878], [0, 0], [0.082, 0.306]], "v": [[4.938, -4.309], [17.75, -0.86], [17.75, 0.86], [4.938, 4.309], [4.309, 4.938], [0.86, 17.75], [-0.86, 17.75], [-4.309, 4.938], [-4.938, 4.309], [-17.75, 0.86], [-17.75, -0.86], [-4.938, -4.309], [-4.309, -4.938], [-0.86, -17.75], [0.86, -17.75], [4.309, -4.938]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.525490224361, 0.996078431606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 320, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Star 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"t": 43, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [127.953, 244.276, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100]}, {"t": 43, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.196, -0.053], [0, 0], [0.563, -0.152], [0, 0], [0.053, -0.196], [0, 0], [0.152, 0.563], [0, 0], [0.196, 0.053], [0, 0], [-0.563, 0.152], [0, 0], [-0.053, 0.196], [0, 0], [-0.152, -0.563], [0, 0]], "o": [[0, 0], [0.563, 0.152], [0, 0], [-0.196, 0.053], [0, 0], [-0.152, 0.563], [0, 0], [-0.053, -0.196], [0, 0], [-0.563, -0.152], [0, 0], [0.196, -0.053], [0, 0], [0.152, -0.563], [0, 0], [0.053, 0.196]], "v": [[3.166, -2.763], [11.382, -0.551], [11.382, 0.551], [3.166, 2.763], [2.763, 3.166], [0.551, 11.382], [-0.551, 11.382], [-2.763, 3.166], [-3.166, 2.763], [-11.382, 0.551], [-11.382, -0.551], [-3.166, -2.763], [-2.763, -3.166], [-0.551, -11.382], [0.551, -11.382], [2.763, -3.166]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.525490224361, 0.996078431606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13, "op": 313, "st": 13, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Star", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 38, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [366.519, 140.081, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"t": 38, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.306, -0.082], [0, 0], [0.878, -0.237], [0, 0], [0.082, -0.306], [0, 0], [0.237, 0.878], [0, 0], [0.306, 0.082], [0, 0], [-0.878, 0.237], [0, 0], [-0.082, 0.306], [0, 0], [-0.237, -0.878], [0, 0]], "o": [[0, 0], [0.878, 0.237], [0, 0], [-0.306, 0.082], [0, 0], [-0.237, 0.878], [0, 0], [-0.082, -0.306], [0, 0], [-0.878, -0.237], [0, 0], [0.306, -0.082], [0, 0], [0.237, -0.878], [0, 0], [0.082, 0.306]], "v": [[4.938, -4.309], [17.75, -0.86], [17.75, 0.86], [4.938, 4.309], [4.309, 4.938], [0.86, 17.75], [-0.86, 17.75], [-4.309, 4.938], [-4.938, 4.309], [-17.75, 0.86], [-17.75, -0.86], [-4.938, -4.309], [-4.309, -4.938], [-0.86, -17.75], [0.86, -17.75], [4.309, -4.938]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.525490224361, 0.996078431606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 308, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Badge Star", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [251.181, 242.144, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [74.611, 74.611, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.194, 115.194, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.915, 123.915, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.459, 127.459, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.19, 127.19, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.392, 124.392, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [120.167, 120.167, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.394, 115.394, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.711, 110.711, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.541, 106.541, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [103.116, 103.116, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.52, 100.52, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.73, 98.73, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.652, 97.652, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.156, 97.156, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.097, 97.097, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.337, 97.337, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.754, 97.754, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.25, 98.25, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.751, 98.751, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.207, 99.207, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.589, 99.589, 100], "t": 34, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.885, 99.885, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.094, 100.094, 100], "t": 36, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.225, 100.225, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.145, 100.145, 100], "t": 43, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.053, 100.053, 100], "t": 45, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.491, 0.386], [0, 0], [1.098, 2.272], [0, 0], [1.136, -2.251], [0, 0], [2.497, -0.34], [0, 0], [-1.789, -1.776], [0, 0], [0.452, -2.482], [0, 0], [-2.242, 1.155], [0, 0], [-2.219, -1.192], [0, 0], [0.405, 2.489], [0, 0], [-1.822, 1.743], [0, 0]], "o": [[0, 0], [-2.492, -0.387], [0, 0], [-1.094, -2.272], [0, 0], [-1.135, 2.25], [0, 0], [-2.499, 0.34], [0, 0], [1.792, 1.776], [0, 0], [-0.448, 2.482], [0, 0], [2.242, -1.153], [0, 0], [2.221, 1.192], [0, 0], [-0.402, -2.489], [0, 0], [1.821, -1.744]], "v": [[22.693, -7.008], [12.981, -8.509], [6.455, -13.341], [2.183, -22.196], [-1.874, -22.232], [-6.306, -13.456], [-12.913, -8.747], [-22.659, -7.419], [-23.949, -3.572], [-16.968, 3.356], [-14.53, 11.097], [-16.278, 20.773], [-13.016, 23.188], [-4.273, 18.692], [3.844, 18.764], [12.506, 23.42], [15.81, 21.064], [14.234, 11.352], [16.813, 3.658], [23.913, -3.138]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Badge Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 308, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Badge Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 243.614, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 6, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 7, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [74.611, 74.611, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.194, 115.194, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.915, 123.915, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.459, 127.459, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.19, 127.19, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.392, 124.392, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [120.167, 120.167, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.394, 115.394, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.711, 110.711, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.541, 106.541, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [103.116, 103.116, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.52, 100.52, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.73, 98.73, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.652, 97.652, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.156, 97.156, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.097, 97.097, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.337, 97.337, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.754, 97.754, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.25, 98.25, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.751, 98.751, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.207, 99.207, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.589, 99.589, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.885, 99.885, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.094, 100.094, 100], "t": 34, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.225, 100.225, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.145, 100.145, 100], "t": 41, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.053, 100.053, 100], "t": 43, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.502, -28.215], [28.215, 0.502], [-0.502, 28.215], [-28.214, -0.5]], "o": [[-0.501, 28.215], [-28.214, -0.501], [0.5, -28.214], [28.215, 0.501]], "v": [[51.086, 0.907], [-0.907, 51.087], [-51.086, -0.908], [0.907, -51.087]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 1, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Badge Circle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 6, "op": 306, "st": 6, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Badge Zig Zag Circle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [249.999, 243.614, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 4, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 5, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 6, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 7, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [74.611, 74.611, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.194, 115.194, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.915, 123.915, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.459, 127.459, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.19, 127.19, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.392, 124.392, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [120.167, 120.167, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.394, 115.394, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.711, 110.711, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.541, 106.541, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [103.116, 103.116, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.52, 100.52, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.73, 98.73, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.652, 97.652, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.156, 97.156, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.097, 97.097, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.337, 97.337, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.754, 97.754, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.25, 98.25, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.751, 98.751, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.207, 99.207, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.589, 99.589, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.885, 99.885, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.094, 100.094, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.225, 100.225, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.145, 100.145, 100], "t": 39, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.053, 100.053, 100], "t": 41, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.038, 0.287], [0.022, 0.124], [0.014, 0.066], [0.029, 0.117], [0.023, 0.081], [0.051, 0.155], [1.075, 4.307], [2.577, 4.647], [3.718, 3.852], [4.617, 2.776], [5.228, 1.495], [5.508, 0.096], [5.103, -1.266], [4.647, -2.577], [3.851, -3.718], [2.784, -4.609], [0.154, -1.01], [0, -0.007], [0.007, -0.045], [0.022, -0.192], [0, 0], [0.007, -0.221], [0, -0.214], [-0.007, -0.272], [-0.081, -0.787], [-0.008, -0.066], [-0.023, -0.199], [-0.073, -0.803], [0, 0], [-0.022, -0.5], [-0.008, -0.184], [0.338, -1.185], [0.043, -0.132], [0.059, -0.133], [0.066, -0.132], [0.073, -0.132], [0.073, -0.132], [0.148, -0.236], [1.466, -2.018], [0.103, -0.154], [0.405, -0.972], [0.059, -0.155], [0.036, -0.11], [0.008, -0.021], [0.022, -0.103], [0.015, -0.089], [0, -0.044], [0.021, -0.132], [0.014, -0.154], [0, -0.192], [-1.266, -5.103], [-0.023, -0.169], [-0.007, -0.03], [0, 0], [-0.014, -0.177], [-0.008, -0.177], [0, -0.177], [0.029, -0.486], [0, 0], [0.03, -0.309], [0, -0.008], [0.044, -0.317], [0.037, -0.287], [0.007, -0.066], [0.132, -1.053], [0.015, -0.185], [0.029, -0.449], [-0.066, -0.766], [-0.007, 0], [-0.53, -0.95], [-0.331, -0.375], [-0.928, -0.567], [-0.265, -0.147], [-0.405, -0.191], [-0.346, -0.162], [-0.707, -0.317], [-0.515, -0.244], [-0.486, -0.265], [-0.81, -0.846], [-4.617, -2.783], [-5.228, -1.493], [-1.421, -1.053], [-0.169, -0.132], [-0.213, -0.17], [-0.449, -0.361], [-0.309, -0.236], [-0.78, -0.486], [-1.723, -0.03], [-5.102, 1.267], [-2.275, -0.316], [-0.406, -0.06], [-0.272, -0.036], [-2.224, 0.795], [-0.317, 0.177], [-0.14, 0.096], [-0.25, 0.228], [-3.49, 3.373], [-3.49, 1.723], [-0.162, 0.082], [-0.743, 0.692], [-0.348, 0.575], [-0.081, 0.183], [-0.074, 0.191], [0.044, 1.539], [0.008, 0.184], [0.007, 0.184], [0.014, 0.184], [0.015, 0.148], [0, 0.045], [0.022, 0.191], [0.015, 0.191], [0.014, 0.116], [0.052, 0.457], [0.007, 0.075], [0.029, 0.767], [-0.014, 0.405], [-0.007, 0.14], [-0.029, 0.272], [-0.022, 0.133], [0, 0], [-0.029, 0.132], [-0.029, 0.111], [-0.043, 0.147], [-0.994, 1.363], [-0.228, 0.302], [-0.117, 0.156], [-0.354, 0.45], [-0.235, 0.309], [-0.486, 0.772], [-0.236, 0.486], [-0.118, 0.324], [-0.015, 0.862]], "o": [[-0.007, -0.125], [-0.008, -0.067], [-0.023, -0.118], [-0.016, -0.082], [-0.037, -0.155], [-1.444, -4.462], [-1.311, -5.28], [-2.621, -4.712], [-3.711, -3.858], [-4.551, -2.746], [-5.052, -1.451], [-5.5, -0.103], [-5.272, 1.319], [-4.712, 2.614], [-3.851, 3.719], [-0.508, 0.847], [0, 0.007], [-0.008, 0.044], [-0.022, 0.191], [0, 0], [-0.015, 0.213], [-0.007, 0.214], [0, 0.271], [0.029, 0.766], [0.007, 0.074], [0.022, 0.199], [0.088, 0.81], [0, 0], [0.044, 0.509], [0.007, 0.184], [0.037, 1.348], [-0.037, 0.14], [-0.045, 0.132], [-0.051, 0.133], [-0.059, 0.132], [-0.067, 0.133], [-0.125, 0.243], [-1.237, 2.002], [-0.117, 0.155], [-0.648, 0.921], [-0.067, 0.139], [-0.037, 0.103], [-0.007, 0.022], [-0.036, 0.103], [-0.037, 0.095], [-0.014, 0.044], [-0.037, 0.133], [-0.023, 0.155], [-0.022, 0.191], [-0.096, 5.501], [0.045, 0.162], [0.007, 0.029], [0, 0], [0.036, 0.169], [0.03, 0.17], [0.022, 0.177], [0.022, 0.471], [0, 0], [-0.015, 0.309], [0, 0], [-0.029, 0.309], [-0.03, 0.287], [-0.014, 0.066], [-0.148, 1.06], [-0.022, 0.176], [-0.059, 0.457], [-0.059, 0.825], [-0.007, 0.009], [0.103, 1.126], [0.242, 0.441], [0.662, 0.758], [0.251, 0.154], [0.375, 0.215], [0.339, 0.17], [0.699, 0.317], [0.53, 0.229], [0.516, 0.243], [1.133, 0.618], [3.719, 3.851], [4.551, 2.739], [1.488, 0.427], [0.177, 0.132], [0.213, 0.162], [0.449, 0.352], [0.302, 0.245], [0.758, 0.588], [1.562, 0.979], [5.501, 0.095], [1.966, -0.486], [0.405, 0.053], [0.272, 0.044], [2.74, 0.398], [0.338, -0.118], [0.146, -0.082], [0.279, -0.184], [3.651, -3.225], [2.533, -2.437], [0.176, -0.082], [0.971, -0.507], [0.494, -0.465], [0.103, -0.176], [0.088, -0.184], [0.485, -1.295], [-0.007, -0.184], [-0.007, -0.184], [-0.016, -0.183], [-0.007, -0.14], [-0.009, -0.045], [-0.015, -0.191], [-0.023, -0.185], [-0.008, -0.11], [-0.051, -0.458], [0, -0.081], [-0.081, -0.795], [-0.014, -0.404], [0, -0.148], [0.014, -0.28], [0.015, -0.14], [0, -0.007], [0.022, -0.132], [0.023, -0.111], [0.03, -0.154], [0.405, -1.421], [0.221, -0.302], [0.11, -0.148], [0.361, -0.448], [0.235, -0.302], [0.589, -0.758], [0.294, -0.472], [0.154, -0.317], [0.294, -0.818], [0.007, -0.288]], "v": [[64.499, 0.28], [64.448, -0.095], [64.419, -0.295], [64.338, -0.648], [64.279, -0.891], [64.146, -1.348], [56.694, -14.116], [56.481, -31.288], [42.027, -40.558], [33.271, -55.329], [16.122, -56.154], [1.145, -64.54], [-14.12, -56.699], [-31.284, -56.477], [-40.562, -42.031], [-55.333, -33.275], [-56.297, -30.47], [-56.297, -30.455], [-56.32, -30.322], [-56.386, -29.741], [-56.386, -29.719], [-56.423, -29.071], [-56.438, -28.43], [-56.423, -27.606], [-56.246, -25.272], [-56.224, -25.058], [-56.157, -24.461], [-55.9, -22.032], [-55.9, -22.025], [-55.789, -20.507], [-55.767, -19.955], [-56.157, -16.134], [-56.282, -15.728], [-56.438, -15.323], [-56.607, -14.918], [-56.798, -14.521], [-57.004, -14.123], [-57.417, -13.408], [-61.894, -7.437], [-62.225, -6.973], [-63.852, -4.138], [-64.036, -3.696], [-64.147, -3.373], [-64.169, -3.307], [-64.257, -2.989], [-64.331, -2.717], [-64.361, -2.585], [-64.448, -2.187], [-64.507, -1.716], [-64.545, -1.149], [-56.695, 14.115], [-56.592, 14.616], [-56.577, 14.698], [-56.577, 14.705], [-56.497, 15.22], [-56.445, 15.743], [-56.416, 16.273], [-56.43, 17.717], [-56.43, 17.731], [-56.497, 18.658], [-56.497, 18.666], [-56.6, 19.602], [-56.702, 20.464], [-56.732, 20.662], [-57.181, 23.844], [-57.241, 24.381], [-57.372, 25.743], [-57.372, 28.128], [-57.372, 28.143], [-56.474, 31.28], [-55.605, 32.502], [-53.183, 34.469], [-52.402, 34.917], [-51.224, 35.521], [-50.193, 36.007], [-48.073, 36.942], [-46.497, 37.65], [-44.994, 38.408], [-42.027, 40.565], [-33.272, 55.329], [-16.123, 56.153], [-11.778, 58.525], [-11.263, 58.914], [-10.615, 59.416], [-9.26, 60.49], [-8.347, 61.206], [-6.042, 62.839], [-1.146, 64.54], [14.118, 56.691], [20.606, 56.72], [21.829, 56.89], [22.646, 57.015], [30.297, 56.92], [31.284, 56.478], [31.71, 56.212], [32.506, 55.601], [40.56, 42.03], [50.951, 36.847], [51.452, 36.595], [54.058, 34.822], [55.333, 33.268], [55.62, 32.731], [55.862, 32.163], [56.429, 27.871], [56.407, 27.319], [56.379, 26.758], [56.333, 26.199], [56.297, 25.764], [56.282, 25.632], [56.223, 25.058], [56.164, 24.49], [56.128, 24.146], [55.972, 22.775], [55.951, 22.539], [55.774, 20.19], [55.767, 18.976], [55.781, 18.549], [55.847, 17.717], [55.899, 17.311], [55.899, 17.304], [55.972, 16.907], [56.046, 16.567], [56.156, 16.119], [58.388, 11.965], [59.066, 11.067], [59.418, 10.61], [60.494, 9.263], [61.208, 8.342], [62.843, 6.046], [63.638, 4.617], [64.05, 3.653], [64.544, 1.142]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Badge Zig Zag Circle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 304, "st": 4, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Badge Zig Zag Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [249.995, 242.764, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 2, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 3, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 4, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 5, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [74.611, 74.611, 100], "t": 6, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 7, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.194, 115.194, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.915, 123.915, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.459, 127.459, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.19, 127.19, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.392, 124.392, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [120.167, 120.167, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.394, 115.394, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.711, 110.711, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.541, 106.541, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [103.116, 103.116, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.52, 100.52, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.73, 98.73, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.652, 97.652, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.156, 97.156, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.097, 97.097, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.337, 97.337, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.754, 97.754, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.25, 98.25, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.751, 98.751, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.207, 99.207, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.589, 99.589, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.885, 99.885, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.094, 100.094, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.225, 100.225, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.145, 100.145, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.053, 100.053, 100], "t": 39, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.775, -6.193], [-0.287, -3.034], [0.596, -3.652], [0.707, -1.185], [5.523, -5.324], [1.576, -3.608], [3.741, -2.084], [7.547, -1.886], [3.579, -2.599], [3.925, 0.066], [3.41, 2.689], [3.675, 1.044], [6.509, 3.925], [1.782, 4.506], [2.401, 2.488], [3.756, 6.745], [0.198, 1.098], [-0.56, 4.705], [0.581, 2.371], [2.113, 6.333], [-0.022, 1.259], [-2.069, 7.224], [-0.089, 0.545], [0.39, 4.079], [-1.81, 3.012], [-5.516, 5.316], [-6.752, 3.74], [-7.563, 1.87], [-7.871, -0.14], [-7.239, -2.077], [-6.515, -3.924], [-5.323, -5.515], [-3.742, -6.737], [0.449, -3.704], [-0.78, -4.175], [-0.059, -0.243], [0.139, -7.886], [0.375, -1.068]], "o": [[-0.743, 2.599], [0.383, 4.131], [-0.222, 1.414], [-3.977, 6.613], [-2.451, 2.371], [-1.967, 4.515], [-6.649, 3.689], [-3.667, 0.905], [-3.549, 2.6], [-3.886, -0.073], [-3.526, -2.753], [-7.488, -2.144], [-3.623, -2.178], [-1.472, -3.734], [-5.323, -5.515], [-0.522, -0.95], [-0.795, -4.108], [0.317, -2.725], [-1.525, -6.126], [-0.404, -1.2], [0.14, -7.879], [0.147, -0.515], [0.581, -3.697], [-0.39, -4.035], [3.976, -6.605], [5.515, -5.324], [6.649, -3.689], [7.304, -1.811], [7.879, 0.14], [7.474, 2.142], [6.614, 3.984], [5.317, 5.523], [1.582, 2.857], [-0.538, 4.661], [0.045, 0.243], [1.811, 7.304], [-0.015, 1.126], [-2.18, 6.42]], "v": [[80.4, 23.085], [80.048, 31.685], [80.563, 43.717], [79.209, 47.626], [58.067, 60.167], [52.479, 69.761], [44.791, 80.851], [20.22, 81.168], [9.469, 87.529], [-1.636, 92.396], [-12.438, 87.235], [-23.077, 80.387], [-47.62, 79.208], [-54.843, 68.032], [-60.16, 58.068], [-80.851, 44.785], [-81.925, 41.707], [-80.998, 27.952], [-81.152, 20.206], [-91.786, 2.047], [-92.397, -1.642], [-80.387, -23.077], [-80.033, -24.682], [-80.564, -36.721], [-79.202, -47.634], [-58.06, -60.166], [-44.778, -80.85], [-20.205, -81.16], [1.642, -92.396], [23.092, -80.386], [47.633, -79.208], [60.174, -58.068], [80.851, -44.792], [81.925, -34.711], [81.011, -20.941], [81.167, -20.212], [92.396, 1.642], [91.785, 4.934]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.796078443527, 0.839215695858, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Badge Zig Zag Circle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 2, "op": 302, "st": 2, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Ribben 2", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [32.331, 79.722, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.473, 0.045], [0, 0], [1.216, -2.163], [0, 0], [1.092, 2.233], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.485, -0.041], [0, 0], [-1.216, 2.162], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.216, 2.163]], "v": [[41.984, 35.834], [21.42, 35.466], [14.704, 39.316], [5.317, 56.031], [1.128, 55.907], [-20.346, 12.102], [-22.864, 6.958], [-28.418, -4.364], [-30.088, -7.788], [-33.413, -14.574], [-44.785, -37.777], [-32.897, -43.901], [-6.253, -57.618], [12.682, -24.036], [14.03, -21.658], [20.142, -10.811], [21.9, -7.697], [21.904, -7.693], [44.275, 31.984]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.435294121504, 0.996078431606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "<PERSON><PERSON><PERSON>", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-34.925, 78.579, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.137, 2.204], [0, 0], [2.485, 0.037], [0, 0], [-1.294, 2.117], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.17, 2.184], [0, 0], [-1.137, -2.204], [0, 0], [-2.473, -0.041], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[46.029, -36.217], [33.842, -13.432], [30.745, -7.659], [29.074, -4.529], [22.478, 7.807], [19.849, 12.723], [-3.185, 55.783], [-7.382, 55.746], [-16.165, 38.718], [-22.74, 34.632], [-43.305, 34.264], [-45.455, 30.335], [-21.64, -8.594], [-19.924, -11.398], [-19.924, -11.402], [-11.641, -24.941], [-10.243, -27.235], [8.22, -57.41], [34.359, -42.759]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.435294121504, 0.996078431606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "<PERSON><PERSON><PERSON>", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Bg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 0, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 1, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 2, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 3, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [74.611, 74.611, 100], "t": 4, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 5, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.194, 115.194, 100], "t": 6, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.915, 123.915, 100], "t": 7, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.459, 127.459, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [127.19, 127.19, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.392, 124.392, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [120.167, 120.167, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [115.394, 115.394, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.711, 110.711, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.541, 106.541, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [103.116, 103.116, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.52, 100.52, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.73, 98.73, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.652, 97.652, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.156, 97.156, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.097, 97.097, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.337, 97.337, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.754, 97.754, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.25, 98.25, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.751, 98.751, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.207, 99.207, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.589, 99.589, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.885, 99.885, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.094, 100.094, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.225, 100.225, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.145, 100.145, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.053, 100.053, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [321.888, 321.888], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254905701, 0.952941179276, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Bg", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}], "markers": []}