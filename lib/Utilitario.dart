// ignore_for_file: deprecated_member_use

import 'dart:io' as Io;
import 'dart:io';
import 'dart:math';
import 'dart:ui';
import 'package:app_treino/appWidgets/BasicWdigetUtil.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/config/TextUtil.dart';
import 'package:app_treino/appWidgets/formWidgets/TextFieldPadrao.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/geral/UsuarioFireBaseManter.dart';
import 'package:app_treino/model/homefit/models.dart';
import 'package:app_treino/model/inapp/controladorJPinApp.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:image/image.dart' as decode;
import 'package:image_picker/image_picker.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';


Color getOppositeColor(Color color) {
  final hsl = HSLColor.fromColor(color);
  final newHue = (hsl.hue + 180) % 360;
  return HSLColor.fromAHSL(hsl.alpha, newHue, hsl.saturation, hsl.lightness).toColor();
}

int countarOcorrenciasDeText(String pesquisa, String original) {
  pesquisa = removeDiacritics(pesquisa.toLowerCase());
  original = removeDiacritics(original.toLowerCase());

  return original.split(pesquisa).length - 1;
}

analytic(EventosKey? eventKey, {Map<String, dynamic>? opicionais, bool recursoEmpresa = false}) {
  if (eventKey != null) {
    try {
      activityTracker.logClick(eventKey.name.toString());
      String eventName = eventKey.toString().replaceAll('EventosKey.', '');
      if (EventosKey.veio_de_banner_inapp == eventKey) {
        FirebaseAnalytics.instance.logEvent(name: eventName, parameters: {'nomeCampanha': GetIt.I.get<ControladorJPinApp>().campanhaInApp ?? ''});
      } else {
        if (EventosKey.abriu_app == eventKey) {
          if (kDebugMode) {
            print('object');
          }
        }
        FirebaseAnalytics.instance.logEvent(name: eventName);
        Future.delayed(const Duration(seconds: 2)).then((value) => GetIt.I.get<ControladorJPinApp>().tratarInApp(eventKey));
      }
    } catch (e) {}
    // FirebaseInAppMessaging.instance.setMessagesSuppressed(true);
  }
}

class UtilitarioApp {
  static Future<bool> isAndroidVersionBelowOrEqual10() async {
    if (Platform.isAndroid) {
      final androidVersion = await getAndroidVersion();
      return androidVersion <= 29;
    }
    return false; // Retorna falso caso o sistema não seja Android
  }

  static Future<int> getAndroidVersion() async {
    final version = await Process.run('getprop', ['ro.build.version.sdk']);
    return int.tryParse(version.stdout.toString().trim()) ?? 0;
  }

  static List removeNullsFromList(List list) => list
    ..removeWhere((value) => value == null)
    ..map((e) => removeNulls(e)).toList();
  static removeNulls(e) => (e is List) ? removeNullsFromList(e) : (e is Map ? removeNullsFromMap(e as Map<String, dynamic>) : e);
  static showAppReview() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int dataAtual = DateTime.now().millisecondsSinceEpoch;
    int dataShowReview = (prefs.getInt('data_show_review') ?? DateTime.now().millisecondsSinceEpoch);
    if (dataShowReview <= dataAtual) {
      /* var inApp = InAppReview.instance;
      inApp.isAvailable().then((value) async {
        if (value) {
          Future.delayed(Duration(seconds: 3)).then((value) {
            inApp.requestReview();
          });
        }
        await prefs.setInt('data_show_review',
            DateTime.now().add(Duration(days: 5)).millisecondsSinceEpoch);
      }); */
    }
  }

  static String formatarDataFormatoAmericano(String data) {
    if (Platform.localeName.contains('en_')) {
      try {
        DateTime dataBrasileira = DateFormat('dd/MM/yyyy').parse(data);
        String dataAmericana = DateFormat('MM/dd/yyyy').format(dataBrasileira);

        return dataAmericana;
      } catch (e) {
        return '';
      }
    } else {
      return data;
    }
  }

  static String formatarDataFormatoAmericanoSomenteDiaMes(String data) {
    try {
      List<String> partes = data.split('/');
      String novaData = '${partes[1]}/${partes[0]}';
      return novaData;
    } catch (e) {
      return '';
    }
  }

  static Future<void> validarPermissoes(
      {Function()? onPermissoesLiberadas, Function()? onPermissoesPermanentementeNegadas, Function()? onPermissoesNegadas, required List<Permission> permissionsRequested}) async {
    await permissionsRequested.request().then((Map<Permission, PermissionStatus> permissionsMap) {
      var listPermissions = permissionsMap.entries.toList();
      if (listPermissions.every((permissionEntry) => permissionEntry.value == PermissionStatus.granted)) {
        onPermissoesLiberadas?.call();
      } else if (listPermissions.any((permissionEntry) => permissionEntry.value == PermissionStatus.permanentlyDenied)) {
        onPermissoesPermanentementeNegadas?.call();
      } else {
        onPermissoesNegadas?.call();
      }
    });
  }

 static bool hasValidUrl(String value) {
    if (value.isEmpty) {
      return false;
    }
    RegExp regex = RegExp(
      r'^https:\/\/www\.youtube\.com\/(?:watch\?v=|embed\/|v\/|playlist\?list=|shorts\/)?[a-zA-Z0-9_-]{11}$',
      caseSensitive: false,
      multiLine: false,
    );
    return regex.hasMatch(value);
  }
  // static Future<bool?> validarPermissao({Function()? liberado, Function()? negado, bool perguntarPorLiberacao = true}) async {
  //   await Permission.camera.request();
  //   return Future.wait([Permission.camera, Permission.storage, Permission.microphone].map((e) => e.status)).then((value) {
  //     if (value.any((element) => element == PermissionStatus.restricted)) {
  //       [Permission.camera, Permission.storage, Permission.microphone].request().then((value) => validarPermissao(liberado: liberado, negado: negado, perguntarPorLiberacao: false));
  //     } else if (value.any((element) => element == PermissionStatus.permanentlyDenied || element == PermissionStatus.denied)) {
  //       if (perguntarPorLiberacao) {
  //         BasicWdigetUtil().showDialogMensagem(
  //             GetIt.I.get<NavigationService>().context,
  //             titulo: localizedString('no_permission_camera'),
  //             mensagem:
  //               localizedString('you_need_to_give_permission'),
  //             textoAcaoPrincipal: localizedString('settings'), callback: (postive) {
  //               if (postive) {
  //                 return openAppSettings().then((value) => validarPermissao(
  //                     liberado: liberado,
  //                     negado: negado,
  //                     perguntarPorLiberacao: false));
  //               } else {
  //                 return false;
  //               }
  //         }, textoAcaoSecundaria: localizedString('later'));
  //       } else {
  //         negado?.call();
  //         return false;
  //       }
  //     } else {
  //       liberado?.call();
  //       return true;
  //     }
  //     return null;
  //   });
  // }

  static Map<String, dynamic> removeNullsFromMap(Map<String, dynamic> json) => json
    ..removeWhere((String key, dynamic value) => value == null)
    ..map<String, dynamic>((key, value) => MapEntry(key, removeNulls(value)));

  static String quantidadeEmLitros(num mls) => '${(mls / 1000)} Lt';
  static String quantidadeEmLitrosOuMl(num mls) => mls > 1000 ? '${(mls / 1000)} lt' : '$mls ml';
  static String sentenseCase(String? texto) {
    String retorno = '';
    (texto ?? '').split(' ').forEach((palavra) {
      retorno += capitalize(palavra) + ' ';
    });
    return retorno.trim();
  }

  static bool validarDataFormatoMMYY(String data) {
    RegExp regExp = new RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$');
    if (!regExp.hasMatch(data)) {
      return false;
    }
    List<String> partes = data.split('/');
    int mes = int.parse(partes[0]);
    if (mes > 12) {
      return false;
    }
    return true;
  }

  static bool validarDataDepoisDeHojeFormatoMMYY(String data) {
    RegExp regExp = new RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$');
    if (!regExp.hasMatch(data)) {
      return false;
    }
    List<String> partes = data.split('/');
    int mes = int.parse(partes[0]);
    int ano = int.parse(partes[1]);

    DateTime dataRetorno = DateTime(ano + 2000, mes, 1);
    DateTime dataHoje = DateTime.now();

    return dataRetorno.isAfter(dataHoje);
  }

  static String? sentenseCaseFirst(String? texto) {
    var auxiliar = '';
    for (int i = 0; i < (texto ?? '').length; i++) {
      if (i == 0) {
        auxiliar += texto![0].toUpperCase();
      } else {
        auxiliar += texto![i].toLowerCase();
      }
    }
    return auxiliar;
  }

  static String pegarAte4caracteres(String? texto) {
    var auxiliar = '';
    for (int i = 0; i < 4; i++) {
      auxiliar += texto![i].toLowerCase();
    }
    return auxiliar;
  }

  static num percentEntre0e1({num? percent, num? valorA, num? valorB}) {
    try {
      if (percent != null) {
        if (percent < 0) return 0.0;
        return percent > 1.0 ? 1.0 : percent.toDouble();
      } else {
        if (valorB == 0) valorB = 1;
        if (valorA == null || valorB == null) return 0.0;
        if ((valorA / valorB).toDouble() > 1.0) return 1.0;
        return (valorA / valorB).toDouble();
      }
    } catch (e) {
      return 0;
    }
  }

  static num percentEntre0e100({num? percent, num? valorA, num? valorB}) {
    try {
      if (percent != null) {
        if (percent < 0) return 0.0;
        return percent > 100 ? 1.0 : percent.toDouble();
      } else {
        if (valorB == 0) valorB = 1;
        if (valorA == null || valorB == null) return 0.0;
        if ((valorA / valorB).toDouble() > 1.0) return 1.0;
        return (valorA / valorB).toDouble();
      }
    } catch (e) {
      return 0;
    }
  }

  static final _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  static String getRandomString(int length) => String.fromCharCodes(Iterable.generate(length, (_) => _chars.codeUnitAt(Random().nextInt(_chars.length))));
  static String arrendonaNumero(valor) => NumberFormat.compact(
        locale: 'PT_br',
      ).format(valor ?? 0);
  static String arrendonaNumero2Casas(valor) => NumberFormat.simpleCurrency(
        locale: 'PT_br',
      ).format(valor ?? 0).replaceAll('R\$ ', '');
  static String formatarDinheiro(valor) => NumberFormat.simpleCurrency(
        locale: 'PT_br',
      ).format(valor ?? 0);
  static String formatarPorcentagem(number) => NumberFormat.percentPattern().format(number);
  static String formatarPorcentagemDecimal(number, {int minDigits = 2}) => NumberFormat.decimalPercentPattern(decimalDigits: minDigits).format(number);
  static String formatarPorcentagemComPresicao(valorDecimal, {int minDigits = 2}) {
    // Convertendo o valor decimal para porcentagem e formatando para duas casas decimais
    String valorFormatado = (valorDecimal * 100).toStringAsFixed(2);

    // Convertendo a string de volta para double para fazer o arredondamento manual
    double valorArredondado = double.parse(valorFormatado);

    // Verificar se é necessário arredondar para baixo
    if (valorArredondado > valorDecimal * 100) {
      valorArredondado -= 0.01;
    }
    return '${valorArredondado.toStringAsFixed(2)}%';
  }

  static String stringOrNumToCurrencyCompleta(valor, {num? decimalLimit}) => NumberFormat.currency(
        locale: 'PT_br',
        symbol: 'R\$',
      ).format(valor);
// static String stringOrNumToCurrency(valor, {num decimalLimit})=> NumberFormat.compactCurrency(locale: "PT_br",symbol: "R\$", decimalDigits: decimalLimit,).format(valor);
//     String capitalize(String s){
//   s = s.toLowerCase();
//   if(s.isEmpty) return "";
//   return s[0].toUpperCase() + s.substring(1);
// }
  static bool validarEnderecoEmail(email) => RegExp(r'^[\w-]+(\.[\w-]+)*@[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*(\.[a-zA-Z]{2,})$').hasMatch(email ?? '');
//String getHoraIniciFim(num inicio, num fim) => (inicio != null ? formatDate(DateTime.fromMillisecondsSinceEpoch(inicio), [HH,':',nn,]).toString(): "--:--")+" até "+ (fim != null ? formatDate(DateTime.fromMillisecondsSinceEpoch(fim), [HH,':',nn,]).toString(): "--:--");

  static bool validarEmailComEspaco(String email) {
    // Remover espaços em branco do início e fim do email
    String emailSemEspacos = email.trim();
    RegExp regex = RegExp(r'^[\w-]+(\.[\w-]+)*@[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*(\.[a-zA-Z]{2,})$');

    // Verificar se o email corresponde ao padrão e não contém espaços
    return regex.hasMatch(emailSemEspacos) && email == emailSemEspacos;
  }

  static bool get usuarioTemPremiumLiberado => !GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_REFEICOES).ePremium!
      ? true
      : GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_REFEICOES).ePremium! && GetIt.I.get<ControladorCliente>().tipoDePremiumContratado != TipoDePremium.NENHUM;

  // Validar número de CPF
  static bool isCPFValid(String? cpf) {
    if (cpf == null) return false;

    // Obter somente os números do CPF
    var numeros = cpf.replaceAll(RegExp(r'[^0-9]'), '');

    // Testar se o CPF possui 11 dígitos
    if (numeros.length != 11) return false;

    // Testar se todos os dígitos do CPF são iguais
    if (RegExp(r'^(\d)\1*$').hasMatch(numeros)) return false;

    // Dividir dígitos
    List<int> digitos = numeros.split('').map((String d) => int.parse(d)).toList();

    // Calcular o primeiro dígito verificador
    var calcDv1 = 0;
    for (final i in Iterable<int>.generate(9, (i) => 10 - i)) {
      calcDv1 += digitos[10 - i] * i;
    }
    calcDv1 %= 11;
    var dv1 = calcDv1 < 2 ? 0 : 11 - calcDv1;

    // Testar o primeiro dígito verificado
    if (digitos[9] != dv1) return false;

    // Calcular o segundo dígito verificador
    var calcDv2 = 0;
    for (final i in Iterable<int>.generate(10, (i) => 11 - i)) {
      calcDv2 += digitos[11 - i] * i;
    }
    calcDv2 %= 11;
    var dv2 = calcDv2 < 2 ? 0 : 11 - calcDv2;

    // Testar o segundo dígito verificador
    if (digitos[10] != dv2) return false;

    return true;
  }

  void showDialogCarregando(BuildContext context, {bool barrierDismissible = false}) {
    showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext _) {
          return WillPopScope(
            onWillPop: () {
              return Future.value(true);
            },
            child: Center(
              child: LoadingAnimationWidget.discreteCircle(
                color: Theme.of(context).primaryColor,
                secondRingColor: lighten(Theme.of(context).primaryColor, 0.2).withValues(alpha: 0.8),
                thirdRingColor: lighten(Theme.of(context).primaryColor, 0.3).withValues(alpha: 0.3),
                size: 50,
              ),
            ),
          );
        });
  }

   void showDialogCarregandoLogin(BuildContext context, {bool barrierDismissible = false}) {
    showDialog(
        useSafeArea: false,
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext _) {
          return WillPopScope(
            onWillPop: () {
              return Future.value(false);
            },
            child: Material(
              child: DScard(
                borderRadius: 0,
                gradiente: true,
                paddingInterno: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(),
                    Center(
                      child: LoadingAnimationWidget.discreteCircle(
                        color: corQuandoFundoForGradiente(context),
                        secondRingColor: lighten(Theme.of(context).primaryColor, 0.2).withValues(alpha: 0.8),
                        thirdRingColor: lighten(Theme.of(context).primaryColor, 0.3).withValues(alpha: 0.3),
                        size: 50,
                      ),
                    ),
                    Column(
                      children: [
                        DStextSubheadline('login_rapido_titulo', eHeavy: true, textAlign: TextAlign.center, corCustomizada: corQuandoFundoForGradiente(context),),
                        DStextBody('login_rapido_subtitulo', eHeavy: false, textAlign: TextAlign.center, corCustomizada: corQuandoFundoForGradiente(context),),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  void showDialogCriouTreino(BuildContext context, bool edicao, {Function? callback}) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.centerRight,
                child: InkWell(
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    callback!();
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 32),
                child: Container(
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/images/pct-sucess.png',
                    height: 70,
                    width: 70,
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                child: TextBody2(edicao ? localizedString('your_workout_has_been_edited') : localizedString('your_workout_has_been_created'), textAlign: TextAlign.center),
              )
            ],
          ),
        );
      },
    );
  }

  void showDialogExcluiuTreino(BuildContext context, Function callback) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.centerRight,
                child: InkWell(
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    callback();
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 32),
                child: Container(
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/images/pct-sucess.png',
                    height: 70,
                    width: 70,
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                child: TextBody2('your_sheet_has_been_deleted', textAlign: TextAlign.center),
              )
            ],
          ),
        );
      },
    );
  }

  void showDialogoRemoverAtividade({required BuildContext context, Atividades? atividades, Function(bool confirm)? callback}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: TextHeadLine1(
            'Deseja Remover ${atividades!.nome} ?',
          ),
          content: TextBody1(
            'Ao confirmar você está removendo a atividade da lista',
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () {
                callback!(true);
                Navigator.of(context).pop();
              },
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(
                Colors.transparent,
              )),
              child: const Text('Confirmar', style: TextStyle(color: Colors.white)),
            ),
            ElevatedButton(
              onPressed: () {
                callback!(false);
                Navigator.of(context).pop();
              },
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(
                Colors.transparent,
              )),
              child: const Text('Cancelar', style: TextStyle(color: Colors.white)),
            )
          ],
        );
      },
    );
  }

  void showDialogoRemoverTAG({required BuildContext context, String? filtro, Function(bool confirm)? callback}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: TextHeadLine1(
            'Deseja remover $filtro ?',
          ),
          content: TextBody1(
            'Ao clicar em confirmar a TAG será removida',
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () {
                callback!(true);
                Navigator.of(context).pop();
              },
              style: ButtonStyle(backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor)),
              child: const Text('Confirmar', style: TextStyle(color: Colors.white)),
            ),
            ElevatedButton(
              onPressed: () {
                callback!(false);
                Navigator.of(context).pop();
              },
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(
                Colors.transparent,
              )),
              child: const Text('Cancelar', style: TextStyle(color: Colors.white)),
            )
          ],
        );
      },
    );
  }

  void showDialogExcluirTreino(BuildContext context, Function? callback) {
    BasicWdigetUtil().showDialogMensagem(
      context,
      textoAcaoPrincipal: localizedString('delete'),
      textoAcaoSecundaria: localizedString('cancel'),
      mensagem: localizedString('are_you_sure_delete_workout'),
      titulo: localizedString('attention'),
      callback: (postive) {
        if (postive) callback!();
      },
    );
    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return Container(
    //       height: 200,

    //       child: CardPadrao(
    //           child: Wrap(
    //             runAlignment: WrapAlignment.center,
    //             crossAxisAlignment: WrapCrossAlignment.center,
    //             children: <Widget>[
    //               Container(
    //                 alignment: Alignment.center,
    //                 child: Column(
    //                   children: <Widget>[
    //                     TextHeadLine3(
    //                       "Atenção",
    //                     ),
    //                     Padding(
    //                       padding: const EdgeInsets.fromLTRB(0, 8, 0, 24),
    //                       child: TextBody1("Tem certeza que deseja excluir seu treino? ", textAlign: TextAlign.center),
    //                     ),
    //                   ],
    //                 ),
    //               ),
    //               Row(
    //                 children: <Widget>[
    //                   Expanded(
    //                       child: BotaoPrimario(
    //                     value: "Cancelar",
    //                     onTap: () => {Navigator.of(context).pop()},
    //                   )),
    //                   Expanded(
    //                       child: BotaoPrimario(
    //                     value: "Excluir",
    //                     onTap: () {
    //                       Navigator.of(context).pop();
    //                       callback();
    //                     },
    //                   ))
    //                 ],
    //               )
    //             ],
    //           ),
    //         ),
    //     );
    //   },
    // );
  }

  void showActionTreino({required BuildContext context, Function? onExcluir, Function? onEditar}) {
    BasicWdigetUtil().showDialogMensagem(context, titulo: localizedString('options'), acoes: [
      BotaoSecundario(
        onTap: () {
          Navigator.pop(context);
          onEditar!();
        },
        value: localizedString('edit'),
      ),
      BotaoSecundario(
        onTap: () {
          Navigator.pop(context);
          showDialogExcluirTreino(context, onExcluir);
        },
        value: localizedString('excluir'),
      ),
      BotaoPrimario(
        onTap: () {
          Navigator.pop(context);
        },
        value: localizedString('cancel'),
      )
    ]);
    // showCupertinoModalPopup(
    //     context: context,
    //     builder: (BuildContext context) => CupertinoActionSheet(
    //           cancelButton: CupertinoActionSheetAction(onPressed: Navigator.of(context).pop, child: Text("Cancelar")),
    //           actions: <Widget>[
    //             CupertinoActionSheetAction(
    //               child: Text(
    //                 "Editar",
    //                 style: TextStyle(color: Colors.blue),
    //               ),
    //               onPressed: () {
    //                 Navigator.of(context).pop();
    //                 onEditar();
    //               },
    //             ),
    //             CupertinoActionSheetAction(
    //               child: Text("Excluir"),
    //               isDefaultAction: true,
    //               onPressed: () {
    //                 Navigator.of(context).pop();
    //                 showDialogExcluirTreino(context, onExcluir);
    //               },
    //             ),
    //           ],
    //         ));
  }

  void showActionTreinoAoVivo({
    required BuildContext context,
    required Function onEditar,
    required Function onExcluir,
  }) {
    showCupertinoModalPopup(
        context: context,
        builder: (BuildContext context) => CupertinoActionSheet(
              title: Text(localizedString('choose_an_option')),
              cancelButton: CupertinoActionSheetAction(onPressed: Navigator.of(context).pop, child: Text(localizedString('cancel'), style: const TextStyle(color: Colors.blue))),
              actions: <Widget>[
                CupertinoActionSheetAction(
                  child: Text(
                    localizedString('edit'),
                    style: const TextStyle(color: Colors.blue),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    onEditar();
                  },
                ),
                CupertinoActionSheetAction(
                  child: Text(localizedString('excluir')),
                  isDestructiveAction: true,
                  onPressed: () {
                    Navigator.of(context).pop();
                    showDialogExcluirTreino(context, onExcluir);
                  },
                ),
              ],
            ));
  }

  showDialogPreenchaCorretamente(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.center,
                child: Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextHeadLine3('Ops!', textAlign: TextAlign.center),
                    ),
                    TextBody1('to_continue_you_must_add_a_name', textAlign: TextAlign.center),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    value: localizedString('got_it')),
              )
            ],
          ),
        );
      },
    );
  }

  showDialogMensagem({required BuildContext context, String? titulo, String? mensagem}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.center,
                child: Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextHeadLine3(titulo ?? 'Ops!', textAlign: TextAlign.center),
                    ),
                    TextBody1(mensagem ?? '', textAlign: TextAlign.center),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    value: localizedString('got_it')),
              )
            ],
          ),
        );
      },
    );
  }

  showDialogPreenchaCorretamenteTreino(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.center,
                child: Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextHeadLine3('Ops!', textAlign: TextAlign.center),
                    ),
                    TextBody1('to_continue_you_need_to_add', textAlign: TextAlign.center),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    value: localizedString('got_it')),
              )
            ],
          ),
        );
      },
    );
  }

  showDialogInput({required BuildContext context, String? titulo, Function(String text)? callback, String? hintText, String? text}) {
    TextEditingController _textController = TextEditingController();
    _textController.text = text ?? '';
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 0, 8),
                child: Container(
                  alignment: Alignment.topLeft,
                  child: InkWell(
                    child: const Icon(
                      PersonalIcon.arrow_left,
                      size: 20,
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                child: Column(
                  children: <Widget>[TextHeadLine1(titulo, textAlign: TextAlign.center), const Divider()],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: TextFieldPadrao(
                      hintText: hintText,
                      valorNoCampo: text,
                      controlller: _textController,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      callback!(_textController.text);
                      Navigator.of(context).pop();
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 16, 16),
                      child: TextAction('save'),
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  void showDialogComImagemECallBack(
      {required BuildContext context,
      Function? acaoPrimaria,
      Function? acaoSecundaria,
      String? tituloBotaoSecundario,
      String? tituloBotaoPrimario,
      String? tituloMensagem,
      String? subtituloMensagem,
      Color? corBotaoPrimario,
      String? imagePath,
      TipoAlerta? tipoAlerta,
      bool vaiTerIconeFechar = true}) {
    showDialog(
        context: context,
        barrierDismissible: vaiTerIconeFechar,
        builder: (BuildContext _) {
          return Padding(
            padding: const EdgeInsets.all(24),
            child: Center(
              child: CardPadrao(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      child: Row(
                        children: [
                          const Spacer(),
                          vaiTerIconeFechar
                              ? InkWell(
                                  child: const Icon(PersonalIcon.x),
                                  onTap: () {
                                    GetIt.I.get<NavigationService>().goBack();
                                  })
                              : Container(
                                  height: 8,
                                ),
                        ],
                      ),
                    ),
                    tipoAlerta == null
                        ? Container()
                        : SizedBox(
                            width: 150,
                            height: 150,
                            child: FlareActor(
                                tipoAlerta == TipoAlerta.alerta
                                    ? 'assets/gifs/pct-alert.flr'
                                    : tipoAlerta == TipoAlerta.sucesso
                                        ? 'assets/gifs/pct-sucess.flr'
                                        : 'assets/gifs/pct-error.flr',
                                alignment: Alignment.center,
                                fit: BoxFit.cover,
                                animation: 'start')),
                    imagePath == null ? const SizedBox(height: 16) : Container(),
                    imagePath == null
                        ? Container()
                        : Padding(
                            padding: const EdgeInsets.only(bottom: 24),
                            child: SizedBox(child: Image.asset(imagePath), width: 130),
                          ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                          child: TextHeadLine1(tituloMensagem, textAlign: TextAlign.center),
                        ),
                        subtituloMensagem == null
                            ? Container()
                            : Padding(
                                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                child: TextBody1(subtituloMensagem, textAlign: TextAlign.center),
                              ),
                      ],
                    ),
                    acaoSecundaria == null
                        ? acaoPrimaria != null
                            ? Padding(
                                padding: const EdgeInsets.all(16),
                                child: BotaoPrimario(
                                  onTap: acaoPrimaria as dynamic Function()?,
                                  value: localizedString(tituloBotaoPrimario ?? ''),
                                  fullWidth: false,
                                  corDefault: corBotaoPrimario == null ? Theme.of(context).primaryColor : corBotaoPrimario,
                                ),
                              )
                            : const SizedBox(height: 24)
                        : Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(40, 0, 40, 8),
                                  child: BotaoPrimario(
                                    onTap: acaoPrimaria as dynamic Function()?,
                                    value: localizedString(tituloBotaoPrimario ?? ''),
                                    fullWidth: true,
                                    corDefault: corBotaoPrimario == null ? Theme.of(context).primaryColor : corBotaoPrimario,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(40, 0, 40, 0),
                                  child: BotaoSecundario(
                                    //corDefault: Colors.white,
                                    onTap: acaoSecundaria as dynamic Function()?,
                                    value: tituloBotaoSecundario,
                                  ),
                                )
                              ],
                            ),
                          ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  void showDialogSelecao({
    required BuildContext context,
    Function(DialogSelecao itemSelecionado)? acaoPrimaria,
    Function? acaoSecundaria,
    String? tituloBotaoSecundario,
    String? tituloBotao,
    String? tituloMensagem,
    String? subtitulo,
    bool vaiTerIconeFechar = true,
    Color? corElementos,
    List<DialogSelecao>? itens,
  }) {
    Widget construirItens({required List<DialogSelecao> listaItens, context, Function? callback}) {
      List<Widget> widgets = [];
      for (int index = 0; index < listaItens.length; index++) {
        widgets.add(GestureDetector(
          onTap: () {
            listaItens[index].selecionado = !listaItens[index].selecionado!;
            for (int i = 0; i < listaItens.length; i++) {
              if (index != i) {
                listaItens[i].selecionado = false;
              }
            }
            callback!();
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(height: 10),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(listaItens[index].selecionado! ? TreinoIcon.check_circle : TreinoIcon.circle, size: 20, color: corElementos == null ? Theme.of(context).primaryColor : corElementos),
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: SizedBox(
                        width: (MediaQuery.of(context).size.width / 2),
                        child: DStextBody(
                          listaItens[index].titulo!,
                          maximoLinhas: 1,
                          eHeavy: false,
                        ),
                      ),
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 8),
                  child: Divider(),
                )
              ],
            ),
          ),
        ));
      }
      return Wrap(children: widgets);
    }

    showModalBottomSheet(
        context: context,
        isDismissible: true,
        isScrollControlled: true,
        builder: (BuildContext _) {
          return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
            return Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: new BoxDecoration(color: Theme.of(context).cardColor, borderRadius: BorderRadius.circular(25)),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          vaiTerIconeFechar ? Container(width: 34) : Container(),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                                child: DStextHeadline(tituloMensagem ?? 'escolha', textAlign: TextAlign.center),
                              ),
                              subtitulo == null
                                  ? Container()
                                  : Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                      child: DStextCaption1(subtitulo, textAlign: TextAlign.center),
                                    ),
                            ],
                          ),
                          vaiTerIconeFechar
                              ? DSbotaoCircular(
                                  altura: 34,
                                  icone: TreinoIcon.times,
                                  categoria: Categoria.secundario,
                                  onTap: () {
                                    GetIt.I.get<NavigationService>().goBack();
                                  },
                                )
                              : Container(
                                  height: 0,
                                ),
                        ],
                      ),
                    ),
                    const Divider(),
                    SizedBox(
                      height: itens!.length > 10 ? MediaQuery.of(context).size.height * 0.7 : itens.length * 55,
                      child: Scrollbar(
                        thumbVisibility: true,
                        child: SingleChildScrollView(
                          child: construirItens(
                              listaItens: itens,
                              context: context,
                              callback: () {
                                setState(() {});
                              }),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                      child: DSbotaoPadrao(
                        desabilitado: itens.where((element) => element.selecionado!).toList().isEmpty,
                        titulo: tituloBotao ?? localizedString('change'),
                        onTap: () {
                          if (itens.where((element) => element.selecionado!).toList().length == 1) {
                            var itemSelecionado = itens.where((element) => element.selecionado!).toList().first;
                            acaoPrimaria!(itemSelecionado);
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
            );
          });
        });
  }

  showSelectDeData({required BuildContext context, Function(DateTime hora)? callBack, DateTime? horaInicial, String? legenda, DateTime? dataLimite}) {
    DateTime selecionado = horaInicial ?? DateTime.now();
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        context: context,
        builder: (_) => StatefulBuilder(
              builder: (context, StateSetter setState) {
                return SafeArea(
                  child: DScard(
                    borderRadius: 24,
                    paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    child: SizedBox(
                      child: Wrap(
                        runSpacing: 16,
                        children: [
                          Container(
                            decoration: const BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
                            child: Column(children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                                    child: Container(
                                      width: 34,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 12),
                                    child: DStextSubheadline(legenda ?? ''),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                                    child: DSbotaoCircular(
                                      altura: 34,
                                      icone: TreinoIcon.times,
                                      categoria: Categoria.secundario,
                                      onTap: () async {
                                        Navigator.of(context).pop();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const Divider(),
                            ]),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: Container(
                                      height: 200,
                                      color: Theme.of(context).cardColor,
                                      child: CupertinoTheme(
                                        data: CupertinoThemeData(
                                          textTheme: CupertinoTextThemeData(
                                              dateTimePickerTextStyle:
                                                  TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                              pickerTextStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                              textStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black)),
                                        ),
                                        child: CupertinoDatePicker(
                                          maximumDate: dataLimite,
                                          backgroundColor: Theme.of(context).cardColor,
                                          mode: CupertinoDatePickerMode.date,
                                          initialDateTime: horaInicial ?? DateTime.now(),
                                          onDateTimeChanged: (DateTime newDateTime) {
                                            selecionado = newDateTime;
                                          },
                                          use24hFormat: true,
                                          minuteInterval: 1,
                                        ),
                                      )),
                                ),
                                DSbotaoPadrao(
                                  titulo: 'confirm',
                                  onTap: () {
                                    callBack!(selecionado);
                                  },
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ));
  }

  showSelectDeHora({required BuildContext context, Function(DateTime hora)? callBack, DateTime? horaInicial, String? legenda}) {
    DateTime selecionado = horaInicial ?? DateTime.now();
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (_) => SizedBox(
              height: 250,
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                child: Column(
                  children: <Widget>[
                    Container(
                      margin: const EdgeInsets.all(0),
                      child: Container(
                        height: 10,
                      ),
                      decoration: BoxDecoration(
                          color: Theme.of(context).cardColor.withAlpha(155),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20.0),
                            topRight: Radius.circular(20.0),
                          )),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        color: Theme.of(context).cardColor.withAlpha(155),
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.zero,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: TextBody1(
                                legenda ?? '',
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                callBack!(selecionado);
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: TextHeader(
                                  'OK',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 8,
                      child: Container(
                        color: Theme.of(context).cardColor.withAlpha(155),
                        child: CupertinoTheme(
                          data: CupertinoThemeData(
                            textTheme: CupertinoTextThemeData(
                                dateTimePickerTextStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                pickerTextStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                textStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black)),
                          ),
                          child: CupertinoDatePicker(
                            backgroundColor: Colors.transparent,
                            mode: CupertinoDatePickerMode.time,
                            initialDateTime: horaInicial ?? DateTime.now(),
                            onDateTimeChanged: (DateTime newDateTime) {
                              selecionado = newDateTime;
                            },
                            use24hFormat: true,
                            minuteInterval: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  List<Widget> getMedatalhaWidget({BuildContext? context, String? texto, required String asset}) {
    return <Widget>[
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(right: 10.0),
          child: TextBody1(
            texto,
            softWrap: true,
          ),
        ),
      ),
      Image.asset(
        asset,
        width: 37,
        height: 40,
      ),
    ];
  }

  showDialogRankingHelper(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Column(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(32),
                    child: TextHeader(
                      'Medalhas',
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: getMedatalhaWidget(context: context, texto: 'Acima de 13 treinos', asset: 'assets/images/pct-gold-medal.png'),
                  ),
                  Divider(color: Theme.of(context).dividerColor),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: getMedatalhaWidget(context: context, texto: '7 a 13 treinos executados', asset: 'assets/images/pct-silver-medal.png'),
                  ),
                  Divider(color: Theme.of(context).dividerColor),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: getMedatalhaWidget(context: context, texto: '1 a 6 treinos executados', asset: 'assets/images/pct-bronze-medal.png'),
                  ),
                  Divider(color: Theme.of(context).dividerColor),
                  Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                    child: TextBody1(
                      'Apenas 1 treino é contablizado por dia para o Ranking',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    value: localizedString('ok_got_it')),
              )
            ],
          ),
        );
      },
    );
  }

  showDialogSalvouImagem(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).cardColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Column(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(32),
                    child: TextHeader(
                      'Tudo pronto!',
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: TextBody1(
                      'A imagem foi salva na galeria com sucesso! ',
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    value: localizedString('fechar')),
              )
            ],
          ),
        );
      },
    );
  }

  void showActionCompartilhar({
    required BuildContext context,
    required Function onSalvar,
    required Function onContarParaAvo,
  }) {
    showCupertinoModalPopup(
        context: context,
        builder: (BuildContext bc) {
          return CupertinoTheme(
              data: CupertinoThemeData(
                barBackgroundColor: Theme.of(context).colorScheme.surface,
                textTheme: CupertinoTextThemeData(primaryColor: Colors.blue, textStyle: TextUtil.styleaction()),
              ),
              child: CupertinoActionSheet(
                title: Text(localizedString('choose_an_option')),
                cancelButton: CupertinoActionSheetAction(
                    onPressed: Navigator.of(context).pop,
                    child: Text(
                      localizedString('cancel'),
                    )),
                actions: <Widget>[
                  CupertinoActionSheetAction(
                    child: TextBody1('Instagram'),
                    onPressed: () {
                      analytic(EventosKey.compartilhar_escolheu_instagram);
                      Navigator.of(context).pop();
                      onContarParaAvo();
                    },
                  ),
                  CupertinoActionSheetAction(
                    child: TextBody1(
                      Platform.isAndroid ? localizedString('save_in_the_gallery') : localizedString('save_in_the_camera_roll'),
                    ),
                    onPressed: () {
                      analytic(EventosKey.compartilhar_salvou_na_galeria);
                      Navigator.of(context).pop();
                      onSalvar();
                    },
                  ),
                ],
              ));
        });
  }

  showSelectDeMinSegundos({required BuildContext context, Function(Duration hora)? callBack, Duration? mmssInicial, String? legenda}) {
    Duration selecionado = mmssInicial ?? const Duration(seconds: 30);
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (_) => SizedBox(
              height: 250,
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                child: Column(
                  children: <Widget>[
                    Container(
                      margin: const EdgeInsets.all(0),
                      child: Container(
                        height: 10,
                      ),
                      decoration: BoxDecoration(
                          color: Theme.of(context).cardColor.withAlpha(155),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20.0),
                            topRight: Radius.circular(20.0),
                          )),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        color: Theme.of(context).cardColor.withAlpha(155),
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.zero,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: TextBody1(
                                legenda ?? '',
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                callBack!(selecionado);
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: TextHeader(
                                  'OK',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 8,
                      child: Container(
                        color: Theme.of(context).cardColor.withAlpha(155),
                        child: CupertinoTheme(
                          data: CupertinoThemeData(
                            textTheme: CupertinoTextThemeData(
                                dateTimePickerTextStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                pickerTextStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black),
                                textStyle: TextStyle(fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark ? Colors.white : Colors.black)),
                          ),
                          child: CupertinoTimerPicker(
                            backgroundColor: Colors.transparent,
                            mode: CupertinoTimerPickerMode.ms,
                            initialTimerDuration: mmssInicial ?? const Duration(seconds: 30),
                            minuteInterval: 1,
                            onTimerDurationChanged: (Duration value) {
                              selecionado = value;
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  // Future<void> launchWhatsApp({required String? phone, required String message}) async {
  //   final link = WhatsAppUnilink(
  //   phoneNumber: phone,
  //   text: message,
  // );
  // try {
  //   await launch('$link');
  // } catch (e) {
  //   BasicWdigetUtil().showDialogMensagem(GetIt.I.get<NavigationService>().context, titulo: 'Ops', mensagem: localizedString('unable_to_launch_whatsapp'));
  // }

  //   /* var toLaunch = Uri(host: PlatformCheck.isIOS ? phone == null ? 'whatsapp://send?text=${Uri.encodeFull(message)}' :
  //     'whatsapp://wa.me/$phone/?text=${Uri.encodeFull(message)}' : 'https://api.whatsapp.com/send?phone=$phone&text=${Uri.encodeFull(message)}' );

  //   if(PlatformCheck.isIOS) {
  //     if(await canLaunchUrl(toLaunch)) {
  //       await launchUrl(toLaunch);
  //     }
  //   } else {
  //     launchUrl(toLaunch).catchError((onError){
  //       BasicWdigetUtil().showDialogMensagem(GetIt.I.get<NavigationService>().context, titulo: 'Ops', mensagem: localizedString('unable_to_launch_whatsapp'));
  //    });
  //   } */
  // }

  void launchWhatsApp({
    required String phone,
    required String message,
  }) async {
    phone = phone.replaceAll(' ', '').replaceAll('(', '').replaceAll(')', '').replaceAll('-', '');
    String url() {
      if (Platform.isIOS) {
        return 'whatsapp://wa.me/$phone/?text=${Uri.encodeFull(message)}';
      } else {
        return 'https://api.whatsapp.com/send?phone=$phone&text=${Uri.encodeFull(message)}';
      }
    }
    // final Uri toLaunch = Uri(host: Platform.isIOS ? phone == null ? 'whatsapp://send?text=${Uri.encodeFull(message)}' : 'whatsapp://wa.me/$phone/?text=${Uri.encodeFull(message)}' : 'https://api.whatsapp.com/send?phone=$phone&text=${Uri.encodeFull(message)}' );

    if (Platform.isIOS) {
      if (await canLaunch(url())) {
        await launch(url());
      }
    } else {
      // ignore: body_might_complete_normally_catch_error
      launch(url(), forceWebView: false).catchError((onError) {
        BasicWdigetUtil().showDialogMensagem(GetIt.I.get<NavigationService>().context, titulo: 'Ops', mensagem: localizedString('unable_to_launch_whatsapp'));
      });
    }
  }

  modalCancelarFoto({required BuildContext context}) {
    showModalBottomSheet(
      isDismissible: false,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return DScard(
          paddingExterno: const EdgeInsets.all(16),
          paddingInterno: const EdgeInsets.all(16),
          child: Wrap(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      DSbotaoCircular(
                          altura: 30,
                          categoria: Categoria.primario,
                          corIconPrimarioCustomizado: const Color(0xFFFAC31E),
                          corPrimarioCustomizado: DSLib.theme == ThemeMode.dark ? const Color(0xff7D5E03) : const Color(0xffFDEBB4),
                          icone: TreinoIcon.exclamation_triangle),
                      const SizedBox(width: 8),
                      DStextHeadline('sair_sem_salvar', eHeavy: true),
                    ],
                  ),
                  DSbotaoCircular(
                      altura: 30,
                      categoria: Categoria.secundario,
                      onTap: () {
                        GetIt.I.get<NavigationService>().goBack();
                      },
                      icone: PersonalIcon.x)
                ],
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 24, 0, 24),
                child: DStextBody(
                  'sair_sem_salvar_registro',
                  eHeavy: false,
                ),
              ),
              DSbotaoPadrao(
                  tipoBotao: TipoBotao.secundario,
                  onTap: () {
                    GetIt.I.get<NavigationService>().goBack();
                  },
                  titulo: localizedString('manter_registro')),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 10, 0, 16),
                child: DSbotaoPadrao(
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    },
                    titulo: 'Apagar registro'),
              )
            ],
          ),
        );
      },
    );
  }

  showDialogMensagemComCallback({
    required BuildContext context,
    String? titulo,
    String? mensagem,
    required Function callback,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: Theme.of(context).canvasColor,
          content: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: <Widget>[
              Container(
                alignment: Alignment.center,
                child: Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextHeadLine1(titulo ?? 'Ops!', textAlign: TextAlign.center),
                    ),
                    TextBody2(mensagem ?? '', textAlign: TextAlign.center),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 25),
                child: BotaoPrimario(
                  value: localizedString('got_it'),
                  onTap: () {
                    Navigator.of(context).pop();
                    callback();
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }

  retornarCorGradientPadrao(context) {
    if (Theme.of(context).primaryColor == const Color(0xFFFF765E)) {
      return [const Color(0xFFF72500), const Color(0xFFFF4B2B), const Color(0xFFFF765E)];
    } else if (Theme.of(context).primaryColor == const Color(0xFFE25260)) {
      return [const Color(0xFFBA202F), const Color(0xFFDB2C3D), const Color(0xFFE25260)];
    } else if (Theme.of(context).primaryColor == const Color(0xFF48D567)) {
      return [const Color(0xFF28AB45), const Color(0xFF2EC750), const Color(0xFF48D567)];
    } else if (Theme.of(context).primaryColor == const Color(0xFFF0B924)) {
      return [const Color(0xFFD6A10F), const Color(0xFFF0B924), const Color(0xFFF3C750)];
    } else if (Theme.of(context).primaryColor == const Color(0xFF1998FC)) {
      return [const Color(0xFF0380E3), const Color(0xFF1998FC), const Color(0xFF47ADFD)];
    } else if (Theme.of(context).primaryColor == const Color(0xFFBA68BA)) {
      return [const Color(0xFF783878), const Color(0xFF914391), const Color(0xFFAA4EAA)];
    } else if (Theme.of(context).primaryColor == const Color(0xFFFF2970)) {
      return [const Color(0xFFF60052), const Color(0xFFFF2970), const Color(0xFFFF5B92)];
    } else {
      return [Theme.of(context).primaryColor, Theme.of(context).primaryColor.withValues(alpha:0.7)];
    }
  }
  abrirGaleria({required BuildContext context, required Function(Uint8List?)? onImageSelected}) async {
    var skdMaiorQue33 = false;
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo;
      androidInfo = await deviceInfo.androidInfo;
      skdMaiorQue33 = androidInfo.version.sdkInt >= 33;
    }
    await validarPermissoes(
        permissionsRequested: skdMaiorQue33 && Platform.isAndroid ? [Permission.camera, Permission.microphone] : [Permission.camera, Permission.storage, Permission.microphone],
        onPermissoesLiberadas: () async {
          UtilitarioApp().showDialogCarregando(context);
          final ImagePicker picker = ImagePicker();
          final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

          File? _foto;
          if (pickedFile != null) {
            _foto = File(pickedFile.path);
          } else {
            _foto = null; // usuário cancelou
          }
          
          Navigator.of(context).pop();
          if (_foto == null) {
            return;
          }
          decode.Image image = decode.decodeImage(_foto.readAsBytesSync())!;
          //    image = decode.decodeImage(new Io.File(foto as String).readAsBytesSync())!;
          decode.Image thumbnail = decode.copyResize(image, width: 800);
          new Io.File(_foto.path)..writeAsBytesSync(decode.encodePng(thumbnail));
          onImageSelected!(_foto.readAsBytesSync());
        },
        onPermissoesNegadas: () {
          DSalerta().exibirAlertaSimplificado(
              context: context, titulo: 'Ops!', subtitulo: 'É preciso que as permissões para câmera e microfone sejam liberada para entrar na chamada', tituloBotao: 'Ok');
          /*  BasicWdigetUtil.showModalComIconEAcao(context,tipoAlerta: TipoAlerta.alerta,
        onConfirm: () {
          Navigator.of(context).pop();
        }, headerText: 'É preciso que as permissões para câmera e microfone sejam liberada para entrar na chamada', confirmText: 'Ok'); */
        },
        onPermissoesPermanentementeNegadas: () {
          BasicWdigetUtil().showDialogMensagemDS(context,
              titulo: 'É preciso conceder permissão à câmera e ao microfone',
              mensagem: 'Acesse as configurações para conceder as permissões necessárias e ingressar na consulta',
              textoAcaoPrincipal: localizedString('settings'), callback: (postive) {
            if (postive) {
              openAppSettings();
            } else {
              Navigator.of(context).pop();
            }
          }, textoAcaoSecundaria: 'Já concedi');
        });
  }
  Future<void> abrirCameraPacto({required BuildContext context, required bool abrirComGaleria, required Function(Uint8List?)? onImageSelected, bool vaiTerCrop = true}) async {
    // Verifica versão do Android para ajustar permissões necessárias
    final bool skdMaiorQue33 = await _verificarVersaoAndroid();

    // Define permissões baseado na versão do Android
    final permissoes = _definirPermissoesNecessarias(skdMaiorQue33);

    await validarPermissoes(
        permissionsRequested: permissoes,
        onPermissoesLiberadas: () => _processarImagemCamera(context: context, abrirComGaleria: abrirComGaleria, vaiTerCrop: vaiTerCrop, onImageSelected: onImageSelected),
        onPermissoesNegadas: () => _exibirAlertaPermissaoNegada(context),
        onPermissoesPermanentementeNegadas: () => _exibirAlertaPermissaoNegada(context));
  }

  Future<bool> _verificarVersaoAndroid() async {
    if (!Platform.isAndroid) return false;

    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    return androidInfo.version.sdkInt >= 33;
  }

  List<Permission> _definirPermissoesNecessarias(bool skdMaiorQue33) {
    return skdMaiorQue33 && Platform.isAndroid ? [Permission.camera, Permission.microphone] : [Permission.camera, Permission.storage, Permission.microphone];
  }

  Future<void> _processarImagemCamera({required BuildContext context, required bool abrirComGaleria, required bool vaiTerCrop, required Function(Uint8List?)? onImageSelected}) async {
    final foto = await Navigator.pushNamed(context, '/camera', arguments: {
      'abrirComGaleria': abrirComGaleria,
      'vaiTerCrop': vaiTerCrop,
    });

    if (foto != null) {
      final file = Io.File(foto as String);
      final image = decode.decodeImage(file.readAsBytesSync())!;
      final thumbnail = decode.copyResize(image, width: 800);

      file.writeAsBytesSync(decode.encodePng(thumbnail));

      final processedImage = file.readAsBytesSync();
      onImageSelected?.call(processedImage);
    }
  }

  void _exibirAlertaPermissaoNegada(BuildContext context) {
    DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'acesso_camera_microfone', tituloBotao: 'Ok');
  }

  abrirCameraComparativo(context, bool vaiTerCrop, bool vaiTerFiltro, {required Function(Uint8List?)? onImageSelected}) async {
    var skdMaiorQue33 = false;
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo;
      androidInfo = await deviceInfo.androidInfo;
      skdMaiorQue33 = androidInfo.version.sdkInt >= 33;
    }
    await validarPermissoes(
        permissionsRequested: skdMaiorQue33 && Platform.isAndroid
            ? [Permission.camera, Permission.microphone, Permission.photos, Permission.videos]
            : [Permission.camera, Permission.storage, Permission.microphone],
        onPermissoesLiberadas: () async {
          final foto = await Navigator.pushNamed(context, '/tirarFotoComparacao', arguments: {'vaiTerCrop': vaiTerCrop, 'vaiTerFiltro': vaiTerFiltro});
          decode.Image image = decode.decodeImage(new Io.File(foto as String).readAsBytesSync())!;
          decode.Image thumbnail = decode.copyResize(image, width: 800);
          new Io.File(foto)..writeAsBytesSync(decode.encodePng(thumbnail));
          onImageSelected!(File(foto).readAsBytesSync()).call();
        },
        onPermissoesNegadas: () {
          DSalerta().exibirAlertaSimplificado(
              context: context, titulo: 'Ops!', subtitulo: 'acesso_camera_microfone', tituloBotao: 'Ok');
          /* BasicWdigetUtil.showModalComIconEAcao(context,tipoAlerta: TipoAlerta.alerta,
          onConfirm: () {
            Navigator.of(context).pop();
          }, headerText: 'acesso_camera_microfone', confirmText: 'Ok'); */
        },
        onPermissoesPermanentementeNegadas: () {
          DSalerta().exibirAlerta(tipoAlerta: TipoAlerta.alerta, showActionCannotBeUnode: false, context: context, titulo: 'acesso_camera_microfone', vaiTerBotaoFechar: false, subtitulo: 'acesso_camera_microfone_subtitulo', tituloBotao: 'abrir_ajustes', tituloBotaoSecundario: 'cancel', onTap: () {
            openAppSettings();
          });
        });
  }

  static Future<void> vibrar() async {
    await SystemChannels.platform.invokeMethod<void>(
      'HapticFeedback.vibrate',
      'HapticFeedbackType.lightImpact',
    );
  }

  String retornarApenasOPrimeiroNome(String nome) {
    //split string
    var arr = nome.split(' ');
    if (nome.isEmpty || arr.first.isEmpty) {
      return '-';
    }
    //garante que não terá espaço em branco nos elementos que serão iterados
    arr.removeWhere((value) => value == '');
    var nomeTratado = '';
    for (var i = 0; i < arr.length; i++) {
      if (i == 0) {
        nomeTratado = nomeTratado + arr[i] + ' ';
      } else {
        nomeTratado = nomeTratado + (arr[i].characters.first.toUpperCase()) + '. ';
      }
    }
    return sentenseCase(nomeTratado);
  }

  String retornarSomenteOPrimeiroNome(String nome) {
    //split string
    var arr = nome.split(' ');
    if (nome.isEmpty || arr.first.isEmpty) {
      return '-';
    }
    //garante que não terá espaço em branco nos elementos que serão iterados
    arr.removeWhere((value) => value == '');
    var nomeTratado = '';
    for (var i = 0; i < arr.length; i++) {
      if (i == 0) {
        nomeTratado = nomeTratado + arr[i] + ' ';
      } else {
        nomeTratado = nomeTratado + (arr[i].characters.first.toUpperCase()) + '. ';
      }
    }
    return sentenseCase(arr[0].isEmpty ? '' : arr[0]);
  }

  static String tratarStringUrl(String url) {
    if (url.contains('instagram.com')) {
      url = 'https://www.instagram.com/${getUsernameIntagramUrl(url)}';
    } else if (url.contains('wa.me') || url.contains('whatsapp')) {
      return url.replaceAll('https://', 'whatsapp://');
    } else if (url.contains('https://')) {
      url = url.replaceAll('https://', '');
    } else if (url.contains('www.')) {
      url = url.replaceAll('www.', 'http://');
    }
    return url;
  }

  static String? getUsernameIntagramUrl(String url) {
    RegExp regExp = RegExp(r'instagram\.com\/([^\/?#]+)');
    Match? match = regExp.firstMatch(url);

    if ((match?.groupCount ?? 0) >= 1) {
      return match?.group(1);
    } else {
      return '';
    }
  }
}

class DialogSelecao {
  String? titulo;
  bool? selecionado;
  num? id;
  String? enumerador;
  DialogSelecao({this.titulo, this.selecionado, this.id, this.enumerador});
}

class MaskedTextController extends TextEditingController {
  MaskedTextController({String? text, this.mask, Map<String, RegExp>? translator}) : super(text: text) {
    this.translator = translator ?? MaskedTextController.getDefaultTranslator();

    this.addListener(() {
      var previous = this._lastUpdatedText;
      if (this.beforeChange(previous, this.text)) {
        this.updateText(this.text);
        this.afterChange(previous, this.text);
      } else {
        this.updateText(this._lastUpdatedText);
      }
    });

    this.updateText(this.text);
  }

  String? mask;

  Map<String, RegExp>? translator;

  Function afterChange = (String previous, String next) {};
  Function beforeChange = (String previous, String next) {
    return true;
  };

  String _lastUpdatedText = '';

  void updateText(String? text) {
    if (text != null) {
      this.text = this._applyMask(this.mask!, text);
    } else {
      this.text = '';
    }

    this._lastUpdatedText = this.text;
  }

  void updateMask(String mask, {bool moveCursorToEnd = true}) {
    this.mask = mask;
    this.updateText(this.text);

    if (moveCursorToEnd) {
      this.moveCursorToEnd();
    }
  }

  void moveCursorToEnd() {
    var text = this._lastUpdatedText;
    this.selection = new TextSelection.fromPosition(new TextPosition(offset: (text).length));
  }

  @override
  set text(String newText) {
    if (super.text != newText) {
      super.text = newText;
      this.moveCursorToEnd();
    }
  }

  static Map<String, RegExp> getDefaultTranslator() {
    return {'A': new RegExp(r'[A-Za-z]'), '0': new RegExp(r'[0-9]'), '@': new RegExp(r'[A-Za-z0-9]'), '*': new RegExp(r'.*')};
  }

  String _applyMask(String mask, String value) {
    String result = '';

    var maskCharIndex = 0;
    var valueCharIndex = 0;

    while (true) {
      // if mask is ended, break.
      if (maskCharIndex == mask.length) {
        break;
      }

      // if value is ended, break.
      if (valueCharIndex == value.length) {
        break;
      }

      var maskChar = mask[maskCharIndex];
      var valueChar = value[valueCharIndex];

      // value equals mask, just set
      if (maskChar == valueChar) {
        result += maskChar;
        valueCharIndex += 1;
        maskCharIndex += 1;
        continue;
      }

      // apply translator if match
      if (this.translator!.containsKey(maskChar)) {
        if (this.translator![maskChar]!.hasMatch(valueChar)) {
          result += valueChar;
          maskCharIndex += 1;
        }

        valueCharIndex += 1;
        continue;
      }

      // not masked value, fixed char on mask
      result += maskChar;
      maskCharIndex += 1;
      continue;
    }

    return result;
  }
}

class MoneyMaskedTextController extends TextEditingController {
  MoneyMaskedTextController({double initialValue = 0.0, this.decimalSeparator = ',', this.thousandSeparator = '.', this.rightSymbol = '', this.leftSymbol = '', this.precision = 2}) {
    _validateConfig();

    this.addListener(() {
      this.updateValue(this.numberValue);
      this.afterChange(this.text, this.numberValue);
    });

    this.updateValue(initialValue);
  }

  final String decimalSeparator;
  final String thousandSeparator;
  final String rightSymbol;
  final String leftSymbol;
  final int precision;

  Function afterChange = (String maskedValue, double rawValue) {};

  double _lastValue = 0.0;

  void updateValue(double value) {
    double valueToUse = value;

    if (value.toStringAsFixed(0).length > 12) {
      valueToUse = _lastValue;
    } else {
      _lastValue = value;
    }

    String masked = this._applyMask(valueToUse);

    if (rightSymbol.length > 0) {
      masked += rightSymbol;
    }

    if (leftSymbol.length > 0) {
      masked = leftSymbol + masked;
    }

    if (masked != this.text) {
      this.text = masked;

      var cursorPosition = super.text.length - this.rightSymbol.length;
      this.selection = new TextSelection.fromPosition(new TextPosition(offset: cursorPosition));
    }
  }

  double get numberValue {
    List<String> parts = _getOnlyNumbers(this.text).split('').toList(growable: true);

    parts.insert(parts.length - precision, '.');

    return double.parse(parts.join());
  }

  _validateConfig() {
    bool rightSymbolHasNumbers = _getOnlyNumbers(this.rightSymbol).length > 0;

    if (rightSymbolHasNumbers) {
      throw new ArgumentError('rightSymbol must not have numbers.');
    }
  }

  String _getOnlyNumbers(String text) {
    String cleanedText = text;

    var onlyNumbersRegex = new RegExp(r'[^\d]');

    cleanedText = cleanedText.replaceAll(onlyNumbersRegex, '');

    return cleanedText;
  }

  String _applyMask(double value) {
    List<String> textRepresentation = value.toStringAsFixed(precision).replaceAll('.', '').split('').reversed.toList(growable: true);

    textRepresentation.insert(precision, decimalSeparator);

    for (var i = precision + 4; true; i = i + 4) {
      if (textRepresentation.length > i) {
        textRepresentation.insert(i, thousandSeparator);
      } else {
        break;
      }
    }

    return textRepresentation.reversed.join('');
  }
}

double percentualTratado({required double percentual}) {
  if (percentual < 0 || percentual > 1) {
    return 1;
  }
  return percentual;
}
