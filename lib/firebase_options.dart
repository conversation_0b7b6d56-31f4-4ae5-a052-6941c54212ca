// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:app_treino/flavors.dart';
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        if (F.isPersonalizado) {
          return F.firebaseInitConfig!;
        }
        return android;
      case TargetPlatform.iOS:
        if (F.isPersonalizado) {
          return F.firebaseInitConfig!;
        }
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBlmcsWvOkQHfRExGeKKuQyEgDed_0TwIY',
    appId: '1:977449544580:web:1955f2e4044aeb30',
    messagingSenderId: '977449544580',
    projectId: 'app-do-aluno-unificado',
    authDomain: 'app-do-aluno-unificado.firebaseapp.com',
    databaseURL: 'https://app-do-aluno-unificado.firebaseio.com',
    storageBucket: 'app-do-aluno-unificado.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk',
    appId: '1:977449544580:android:81cf641657abe8d0',
    messagingSenderId: '977449544580',
    projectId: 'app-do-aluno-unificado',
    databaseURL: 'https://app-do-aluno-unificado.firebaseio.com',
    storageBucket: 'app-do-aluno-unificado.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBo0Yk-f7pU1QLLhbS7IEL2Eym-vQDNmjo',
    appId: '1:977449544580:ios:b900830bce5b3cb4',
    messagingSenderId: '977449544580',
    projectId: 'app-do-aluno-unificado',
    databaseURL: 'https://app-do-aluno-unificado.firebaseio.com',
    storageBucket: 'app-do-aluno-unificado.appspot.com',
    androidClientId: '977449544580-2h91nfl5oiumupmkcje1j6bnmdcgrpqc.apps.googleusercontent.com',
    iosClientId: '977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com',
    iosBundleId: 'br.com.pactosolucoes.treino.ios',
  );
}
