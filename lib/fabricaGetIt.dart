// import 'package:admob_flutter/admob_flutter.dart';
import 'package:android_id/android_id.dart';
import 'package:app_treino/ServiceProvider/ConversaAiServices.dart';
import 'package:app_treino/ServiceProvider/NiveisAlunoServico.dart';
import 'package:app_treino/ServiceProvider/n2bservices.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorAppLoading.dart';
import 'package:app_treino/controlladores/ControladorContrato.dart';
import 'package:app_treino/controlladores/ControladorConversaAI.dart';
import 'package:app_treino/controlladores/ControladorHealthKit.dart';
import 'package:app_treino/controlladores/ControladorNovoLogin.dart';
import 'package:app_treino/controlladores/ControladorPushIa.dart';
import 'package:app_treino/util/util_nav_history.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:io' show Platform;
import 'package:app_treino/ServiceProvider/AgendamentoService.dart';
import 'package:app_treino/ServiceProvider/AulaTurmaService.dart';
import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/ServiceProvider/NutricaoService.dart';
import 'package:app_treino/ServiceProvider/PlannerService.dart';
import 'package:app_treino/ServiceProvider/PrescricaoTreinoService.dart';
import 'package:app_treino/ServiceProvider/ServiceVendaDePlano.dart';
import 'package:app_treino/ServiceProvider/UsuarioAppService.dart';
import 'package:app_treino/ServiceProvider/WodService.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAvaliacaoFisica.dart';
import 'package:app_treino/ServiceProvider/authServices/PersonalRecordService.dart';
import 'package:app_treino/controlladores/ControladorAcessoCatraca.dart';
import 'package:app_treino/controlladores/ControladorConfiguracao.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoIA.dart';
import 'package:app_treino/controlladores/ControladorUsuarioApp.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorCronometro.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorCronometroPadrao.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorEmom.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorTabata.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:ds_pacto/ds_pacto.dart';
// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/controlladores/ControladorAvaliarProfessor.dart';
import 'package:app_treino/controlladores/ControladorBeberAgua.dart';
import 'package:app_treino/controlladores/ControladorChat.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/ControladorGraduacao.dart';
import 'package:app_treino/controlladores/ControladorManterWod.dart';
import 'package:app_treino/controlladores/ControladorNotificacoesCrm.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/controlladores/ControladorTime.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/inapp/controladorJPinApp.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:app_treino/ServiceProvider/ServiceDioProvider.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/controlladores/ControladorFirebase.dart';
import 'package:app_treino/controlladores/ControladorNosql.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/ServiceProvider/TreinoSimples.service.dart';
import 'package:app_treino/ServiceProvider/VendasOnlineService.dart';
import 'package:app_treino/ServiceProvider/authServices/FeedService.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorDashBoardPersonal.dart';
import 'package:app_treino/controlladores/ControladorEdicaoiExercicio.dart';
import 'package:app_treino/controlladores/ControladorEventBus.dart';
import 'package:app_treino/controlladores/ControladorFeed.dart';
import 'package:app_treino/controlladores/ControladorIndicacao.dart';
import 'package:app_treino/controlladores/ControladorManterAluno.dart';
import 'package:app_treino/controlladores/ControladorManterExercicio.dart';
import 'package:app_treino/controlladores/ControladorManterPerfil.dart';
import 'package:app_treino/controlladores/ControladorNPS.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorObservacoesAluno.dart';
import 'package:app_treino/controlladores/ControladorPesquisaDePais.dart';
import 'package:app_treino/controlladores/ControladorPlanoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorTreinoAoVivo.dart';
import 'package:app_treino/controlladores/ControladorTreinosPreDefinidos.dart';
import 'package:app_treino/controlladores/ControladorValidadorYouTube.dart';
import 'package:app_treino/controlladores/ControladorVendasOnline.dart';
import 'package:app_treino/controlladores/ControladorTreinoSimples.dart';

bool enviouReport = false;
String thedeviceID = '';
bool modoHomologacao = false;
late Dio dioConnect;
UserActivityTracker activityTracker = UserActivityTracker();
initDepedencies(GetIt getIt, Function(FlutterLocalNotificationsPlugin plugin) localNotificationCenter) async {
  await ControladorFirebase().initFirebase();
  await ControladorFirebase().fetchRemoteConfig();
// Inciando o banco de dados
  tz.initializeTimeZones();
  getIt.registerSingleton<ControladorNoSql>(ControladorNoSql());

  await getIt.get<ControladorNoSql>().initDatabase();
  var db = await SharedPreferences.getInstance();
  try {
//     FirebaseDynamicLinksPlatform.instance.getInitialLink().then((data) async {
//     if (data != null) {
//       String uri = data.link.toString();
//       if (uri.contains('vitio')) {
//         var url = 'https://checkout.vitio.com.br/product-offer/${uri.split('vitio_')[1]}';
//         db.setString('URL_VITIO', url);
//       }
//     }
//   });
  } catch (e) {
  }
  DeviceInfoPlugin().deviceInfo.then((info) async {
    if (Platform.isAndroid) {
      thedeviceID = await const AndroidId().getId() ?? 'Unknown';
    } else {
      thedeviceID = info.data['identifierForVendor'];
    }
    return const FlutterSecureStorage().containsKey(key: 'identifierForVendor');
  }).then((value) async {
    if (!value) {
      await const FlutterSecureStorage().write(key: 'identifierForVendor', value: thedeviceID);
      return null;
    } else {
      return await const FlutterSecureStorage().read(key: 'identifierForVendor');
    }
  }).then((value) {
    thedeviceID = value ?? thedeviceID;
  });
  modoHomologacao = db.getBool('showHtpLog') ?? false;
  dioConnect = ServiceDioProvider().getDio();
  getIt.registerLazySingleton(() => NavigationService());
  getIt.registerSingleton<ClienteAppService>(ClienteAppService(dioConnect));
  getIt.registerSingleton<FeedService>(FeedService(dioConnect));
  getIt.registerSingleton<ServiceAuth>(ServiceAuth(dioConnect));
  getIt.registerSingleton<TreinoService>(TreinoService(dioConnect));
  getIt.registerSingleton<PersonalService>(PersonalService(dioConnect));
  getIt.registerSingleton<AulaTurmaService>(AulaTurmaService(dioConnect));
  getIt.registerSingleton<VendasOnlineService>(VendasOnlineService(dioConnect));
  getIt.registerSingleton<WodService>(WodService(dioConnect));
  getIt.registerSingleton<ServiceVendaDePlano>(ServiceVendaDePlano(dioConnect));
  getIt.registerSingleton<AgendamentoService>(AgendamentoService(dioConnect));
  getIt.registerSingleton<ContratoUsuarioService>(ContratoUsuarioService(dioConnect));
  getIt.registerSingleton<PlannerService>(PlannerService(dioConnect));
  getIt.registerSingleton<ClienteAvaliacaoFisicaService>(ClienteAvaliacaoFisicaService(dioConnect));

  getIt.registerSingleton<ControladorNotificacoes>(ControladorNotificacoes());

  getIt.registerSingleton<ControladorApp>(ControladorApp());
  modoHomologacao = db.getBool('showHtpLog') ?? false;
  getIt.registerSingleton<ControladorSplash>(ControladorSplash());
  getIt.registerSingleton<ControladorCliente>(ControladorCliente());
  getIt.registerSingleton<ControladorCronometro>(ControladorCronometro());
  getIt.registerSingleton<ControladorCronometroPadrao>(ControladorCronometroPadrao());
  getIt.registerSingleton<ControladorEmom>(ControladorEmom());
  getIt.registerSingleton<ControladorTabata>(ControladorTabata());
  getIt.registerSingleton<ControladorPesquiaDePais>(ControladorPesquiaDePais());
  getIt.registerSingleton<ControladorVendasOnline>(ControladorVendasOnline());
  getIt.registerSingleton<ControladorFeed>(ControladorFeed());
  getIt.registerSingleton<ControladorTreinoAluno>(ControladorTreinoAluno());
  getIt.registerSingleton<ControladorExecucaoTreino>(ControladorExecucaoTreino());
  getIt.registerSingleton<ControladorEventBus>(ControladorEventBus());
  //------------------------------
  getIt.registerSingleton<TreinoSimplesService>(TreinoSimplesService(dioConnect));
  getIt.registerSingleton<NutricaoService>(NutricaoService(dioConnect));
  getIt.registerSingleton<UsuarioAppService>(UsuarioAppService(dioConnect));
  getIt.registerSingleton<PrescricaoTreinoService>(PrescricaoTreinoService(dioConnect));
  getIt.registerSingleton<ControladorPrescricaoDeTreino>(ControladorPrescricaoDeTreino());
  getIt.registerSingleton<ControladorPrescricaoIA>(ControladorPrescricaoIA());
  getIt.registerSingleton<ControladorTreinoSimples>(ControladorTreinoSimples());
  getIt.registerSingleton<ControladorTreinoAoVivo>(ControladorTreinoAoVivo());
  getIt.registerSingleton<ControladorValidadorYouTube>(ControladorValidadorYouTube());
  getIt.registerSingleton<ControladorInicacao>(ControladorInicacao());
  getIt.registerSingleton<ControladorDashBoardPersonal>(ControladorDashBoardPersonal());
  getIt.registerSingleton<ControladorManterAluno>(ControladorManterAluno());
  getIt.registerSingleton<ControladorNPS>(ControladorNPS());
  getIt.registerSingleton<ControladorObservacoesAluno>(ControladorObservacoesAluno());
  getIt.registerSingleton<ControladorTreinosPreDefinidos>(ControladorTreinosPreDefinidos());
  getIt.registerSingleton<ControladorManterPerfil>(ControladorManterPerfil());
  getIt.registerSingleton<ControladorEdicaoiExercicio>(ControladorEdicaoiExercicio());
  getIt.registerLazySingleton<ControladorPlanoDeTreino>(() => ControladorPlanoDeTreino());
  getIt.registerLazySingleton<ControladorPushIA>(() => ControladorPushIA());
  getIt.registerLazySingleton<ControladorManterExercicio>(() => ControladorManterExercicio());
  getIt.registerLazySingleton<ControladorAulaTurma>(() => ControladorAulaTurma());
  getIt.registerLazySingleton<ControladorWod>(() => ControladorWod());
  getIt.registerLazySingleton<ControladorConfiguracao>(() => ControladorConfiguracao());
  getIt.registerLazySingleton<ControladorManterWod>(() => ControladorManterWod());
  getIt.registerLazySingleton<ControladorAgendamento>(() => ControladorAgendamento());
  getIt.registerLazySingleton<ControladorAvaliarProfessor>(() => ControladorAvaliarProfessor());
  getIt.registerLazySingleton<ControladorAvaliacaoFisica>(() => ControladorAvaliacaoFisica());
  getIt.registerLazySingleton<ControladorContratoUsuario>(() => ControladorContratoUsuario());
  getIt.registerLazySingleton<ControladorBeberAgua>(() => ControladorBeberAgua());
  getIt.registerLazySingleton<ControladorChat>(() => ControladorChat());
  getIt.registerLazySingleton<ControladorPlanner>(() => ControladorPlanner());
  getIt.registerSingleton<ControladorNotificacoesCrm>(ControladorNotificacoesCrm());
  getIt.registerLazySingleton(() => ControladorGraduacao());
  getIt.registerLazySingleton(() => ControladorVendaDePlano());
  getIt.registerLazySingleton(() => ControladorTime());
  getIt.registerLazySingleton(() => ControladorJPinApp());
  getIt.registerSingleton<ControladorFirebase>(ControladorFirebase());
  getIt.registerSingleton<PersonalRecordService>(PersonalRecordService(dioConnect));

  getIt.registerLazySingleton<ControladorNutricao>(() => ControladorNutricao());
  getIt.registerLazySingleton<ControladorUsuarioApp>(() => ControladorUsuarioApp());
  getIt.registerLazySingleton<ControladorHealthKit>(() => ControladorHealthKit());
  getIt.registerLazySingleton<ControladorPrescricaoTreino>(() => ControladorPrescricaoTreino());
  getIt.registerLazySingleton<ControladorAcessoCatraca>(() => ControladorAcessoCatraca());
  getIt.registerLazySingleton<ControladorNovoLogin>(() => ControladorNovoLogin());
  getIt.registerLazySingleton<ControladorAppLoading>(() => ControladorAppLoading());
  getIt.registerLazySingleton<Niveisalunoservico>(() => Niveisalunoservico(dioConnect));
  getIt.registerLazySingleton<N2BService>(() => N2BService(dioConnect));
GetIt.I.registerLazySingleton<ControladorContrato>(() => ControladorContrato());
  getIt.registerSingleton<ServicesConversaAI>(ServicesConversaAI(dioConnect));
  getIt.registerSingleton<ControladorConversaAI>(ControladorConversaAI());
  FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  if (!false) {
    Function? originalOnError = FlutterError.onError;
    ErrorWidget.builder = (FlutterErrorDetails details) {
      return DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        categoria: CategoriaCard.primario,
        corPrimaria: Colors.transparent,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
                height: 220,
                child: DSsvg(
                  imagem: Imagem.coracaoPartido,
                )),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                DStextSubheadline('Opa, não era pra isso acontecer!'),
                DStextBody(
                  'Logo tudo volta ao normal, vamos consertar tudo o mais rápido possível!',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            DSbotaoPadrao(
              onTap: () {
                if (!enviouReport) {
                  createAlbum(details);
                  enviouReport = true;
                }
              },
              titulo: 'Reportar problema',
            )
          ],
        ),
      );
    };
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    FlutterError.onError = (FlutterErrorDetails errorDetails) async {
      FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
      try {
        FirebaseCrashlytics.instance.recordError(errorDetails.exception, errorDetails.stack);
      } catch (e) {}

      originalOnError!(errorDetails);
    };
  }

  localNotificationCenter.call(FlutterLocalNotificationsPlugin());
}

void createAlbum(FlutterErrorDetails error) async {
  if (kDebugMode || kProfileMode) {
    // Em modo de debug não envia o report, vamos evitar de enviar report de erros em modo de debug
    return;
  }
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  String screenshotBase64 = base64Encode((await GetIt.I.get<ControladorApp>().mainScreenShotController.capture(pixelRatio: 0.8))!.toList());
  UtilitarioApp().showDialogCarregando(GetIt.I.get<NavigationService>().context);
  AndroidDeviceInfo? androidInfo;
  IosDeviceInfo? iosInfo;
  if (Platform.isAndroid) {
    androidInfo = await deviceInfo.androidInfo;
  } else {
    iosInfo = await deviceInfo.iosInfo;
  }
  var report = {
    'chave': GetIt.I.get<ControladorApp>().chave,
    'codigoEmpresa': GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa,
    'username': GetIt.I.get<ControladorCliente>().mUsuarioLogado?.username,
    'plataforma': Platform.operatingSystem,
    'infoDevice': Platform.isAndroid ? androidInfo?.data : iosInfo?.data,
    'versaoApp': packageInfo.version,
    'nomeDoApp': F.nomeApp,
    'versaoSO': Platform.operatingSystemVersion,
    'tipoDeUsuario': GetIt.I.get<ControladorCliente>().isUsuarioColaborador ? 'Colaborador' : 'Aluno',
    'flutterErrorDetails': {
      'screenShot': screenshotBase64,
      'erro': error.exceptionAsString(),
      'tipo': error.toStringShort(),
      'exceptionAsString': error.exceptionAsString(),
      'stack': error.stack.toString().split('#6')[0],
      'historicoDeNavegacao': activityTracker.getFormattedActivityHistory(),
    }
  };
  http.post(Uri.parse('https://app-do-aluno-unificado.web.app/bugreport/reportError'), body: jsonEncode(report), headers: {'Content-Type': 'application/json'}).then((value) {
    Navigator.pop(GetIt.I.get<NavigationService>().context);
    Fluttertoast.showToast(msg: 'Report enviado com sucesso!');
  }).catchError((e) {
    Navigator.pop(GetIt.I.get<NavigationService>().context);
    Fluttertoast.showToast(msg: 'Erro ao enviar o report!');
  });
}


