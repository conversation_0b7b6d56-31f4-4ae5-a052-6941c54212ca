import 'package:app_treino/custom_theme.dart';
import 'package:ds_pacto/ds_fonts.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';

class AlertaFormularioValueListanable extends StatelessWidget {
  const AlertaFormularioValueListanable({Key? key, required this.visibleNotifier, this.padding, required this.textoAlerta}) : super(key: key);
  final ValueNotifier<bool> visibleNotifier;
  final String textoAlerta;
  final EdgeInsets? padding;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: visibleNotifier,
        builder: (context, _, __) {
          return AlertaFormulario(mostrarAlerta: visibleNotifier.value, padding: padding, textoAlerta: textoAlerta);
        });
  }
}

class AlertaFormulario extends StatelessWidget {
  const AlertaFormulario({
    Key? key,
    required this.mostrarAlerta,
    required this.padding,
    required this.textoAlerta,
  }) : super(key: key);

  final bool mostrarAlerta;
  final EdgeInsets? padding;
  final String textoAlerta;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: mostrarAlerta,
      maintainAnimation: true,
      maintainState: true,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(0),
        child: AnimatedScale(
          scale: mostrarAlerta? 1.0 : 0.75,
          duration: const Duration(milliseconds: 125),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              height: 32,
              color: CustomTheme.secundaryColor,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    TreinoIcon.exclamation_triangle,
                    color: Color(0xffED0000),
                    size: 14,
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  DStextCaption1(textoAlerta),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
