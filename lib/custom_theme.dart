import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';

class CustomTheme {
  static Color get primaryColor => DSLib.theme == ThemeMode.dark ? const Color(0xffffffff) : const Color(0xff3F3F3F);
  static Color get secundaryColor => DSLib.theme == ThemeMode.dark ? const Color(0xff000000) : const Color(0xffF0F0F0);
  static Color get separatorsColor => DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xffDCDDDF);
}
