import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

/// Trata erros de autorização.
///
/// Tenta regenerar o token de autenticação para colaboradores ou usuários,
/// retransmitindo o erro para o interceptor após o processo.
class UrlDioInterceptor extends Interceptor {
  final Dio _dio;

  final Response<dynamic> Function(Response origina, Function(Response done) resonseDone) responseHandler;
  final Function(DioException e, ErrorInterceptorHandler handler) rejectHandler;
  ControladorApp get _controladorApp => GetIt.I.get<ControladorApp>();
  UrlDioInterceptor(this._dio, {required this.responseHandler, required this.rejectHandler});

  @override
  Future onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (response.data != null && response.data.toString().toLowerCase().contains('Parâmetros não definidos para chave: ${_controladorApp.chave}'.toLowerCase())) {
      try {
        String? baseUrlKey = await ConfigURL.getKeyByUrl(response.requestOptions.baseUrl);
        await _controladorApp.consultarUrlTreino();
        final requestOptions = response.requestOptions;
        requestOptions.baseUrl = (await ConfigURL().url(baseUrlKey!))!;
        final retryResponse = await _dio.fetch(requestOptions);
        return responseHandler(retryResponse, handler.resolve);
      } catch (e) {}
    } else {
      handler.next(response);
    }
  }
}
