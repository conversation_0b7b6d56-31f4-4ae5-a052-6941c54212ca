// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'NutricaoService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _NutricaoService implements NutricaoService {
  _NutricaoService(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<List<PesoUsuario>> consultarPesoUsuario(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PesoUsuario>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarHistoricoPeso',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PesoUsuario.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<PesoUsuario>> atualizarPesoUsuario(
    usuarioApp,
    peso,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'peso': peso,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PesoUsuario>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/atualizarPeso',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PesoUsuario.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> configurarPlanoNutricional(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/configurarPlanoNutricional',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PlanoNutricional> consultarPlanoNutricional(
    usuarioApp,
    dataConsulta,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataConsulta': dataConsulta,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoNutricional>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarPlanoNutricional',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoNutricional.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<HistoricoHidratacao>> atualizarMlConsumido(
    usuarioApp,
    ml,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'ml': ml,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoHidratacao>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/atualizarMlConsumido',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoHidratacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<HistoricoHidratacao>> removerMlConsumido(
    usuarioApp,
    ml,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'ml': ml,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoHidratacao>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/removerMlConsumido',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoHidratacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<HistoricoHidratacao>> consultarMlConsumidoPorData(
    usuarioApp,
    dataInicio,
    dataFim,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataInicio': dataInicio,
      r'dataFim': dataFim,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoHidratacao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarMlConsumidoPorData',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoHidratacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<Refeicoes>> consultarRefeicoes() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<Refeicoes>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarRefeicoes?tipoConsulta={APPTIPOCONSULTA}&chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Refeicoes.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicoesDoPlano>> gerarReceitasPlanoNutricional(
      usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicoesDoPlano>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/gerarReceitasPlanoNutricional?tipoConsulta={APPTIPOCONSULTA}&chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => RefeicoesDoPlano.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<PlanoNutricional> atualizarDataInicioTerminoPlano(
    usuarioApp,
    dataInicio, {
    duracao,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataInicio': dataInicio,
      r'duracao': duracao,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoNutricional>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/atualizarDataInicioTerminoPlano',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoNutricional.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ItemListaCompras>> consultarListaCompras(
    usuarioApp,
    dataConsulta,
    dataFim,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataInicio': dataConsulta,
      r'dataFim': dataFim,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ItemListaCompras>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarListaCompras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ItemListaCompras.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> consumirRefeicao(
    usuarioApp,
    dataRefeicao,
    consumido,
    id,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataRefeicao': dataRefeicao,
      r'consumido': consumido,
      r'id': id,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/consumirRefeicao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<RefeicoesDoPlano>> calendarioNutricao(
    usuarioApp,
    dataConsulta,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataConsulta': dataConsulta,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicoesDoPlano>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/calendarioNutricao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => RefeicoesDoPlano.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicoesDoPlano>> consultarReceitasGeradas(
    usuarioApp, {
    dataConsulta,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataConsulta': dataConsulta,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicoesDoPlano>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarReceitasGeradas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => RefeicoesDoPlano.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<Refeicoes>> consultarReceitasParaSubstituicao(map) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(map);
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<Refeicoes>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarReceitasParaSubstituicao?tipoConsulta={APPTIPOCONSULTA}&chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Refeicoes.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> substituirReceita(map) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(map);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/substituirReceita',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PlanoNutricional> confirmarReceitasGeradas(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoNutricional>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/confirmarReceitasGeradas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoNutricional.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> atualizarHorarioRefeicoes(
    usuarioApp,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/atualizarHorarioRefeicoes',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<Ingredientes>> consultarIngredientes() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<Ingredientes>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarIngredientes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Ingredientes.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> consumirRefeicaoAvulsa(
    usuarioApp,
    consumido,
    id,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'consumido': consumido,
      r'id': id,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/consumirRefeicaoAvulsa',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> adicionarLegendaRefeicaoAvulsa(
    usuarioApp,
    id,
    legenda,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'id': id,
      r'legenda': legenda,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/adicionarLegendaRefeicaoAvulsa',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> salvarFotoRefeicao(
    usuarioApp,
    consumido,
    id,
    fotoBase64,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'consumido': consumido,
      r'id': id,
    };
    final _headers = <String, dynamic>{r'Content-Type': 'text/plain'};
    _headers.removeWhere((k, v) => v == null);
    final _data = fotoBase64;
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'text/plain',
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/salvarFotoRefeicao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<ReceitaFavorita>> favoritarRefeicao(
    usuarioApp,
    ref,
    favorita,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'ref': ref,
      r'favorita': favorita,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ReceitaFavorita>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/favoritarRefeicao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ReceitaFavorita.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ReceitaFavorita>> consultarReceitasFavoritas(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ReceitaFavorita>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarReceitasFavoritas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ReceitaFavorita.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ItemListaCompras>> consultarListaComprasGerada(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ItemListaCompras>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarListaComprasGerada',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ItemListaCompras.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ItemListaCompras>> editarItemListaCompras(
    usuarioApp,
    ref,
    comprado,
    nome,
    quantidade,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'ref': ref,
      r'comprado': comprado,
      r'nome': nome,
      r'quantidade': quantidade,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ItemListaCompras>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/editarItemListaCompras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ItemListaCompras.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ItemListaCompras>> gravarListaCompras(
    usuarioApp,
    dataInicioListaCompras,
    dataFimListaCompras,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'dataInicioListaCompras': dataInicioListaCompras,
      r'dataFimListaCompras': dataFimListaCompras,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ItemListaCompras>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/gravarListaCompras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ItemListaCompras.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<DicasNutri>> consultarDicas(
    limit,
    usuarioMovel,
    tipo,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'limit': limit,
      r'usuarioMovel': usuarioMovel,
      r'tipo': tipo,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<DicasNutri>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{UCP}/prest/dicasNutri/{chave}/dicas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => DicasNutri.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<HistoricoRespiracao>> consultarHistoricoRespiracao(
      usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoRespiracao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarHistoricoRespiracao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoRespiracao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<HistoricoRespiracao>> atualizarRespiracao(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoRespiracao>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/atualizarRespiracao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoRespiracao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<PlanoNutricao>> consultarPlanosNutricionais() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PlanoNutricao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarPlanosNutricionais?tipoConsulta={APPTIPOCONSULTA}&chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PlanoNutricao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<PlanoNutricao>> consultarPlanosNutricionaisSemCache() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PlanoNutricao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarPlanosNutricionaisSemCache?tipoConsulta={APPTIPOCONSULTA}&chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PlanoNutricao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<Novidades>> consultarNovidades() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<Novidades>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarNovidades',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Novidades.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<RetornoConclusao> consultarProgressoTotalPlano(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<RetornoConclusao>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarProgressoTotalPlano',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RetornoConclusao.fromJson(_result.data!);
    return value;
  }

  @override
  Future<PlanoNutricional> substituirPlanoNutricional(
    usuarioApp,
    refNovoPlano,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'refNovoPlano': refNovoPlano,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoNutricional>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/substituirPlanoNutricional',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoNutricional.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AlimentoCriado>> criarAlimento(
    usuarioApp,
    gordura,
    carboidratos,
    proteinas,
    nome,
    calorias,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'gordura': gordura,
      r'carboidratos': carboidratos,
      r'proteinas': proteinas,
      r'nome': nome,
      r'calorias': calorias,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlimentoCriado>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/criarAlimento',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AlimentoCriado.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlimentoCriado>> consultarAlimentosCriados(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlimentoCriado>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/consultarAlimentosCriados',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AlimentoCriado.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> atualizarMetaAguaETamanhoCopo(
    usuarioApp,
    tamanhoCopo,
    metaHidratacao,
    lembrete,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'tamanhoCopo': tamanhoCopo,
      r'metaHidratacao': metaHidratacao,
      r'lembrete': lembrete,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/0',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PlanoNutricional> atualizarObjetivo(
    usuarioApp,
    objetivo,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'objetivo': objetivo,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoNutricional>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/nutri/atualizarObjetivo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoNutricional.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> atualizarMetaDePeso(
    usuarioApp,
    metaDePeso,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'metaDePeso': metaDePeso,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/atualizarMetaDePeso',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> atualizarLembretes(
    usuarioApp,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/atualizarLembretes',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> resetarPlanoNutricional(usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/nutri/resetarPlanoNutricional',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> enviarDadosMinhaAcademia(
    chave,
    nome,
    dispositivo,
    usuarioMovel,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chave': chave,
      r'nome': nome,
      r'dispositivo': dispositivo,
      r'usuarioMovel': usuarioMovel,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/aluno/enviarDadosMinhaAcademia',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<PlanosNutricionaisFitStream>> consultarProgramasFitStream(
      chave) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'chave': chave};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<PlanosNutricionaisFitStream>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/planoAlimentar/listar',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            PlanosNutricionaisFitStream.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<PlanosNutricionaisFitStream> inserirProgramasFitStream(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<PlanosNutricionaisFitStream>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/planoAlimentar/inserir',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanosNutricionaisFitStream.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> removerProgramaFitStream(ref) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/planoAlimentar/deletar/${ref}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PlanosNutricionaisFitStream> consularDetalhesProgramaFitStream(
    ref,
    user,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<PlanosNutricionaisFitStream>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/planoAlimentar/detalhes/${ref}/${user}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanosNutricionaisFitStream.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Content> marcarProgramaFitStreamComoConcluidoELike(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Content>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/planoAlimentar/contents/concluidoLike',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Content.fromJson(_result.data!);
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
