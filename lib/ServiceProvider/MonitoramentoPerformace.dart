import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';

class MonitoramentoPerformace extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    
    // Converter o método HTTP do Dio para HttpMethod
    HttpMethod httpMethod = _parseHttpMethod(options.method);

    // Inicia o monitoramento da requisição com o método correto
    HttpMetric metric = FirebasePerformance.instance.newHttpMetric(
      options.uri.toString(),
      httpMethod,
    );
    await metric.start();

    // Armazena a métrica no extra para acessar mais tarde
    options.extra['firebase_metric'] = metric;

    // Continue com a requisição
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Recupera a métrica da requisição e marca como sucesso
    HttpMetric? metric = response.requestOptions.extra['firebase_metric'];
    if (metric != null) {
      metric
        ..responseContentType = response.headers.value('content-type')
        ..httpResponseCode = response.statusCode;
      await metric.stop();
    }

    // Continue com a resposta
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Recupera a métrica da requisição e marca como erro
    HttpMetric? metric = err.requestOptions.extra['firebase_metric'];
    if (metric != null) {
      metric
        ..responseContentType = err.response?.headers.value('content-type')
        ..httpResponseCode = err.response?.statusCode;
      await metric.stop();
    }

    // Continue com o erro
    return handler.next(err);
  }

  // Função auxiliar para converter o método HTTP da string para o tipo HttpMethod
  HttpMethod _parseHttpMethod(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return HttpMethod.Get;
      case 'POST':
        return HttpMethod.Post;
      case 'PUT':
        return HttpMethod.Put;
      case 'DELETE':
        return HttpMethod.Delete;
      case 'PATCH':
        return HttpMethod.Patch;
      case 'HEAD':
        return HttpMethod.Head;
      case 'OPTIONS':
        return HttpMethod.Options;
      default:
        return HttpMethod.Get; // Padrão para GET caso o método não seja reconhecido
    }
  }
}
