// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ServiceVendaDePlano.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _ServiceVendaDePlano implements ServiceVendaDePlano {
  _ServiceVendaDePlano(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<List<UnidadeVendaDePlano>> obterUnidadesParaChave() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<UnidadeVendaDePlano>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/unidades/{chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            UnidadeVendaDePlano.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<PlanoVendidoUnidade>> obterDadosDaUnidadePlanos(unidade) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PlanoVendidoUnidade>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/unidade/${unidade}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            PlanoVendidoUnidade.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<PlanoVendidoUnidade>> obterTodosPlanosDaUnidade(unidade) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PlanoVendidoUnidade>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/planos/${unidade}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            PlanoVendidoUnidade.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<ConfiguracaoVendasUnidade> obterConfiguracaoVendasOnline(
      unidade) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ConfiguracaoVendasUnidade>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/configs/${unidade}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ConfiguracaoVendasUnidade.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ConfigLinkPagamento> obterConfiguracaoLinkPagamento(codEmpresa) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'codEmpresa': codEmpresa};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ConfigLinkPagamento>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/empresa/{chave}/convenioPagamento',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ConfigLinkPagamento.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> gerarTokenVenda(venda) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(venda.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/tkn/{tokenApp}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<DadosDoUsuario> consultarAlunoViaEmailOuCPF(
    cpf,
    email,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cpf': cpf,
      r'email': email,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<DadosDoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarClienteJson',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DadosDoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> cadastrarUmaNovaVenda(
    planoInserir,
    token,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(planoInserir.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/alunovendaapp/${token}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<ContratoUsuario> simularVenda(
    planoInserir,
    plano,
    unidade,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(planoInserir.toJson());
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ContratoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/simular/${plano}/${unidade}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ContratoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ContratoUsuario> renovarContrato(
    contrato,
    simulacao,
    cliente,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'contrato': contrato,
      r'simulacao': simulacao,
      r'cliente': cliente,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ContratoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/negociacao/{chave}/renovarContrato',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ContratoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> obterInformacaoContratual(
    codPlano,
    codContrato,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/plano/${codPlano}/contrato/${codContrato}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<ClienteParcelas> consultarClienteParcelas(matricula) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ClienteParcelas>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/aluno/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ClienteParcelas.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> cobrarParcelas(
    matricula,
    cobrarParcelas,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(cobrarParcelas.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/cobrarparcelasabertas/${matricula}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> validarCupomDesconto(
    numeroCupomDesconto,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'numeroCupomDesconto': numeroCupomDesconto
    };
    final _headers = <String, dynamic>{};
    final _data = body;
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/adicionarCupomDescontoSite',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<Pix> cobrarParcelasComPix(
    matricula,
    cobrarParcelas,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(cobrarParcelas.toJson());
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Pix>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/cobrarParcelasAbertasPix/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Pix.fromJson(_result.data!);
    return value;
  }

  @override
  Future<StatusParcelaPix> consultarStatusParcelaPix(codigoPix) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<StatusParcelaPix>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/consultarParcelasPix/${codigoPix}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = StatusParcelaPix.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Pix> cobrarParcelasSelecionadasComPix(
    matricula,
    unidade,
    origemCobranca,
    parcelasSelecionadas,
    enviarEmail,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'unidade': unidade,
      r'origemCobranca': origemCobranca,
      r'parcelasSelecionadas': parcelasSelecionadas,
      r'enviarEmail': enviarEmail,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Pix>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/v2/vendas/{chave}/v2/cobrarParcelasAbertasPix/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Pix.fromJson(_result.data!);
    return value;
  }

  @override
  Future<dynamic> cobrarParcelasSelecionadasComCartao(
    matricula,
    cobrarParcelas,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(cobrarParcelas.toJson());
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/cobrarparcelasabertas/${matricula}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> cobrarParcelasSelecionadasComBoleto(
    matricula,
    cobrarParcelas,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(cobrarParcelas.toJson());
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/v2/vendas/{chave}/cobrarParcelasAbertasBoleto/${matricula}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
