import 'package:app_treino/config/ConfigURL.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
part 'ConversaAiServices.g.dart'; // Gera o arquivo de geração do retrofit

@RestApi(baseUrl: '')
abstract class ServicesConversaAI {
  factory ServicesConversaAI(Dio dio, {String? baseUrl}) = _ServicesConversaAI;

  @POST('${ConfigURL.CONTATOMSURL}/v1/ia/conversa/fase-ai')
  Future<dynamic> iniciarFaseAI({ @Query('empresa') required num empresa, @Query('fase') required String fase, @Query('cliente') required num cliente });
}