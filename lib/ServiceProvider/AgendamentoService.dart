import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/agenda/Agendamento.dart';
import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

part 'AgendamentoService.g.dart';

@RestApi(baseUrl: '')
abstract class AgendamentoService {
  factory AgendamentoService(Dio dio, {String? baseUrl}) = _AgendamentoService;

  @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/todos')
  Future<List<Agendamento>> consultarAgendamentos(@Query('username') String username, [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/cancelar')
  Future<String> cancelarAgendamento(@Query('username') String userName, @Query('idAgendamento') num codigoAgendamento, [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/confirma')
  Future<String> confirmarAgendamento(@Query('username') String userName, @Query('idAgendamento') num codigoAgendamento, [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/submit')
  Future<String> reagendarEvento(@Query('username') String userName, @Query('idAtividade') num codigoAtividade, @Query('dia') String dia, @Query('horario') String horario,
      [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/agendadosAppAluno')
  Future<List<HorarioReagendamento>> consultarReagendamentos(@Query('username') String userName, @Query('idAgendamento') num codigoAgendamento, @Query('diaInicial') String diaInicial,
      [@Query('matricula') String? matricula]);

  @GET('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/disponibilidades')
  Future<Map<String, List<NovoAgendamento>>> consultarAgendamentosPeloDia(@Query('empresaId') num empresaId, @Query('dia') String dia, @Query('periodo') String periodo, @Query('appTreino') bool appTreino, [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/criar-agendamento')
  Future<NovoAgendamento> persistirUmAgendamentoAoAluno(@Body() Map<String, dynamic> body);
}
