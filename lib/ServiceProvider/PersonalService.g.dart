// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PersonalService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _PersonalService implements PersonalService {
  _PersonalService(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<CadastroColaborador> salvarPersonal(
    idAluno,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<CadastroColaborador>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/personal/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CadastroColaborador.fromJson(_result.data!);
    return value;
  }

  @override
  Future<CadastroAluno> salvarAluno(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<CadastroAluno>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/simplificado',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CadastroAluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<CadastroAluno> manterAluno(
    idAluno,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<CadastroAluno>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/simplificado/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CadastroAluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ReportSatisfacao>> resultadoNivelSatisfacao() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ReportSatisfacao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/personal/{chave}/notas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ReportSatisfacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ReportExecucao>> resultadoExecucoes() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ReportExecucao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/personal/{chave}/execucoes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ReportExecucao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<ConfiguracoesBaseFicha> consultarObjetivos() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ConfiguracoesBaseFicha>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/consultarConfiguracaoFichaApp',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ConfiguracoesBaseFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<DashBoardTreinoIdependente> consultarDashBoardFiti(
    codigoProfessor,
    empresa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoProfessor': codigoProfessor,
      r'empresa': empresa,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<DashBoardTreinoIdependente>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/gestao/{chave}/app/consultarDashColaborador',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DashBoardTreinoIdependente.fromJson(_result.data!);
    return value;
  }

  @override
  Future<DashboardNovoTreino> consultarDashboardNovoTreino(
    id,
    idProfessor,
    idPessoa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'idProfessor': idProfessor,
      r'idPessoa': idPessoa,
    };
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<DashboardNovoTreino>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/dash',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DashboardNovoTreino.fromJson(_result.data!);
    return value;
  }

  @override
  Future<DashboardNovoTreino> atualizarDashboardNovoTreino(
    id,
    idProfessor,
    codigoPessoa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'idProfessor': idProfessor,
      r'codigoPessoa': codigoPessoa,
    };
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<DashboardNovoTreino>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/atualizar',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DashboardNovoTreino.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteira>> treinosVencidosPorProfessor(
    cod,
    filters,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'size': size,
    };
    final _headers = <String, dynamic>{
      r'Content-Type': 'application/json',
      r'empresaId': empresaId,
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteira>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/alunos-treino-vencido/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoSimplificadoCarteira.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<dynamic> contabilizarAcompanhar(
    id,
    codigoPessoa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/treino-bi/alunos-sem-acompanhamento/${codigoPessoa}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> finalizarAcompanhar(
    id,
    codigoPessoa,
    programaId,
    fichaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/treino-bi/alunos-com-acompanhamento/${codigoPessoa}/${programaId}/${fichaId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> contabilizarAcompanharV2(
    id,
    codigoCliente,
    codigoProfessor,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/treino-bi/iniciar-acompanhamento-aluno/v2/${codigoCliente}/${codigoProfessor}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> finalizarAcompanharV2(
    id,
    codigoCliente,
    codigoProfessor,
    programaId,
    fichaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/treino-bi/finalizar-acompanhamento-aluno/v2/${codigoCliente}/${codigoProfessor}/${programaId}/${fichaId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteira>> treinosAVencerPorProfessor(
    cod,
    filters,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'size': size,
    };
    final _headers = <String, dynamic>{
      r'Content-Type': 'application/json',
      r'empresaId': empresaId,
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteira>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/alunos-treino-a-renovar/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoSimplificadoCarteira.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteira>> treinosEmDiaPorProfessor(
    cod,
    filters,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'size': size,
    };
    final _headers = <String, dynamic>{
      r'Content-Type': 'application/json',
      r'empresaId': empresaId,
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteira>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/alunos-treino-em-dia/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoSimplificadoCarteira.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteiraAVencer>> contratoAvencer(
    cod,
    filters,
    page,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'page': page,
      r'size': size,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteiraAVencer>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/lista-alunos-vencer-30-dias/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AlunoSimplificadoCarteiraAVencer.fromJson(
            i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteira>> contratoAtivo(
    cod,
    filters,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'size': size,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteira>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/alunos-ativos/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoSimplificadoCarteira.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoSimplificadoCarteira>> contratoInativo(
    cod,
    filters,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'size': size,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<AlunoSimplificadoCarteira>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/treino-bi/alunos-inativos/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoSimplificadoCarteira.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<BiAvaliacao> consultarDashAvaliacao(
    id,
    dataInicio,
    dataFim,
    codigoProfessor,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataInicio': dataInicio,
      r'dataFim': dataFim,
      r'codigoProfessor': codigoProfessor,
    };
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BiAvaliacao>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/avaliacao-fisica-bi',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BiAvaliacao.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AvaliacaoProfessorDash> consultarAvaliacoes(codigoProfessor) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoProfessor': codigoProfessor
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AvaliacaoProfessorDash>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/manter/avaliacaoProfessorApp?chave={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AvaliacaoProfessorDash.fromJson(_result.data!);
    return value;
  }

  @override
  Future<InfoIndicacoesPersonal> consultarInfoIndicacoes() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<InfoIndicacoesPersonal>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{ALCIDDES}/?key={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = InfoIndicacoesPersonal.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AlunoDeProfessor>> consultarAlunos(
    statusAluno,
    codUsuario,
    empresa,
    max, {
    nomePesquisa,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'statusAluno': statusAluno,
      r'codUsuario': codUsuario,
      r'empresa': empresa,
      r'max': max,
      r'porNome': nomePesquisa,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlunoDeProfessor>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/app/v2/consultarAlunosColaborador',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => AlunoDeProfessor.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<ResponseAlunosPaginado> consultarAlunosTreino(
    filters,
    configs,
    page,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'filters': filters,
      r'configs': configs,
      r'page': page,
      r'size': size,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ResponseAlunosPaginado>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ResponseAlunosPaginado.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ContentAlunoAcademia>> consultarAlunosColaborador(
    carteiras,
    empresas,
    filters,
    configs,
    page,
    size,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'carteiras': carteiras,
      r'empresas': empresas,
      r'filters': filters,
      r'configs': configs,
      r'page': page,
      r'size': size,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<ContentAlunoAcademia>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/alunoColaborador',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            ContentAlunoAcademia.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<AvaliacaoFisicaRecente> consultarAvaliacaoFisicaRecente(
    empresaId,
    id,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'empresaId': empresaId};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AvaliacaoFisicaRecente>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/avaliacoes-fisica/alunos/${id}/avaliacao-recente',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AvaliacaoFisicaRecente.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AcessosSemanalAluno> consultarAcessos(
    username,
    codigoAluno,
    dataInicial,
    dataFinal,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'username': username,
      r'codigoAluno': codigoAluno,
      r'dataInicial': dataInicial,
      r'dataFinal': dataFinal,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AcessosSemanalAluno>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/app/consultarQuantidadeAcessosClientesAgrupadosDia',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AcessosSemanalAluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<HistoricoExecucaoAluno>> consultarHistoricoExecucoes(
    codAluno,
    index,
    maxResult,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoCliente': codAluno,
      r'index': index,
      r'maxResults': maxResult,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<HistoricoExecucaoAluno>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/programa/{chave}/app/consultarHistoricoExecs',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            HistoricoExecucaoAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<ObservacaoAlunoColaborador> gravarObservacao(observacao) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(observacao);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ObservacaoAlunoColaborador>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{ADMCORE}/cliente-observacao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ObservacaoAlunoColaborador.fromJson(_result.data!);
    return value;
  }

  @override
  Future<dynamic> removerObservacao(codigoObservacao) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{ADMCORE}/cliente-observacao/${codigoObservacao}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<ProgramaDeTreino> consultarProgramaBase(
    codigo,
    userName,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoCliente': codigo,
      r'userName': userName,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaDeTreino>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/programa/{chave}/app/consultarProgramaBase',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaDeTreino.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> renovarPrograma(
    username,
    empresa,
    codigoPrograma,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'username': username,
      r'empresa': empresa,
      r'codigoPrograma': codigoPrograma,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/programa/{chave}/app/renovarPrograma',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> revisarPrograma(
    empresa,
    username,
    justificativa,
    codigoPrograma,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'empresa': empresa,
      r'username': username,
      r'justificativa': justificativa,
      r'codigoPrograma': codigoPrograma,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/programa/{chave}/app/revisaoPrograma',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<ProgramaDeTreino> consultarUltimoProgramaDoAluno(
    usernameProfessor,
    codigoAluno,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'userName': usernameProfessor,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaDeTreino>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/programa/{chave}/app/consultarUltimoPrograma',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaDeTreino.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ProgramaFicha> manterPrograma(programa) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(programa);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaFicha>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/programa/{chave}/app/persistirPrograma',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ProgramaFicha> consultarDadosDaFicha(
    usernameProfessor,
    codigoFicha,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'userName': usernameProfessor,
      r'codigoFicha': codigoFicha,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaFicha>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/getFicha',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ConfiguracoesBaseFicha> consultarInfoBasePrograma() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ConfiguracoesBaseFicha>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/consultarConfiguracaoFichaApp',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ConfiguracoesBaseFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ProgramaFicha>> consultarFichasPreDefinidas({
    userName,
    max,
    nomeFicha,
    codigoProfessor,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'userName': userName,
      r'max': max,
      r'nomeFicha': nomeFicha,
      r'codigoProfessor': codigoProfessor,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ProgramaFicha>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/consultarFichasPreDefinidas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ProgramaFicha.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> deletarFichaDeUmPrograma(
    username,
    codigoDaFicha,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/ficha/{chave}/app/deletarFicha?username=${username}&codigoDaFicha=${codigoDaFicha}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> deletarTreinoModelo(
    username,
    codigoFicha,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'username': username,
      r'codigoFicha': codigoFicha,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/ficha/{chave}/excluir',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<ProgramaFicha> persistirFicha(
    ficha,
    usernameProfessor,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'username': usernameProfessor};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(ficha);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaFicha>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/persistirFicha',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ProgramaFicha> persistirFichaPreDef(
    ficha,
    usernameProfessor,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'username': usernameProfessor};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(ficha);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProgramaFicha>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/persistirFichaPredefinida',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaFicha.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ProgramaSimplesAluno>> consultarProgramasBaisocosAluno(
    alunos,
    usernameProfessor,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'username': usernameProfessor};
    final _headers = <String, dynamic>{};
    final _data = alunos.map((e) => e.toJson()).toList();
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<ProgramaSimplesAluno>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/ficha/{chave}/app/consultarProgramaDeAlunosAlunos',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            ProgramaSimplesAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AtividadesFicha>> consultarTodasAtividades() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AtividadesFicha>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/atividades/{chave}/app/consultarTodasAtividades?crossfit=false',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AtividadesFicha.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<AvaliacaoNPS> avaliarClienteApp(avaliacao) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(avaliacao);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<AvaliacaoNPS>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/manter/avaliarClienteApp',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AvaliacaoNPS.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<NotificacaoPersonal>> consultarNotificacaoPersonal(
      {dataUltimoItem}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataUltimoItem': dataUltimoItem
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<NotificacaoPersonal>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/personalFit/consultarNotificacoesApp?clienteApp={chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            NotificacaoPersonal.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> marcarNotificacoesComoLidaPersonal() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/personalFit/registrarComoLida?clienteApp={chave}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<AtividadeBase> manterAtividade(atividade) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(atividade);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<AtividadeBase>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/atividades/{chave}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AtividadeBase.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AtividadeBase>> consultarAtividadeBase() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AtividadeBase>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/atividades/{chave}/personal/consultarTodasAtividades',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AtividadeBase.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ImagemBase>> consultarCatalogoDeImagens() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ImagemBase>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/atividades/{chave}/imagens-catalogo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ImagemBase.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<GruposMuscular>> consultarGruposMusculares() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<GruposMuscular>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/atividades/{chave}/grupos-musculares',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => GruposMuscular.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> registrarPushPersonal(push) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(push);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/personalFit/registrarNotificaoPush?clienteApp={chave}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<ProfessorAvaliar>> consultarTodosProfessores(id) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'cliente': id};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ProfessorAvaliar>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/gestao/{chave}/obterTodosProfessores',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ProfessorAvaliar.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<ProfessorNota> avaliarProfessor(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ProfessorNota>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/manter/avaliarProfessor',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProfessorNota.fromJson(_result.data!);
    return value;
  }

  @override
  Future<RegistroFacialSucesso> registrarFace(json) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(json);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<RegistroFacialSucesso>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{MOCK}/facial/gravar',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RegistroFacialSucesso.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> consultarCodAcesso({required matricula}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'matricula': matricula};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/aula/{chave}/consultarCodAcessoAluno',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<ProfessorAvaliar>> consultarColaboradorTreinoWeb(id) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': id};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ProfessorAvaliar>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/colaboradores',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ProfessorAvaliar.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<SituacaoEduzz> situacaoEduzz(user) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'email': user};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<SituacaoEduzz>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://us-central1-personalfit-55234.cloudfunctions.net/usuario/consultarStatusEduzz',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = SituacaoEduzz.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<ObservacaoAluno>> consultarObservacoesAluno(
    codigoMatricula, {
    required page,
    required size,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ObservacaoAluno>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{ADMCORE}/cliente-observacao/by-matricula/${codigoMatricula}?filters={}&configs={}&page=${page}&size=${size}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ObservacaoAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<NivelAlunoGraduacao>> consultarGraduacaoAluno({
    required codigoCliente,
    required chaveZW,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'key': chaveZW};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<NivelAlunoGraduacao>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://graduacao.ms.pactosolucoes.com.br/fichas/${codigoCliente}/fichasaluno',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            NivelAlunoGraduacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AvaliacaoProgresso>> consultarAvaliacaoProgresso({
    required alunoId,
    required fichaId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'alunoId': alunoId,
      r'fichaId': fichaId,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AvaliacaoProgresso>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://graduacao.ms.pactosolucoes.com.br/avaliacoes-progresso',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AvaliacaoProgresso.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<num> consultarQtdeAulasFeitasNoNivel({
    required codigoCliente,
    required matriculaZW,
    required idNivel,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<num>(_setStreamType<num>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          'https://graduacao.ms.pactosolucoes.com.br/niveis/${codigoCliente}/aulas/${matriculaZW}/${idNivel}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<EventoTimeLineAluno>> consultarTimeLineAluno({
    required dataInicio,
    required dataFim,
    required tiposEvento,
    required idAluno,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataInicio': dataInicio,
      r'dataFim': dataFim,
      r'tiposEvento': tiposEvento,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<EventoTimeLineAluno>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/linha-tempo/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            EventoTimeLineAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<EventoTimeLineAluno>> consultarTimeLineAlunoComFiltro({
    required dataInicio,
    required dataFim,
    required tiposEvento,
    required idAluno,
    required empresaId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataInicio': dataInicio,
      r'dataFim': dataFim,
      r'tiposEvento': tiposEvento,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<EventoTimeLineAluno>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/linha-tempo/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            EventoTimeLineAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ExercicioTreinoExtraFirebase>> listarAtividadeExtras(
      usuarioApp) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'refUser': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<ExercicioTreinoExtraFirebase>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/listarAtividadeExtras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            ExercicioTreinoExtraFirebase.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ExercicioTreinoExtraFirebase>> inserirAtividadeExtras(
    usuarioApp,
    body,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'refUser': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<List<dynamic>>(
        _setStreamType<List<ExercicioTreinoExtraFirebase>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/inserirAtividadeExtras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            ExercicioTreinoExtraFirebase.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> deletarAtividadeExtras(
    usuarioApp,
    atividadeId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refUser': usuarioApp,
      r'atividadeId': atividadeId,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/usuario/deletarAtividadeExtra',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<Aluno> consultarAlunoDetalhado({
    required codigoAluno,
    required empresaId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Aluno>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/${codigoAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Aluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Aluno> consultarAlunoDetalhadoMatricula({
    required matricula,
    required empresaId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Aluno>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/obter-aluno-completo-por-matricula/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Aluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ProgramaTreinoPerfilAluno> consultarProgramaTreinoPerfilAluno({
    required idAluno,
    required idEmpresa,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': idEmpresa};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ProgramaTreinoPerfilAluno>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/perfil/programa-treino/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ProgramaTreinoPerfilAluno.fromJson(_result.data!);
    return value;
  }

  @override
  Future<SituacaoCliente> consultarSituacaoProfessor({
    required colaborador,
    required idEmpresa,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': idEmpresa};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<SituacaoCliente>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/colaboradores/${colaborador}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = SituacaoCliente.fromJson(_result.data!);
    return value;
  }

  @override
  Future<dynamic> renovarProgramaDeTreinoAtual({
    required objetoComIdAluno,
    required idEmpresa,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': idEmpresa};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(objetoComIdAluno);
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/programas?renovarAtual=true',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<List<ProfTreinoWeb>> consultarProfessorTreinoWeb({
    required cod,
    required page,
    required size,
    required filters,
    required empresaId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'page': page,
      r'size': size,
      r'filters': filters,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ProfTreinoWeb>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/colaboradores/professores-dados-basicos/${cod}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ProfTreinoWeb.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<CadastroProfessorTreinoWeb> cadastrarProfessorTreinoWeb({
    required idAluno,
    required empresaId,
    required body,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<CadastroProfessorTreinoWeb>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/alunos/${idAluno}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CadastroProfessorTreinoWeb.fromJson(_result.data!);
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
