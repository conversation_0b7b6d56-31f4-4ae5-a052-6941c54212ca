// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:retrofit/http.dart' as retrofit;
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/model/util/DioRetryRequestIptions.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';

import 'package:shared_preferences/shared_preferences.dart';

class _RetryLaterDioSembastCacheStore {
  factory _RetryLaterDioSembastCacheStore() {
    return _singleton;
  }

  _RetryLaterDioSembastCacheStore._internal();

  final lastCleanKey = 'last_clean_timestamp'; // Chave para armazenar o timestamp da última limpeza
  var storeForLater = stringMapStoreFactory.store('cache_retry_later');

  static final _RetryLaterDioSembastCacheStore _singleton = _RetryLaterDioSembastCacheStore._internal();

  Database? _db;

  // Inicializa o banco de dados do Sembast
  Future<void> init() async {
    if (_db == null) {
      final dbPath = await _getDbPath();
      final db = await databaseFactoryIo.openDatabase(dbPath);
      _db = db;
    }
  }

  // Limpa o cache de requests futura
  Future<void> removeFromSubmmitLater(DioRetryRequestOptions options) async {
    final key = options.url.toString();
    await storeForLater.record(key).delete(_db!);
    if (kDebugMode) {
      print('Cache limpo!');
    }
  }

// Limpa todos os registros do storeForLater
  Future<void> clearAllStoreForLater() async {
    await storeForLater.delete(_db!);
    if (kDebugMode) {
      print('Todos os registros do storeForLater foram limpos!');
    }
  }

  Future<void> saveToCallLater(RequestOptions options) async {
    options.headers.remove(SaveToSubmitLater.headers.keys.first);

    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.getKeys().forEach((key) {
      try {
        if ((prefs.get(key) ?? '') == options.baseUrl) {
          options.baseUrl = key;
        }
      } catch (e) {
        if (kDebugMode) {
          print(e);
        }
      }
    });
    final key = options.uri.toString();
    final data = {'curl': DioRetryRequestOptions.parse(options)};
    storeForLater.record(key).put(_db!, data);
  }

// Obtém todos os registros de storeForLater e retorna uma lista de DioParsedRequestOptions
  Future<List<DioRetryRequestOptions>> getAllStoredForLater() async {
    final records = await storeForLater.find(_db!);
    return records.map((record) {
      final data = record.value as Map<String, dynamic>;
      return DioRetryRequestOptions.fromMap(json.decode(json.encode(data))['curl']);
    }).toList();
  }

  // Obtém o caminho do banco de dados
  Future<String> _getDbPath() async {
    Directory appDocDirectory = await getApplicationDocumentsDirectory();
    await Directory(appDocDirectory.path + '/' + 'dir').create(recursive: true);
    var path = await getApplicationDocumentsDirectory();
    return join(path.path, 'retryLaterDio.db');
  }
}

class RetryLaterDio extends Interceptor {
  RetryLaterDio() {
    cacheStore.init();
  }

  final _RetryLaterDioSembastCacheStore cacheStore = _RetryLaterDioSembastCacheStore();

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    if (((err.type == DioExceptionType.connectionError && err.error is SocketException) || (err.message ?? '').contains('Sem conexão com a internet')) && SaveToSubmitLater.containsHeader(err.requestOptions)) {
      await cacheStore.saveToCallLater(err.requestOptions);
      // Retorna uma resposta falsa para o usuário
      handler.resolve(Response(requestOptions: err.requestOptions, data: {
        'dioRetry': 'Salvo para enviar mais tarde',
      }));
    } else {
      return handler.next(err);
    }
  }

  static Future<void> submitSavedRequests(Function() done) async {
    // await cacheStore.clearAllStoreForLater();
    final savedRequests = await RetryLaterDio().cacheStore.getAllStoredForLater();

    for (final request in savedRequests) {
      try {
        // Adiciona Authorization Atualizado
        request.headers.remove('Authorization');
        final response = await dioConnect.fetch(RequestOptions(
          method: request.method,
          path: Uri.decodeQueryComponent(request.url),
          headers: request.headers,
          data: request.data,
        ));
        if (response.statusCode == 200) {
          await RetryLaterDio().cacheStore.removeFromSubmmitLater(request);
        }
      } catch (e) {
        if (kDebugMode) {
          print('Erro ao tentar submeter requisição salva: $e');
        }
      }
    }
    done();
  }
}

class SaveToSubmitLater extends retrofit.Headers {
  static const Map<String, dynamic> headers = {'saveToSubmitLater': 'true'};
  static bool containsHeader(RequestOptions request) {
    return request.headers.containsKey(headers.keys.first);
  }

  // exemplo de uso SaveToSubmitLater.containsHeader(request)
  const SaveToSubmitLater() : super(headers);
}
