import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:app_treino/model/geral/UsuarioFireBaseManter.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'UsuarioAppService.g.dart';
//"${ConfigURL.FIREBASE}/

@RestApi(baseUrl: '')
abstract class UsuarioAppService {
  factory UsuarioAppService(Dio dio, {String? baseUrl}) = _UsuarioAppService;

  @GET('${ConfigURL.FIREBASE}/aluno/consultarHistoricoPeso')
  Future<List<PesoUsuario>> consultarPesoUsuario(
      @Query('usuarioApp') String usuarioApp);

  @GET('${ConfigURL.FIREBASE}/aluno/consultarDadosUsuario')
  Future<UsuarioFireBaseManter> consultarDadosUsuario(
      @Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/aluno/atualizarPeso')
  Future<String> atualizarPesoUsuario(
      @Query('usuarioApp') String usuarioApp, @Query('peso') double peso);

  @POST('${ConfigURL.FIREBASE}/aluno/atualizarDadosSaude')
  Future<String> atualizarDadosSaude(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.FIREBASE}/aluno/habilitarWidgetsSaude')
  Future<String> habilitarWidgetsSaude(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.FIREBASE}/aluno/atualizarConsumoAgua')
  Future<List<HistoricoHidratacao>> atualizarConsumoAgua(@Query('usuarioApp') String usuarioApp, @Query('ml') num ml);

  @POST('${ConfigURL.FIREBASE}/aluno/removerConsumoAgua')
  Future<List<HistoricoHidratacao>> removerConsumoAgua(@Query('usuarioApp') String usuarioApp, @Query('ml') num ml);

  @GET('${ConfigURL.FIREBASE}/aluno/consultarConsumoAguaPorData')
  Future<List<HistoricoHidratacao>> consultarConsumoAguaPorData(@Query('usuarioApp') String usuarioApp, @Query('dataInicio') String dataInicio, @Query('dataFim') String dataFim);

  @POST('${ConfigURL.FIREBASE}/aluno/marcarAulaComoFavorita')
  Future<List<AulasFavoritas>> marcarAulaComoFavorita(@Query('usuarioApp') String usuarioApp, @Query('codigo') num codigo, @Query('favorita') bool favorita);

  @GET('${ConfigURL.FIREBASE}/aluno/consultarAulasFavoritas')
  Future<List<AulasFavoritas>> consultarAulasFavoritas(@Query('usuarioApp') String usuarioApp);
}

