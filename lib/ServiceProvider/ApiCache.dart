import 'dart:convert';

import 'package:app_treino/util/debug_utils.dart';
import 'package:crypto/crypto.dart';
import 'package:retrofit/http.dart' as retrofit;
import 'package:dio/dio.dart';
import 'package:sembast/sembast_io.dart';
import 'package:path_provider/path_provider.dart';

// Classe para armazenar metadados de cache
class ApiCacheMetadata {
  static final Map<String, ApiCacheTime> _cacheMetadata = {};
  static final Map<String, Duration> _customDurations = {};

  static void setCacheTime(String key, ApiCacheTime cacheTime) {
    _cacheMetadata[key] = cacheTime;
  }

  static void setCustomDuration(String key, Duration duration) {
    _customDurations[key] = duration;
  }

  static ApiCacheTime? getCacheTime(String key) {
    return _cacheMetadata[key];
  }

  static Duration? getCacheDuration(String key) {
    // Primeiro verifica se há uma duração customizada
    final customDuration = _customDurations[key];
    if (customDuration != null) {
      return customDuration;
    }
    // Senão, usa a duração do enum
    return _cacheMetadata[key]?.duration;
  }

  static Duration? getCustomDuration(String key) {
    return _customDurations[key];
  }
}

class ApiCacheParam extends retrofit.Query {
  const ApiCacheParam.clean() : super('appapicacheclean');
}

class Apicache extends retrofit.Headers {
  final ApiCacheTime keepFor;
  final Duration? customDuration;

  // Factory constructors para cada duração que retornam o valor correto
  const Apicache.fiveMin()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = null,
        super(const {'api-cache': '0:05:00.000000'});
  const Apicache.oneHour()
      : keepFor = ApiCacheTime.oneHour,
        customDuration = null,
        super(const {'api-cache': '1:00:00.000000'});
  const Apicache.oneDay()
      : keepFor = ApiCacheTime.oneDay,
        customDuration = null,
        super(const {'api-cache': '24:00:00.000000'});
  const Apicache.twoDays()
      : keepFor = ApiCacheTime.twoDays,
        customDuration = null,
        super(const {'api-cache': '48:00:00.000000'});
  const Apicache.threeDays()
      : keepFor = ApiCacheTime.threeDays,
        customDuration = null,
        super(const {'api-cache': '72:00:00.000000'});
  const Apicache.fiveDays()
      : keepFor = ApiCacheTime.fiveDays,
        customDuration = null,
        super(const {'api-cache': '120:00:00.000000'});
  const Apicache.oneWeek()
      : keepFor = ApiCacheTime.oneWeek,
        customDuration = null,
        super(const {'api-cache': '168:00:00.000000'});
  const Apicache.oneMonth()
      : keepFor = ApiCacheTime.oneMonth,
        customDuration = null,
        super(const {'api-cache': '720:00:00.000000'});

  // Constructors para durações customizadas comuns
  const Apicache.thirtySeconds()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(seconds: 30),
        super(const {'api-cache': '0:00:30.000000'});

  const Apicache.oneMinute()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 1),
        super(const {'api-cache': '0:01:00.000000'});

  const Apicache.twoMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 2),
        super(const {'api-cache': '0:02:00.000000'});

  const Apicache.fiveMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 5),
        super(const {'api-cache': '0:05:00.000000'});

  const Apicache.tenMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 10),
        super(const {'api-cache': '0:10:00.000000'});

  const Apicache.fifteenMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 15),
        super(const {'api-cache': '0:15:00.000000'});

  const Apicache.thirtyMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 30),
        super(const {'api-cache': '0:30:00.000000'});

  const Apicache.fortyFiveMinutes()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(minutes: 45),
        super(const {'api-cache': '0:45:00.000000'});

  const Apicache.twoHours()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(hours: 2),
        super(const {'api-cache': '2:00:00.000000'});

  const Apicache.sixHours()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(hours: 6),
        super(const {'api-cache': '6:00:00.000000'});

  const Apicache.twelveHours()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(hours: 12),
        super(const {'api-cache': '12:00:00.000000'});

  const Apicache.customThreeDays()
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = const Duration(days: 3),
        super(const {'api-cache': '72:00:00.000000'});

  // Constructor genérico para durações customizadas (interceptor atualizará o valor)
  const Apicache.custom(Duration duration)
      : keepFor = ApiCacheTime.fiveMin,
        customDuration = duration,
        super(const {'api-cache': 'custom'});

  // Constructor original (mantido para compatibilidade, mas interceptor atualizará o valor)
  const Apicache({required this.keepFor})
      : customDuration = null,
        super(const {'api-cache': 'enabled'});

  // Método para obter o valor da duração em formato string
  String get durationString {
    return customDuration?.toString() ?? keepFor.duration.toString();
  }

  // Método estático para obter a duração string de um ApiCacheTime
  static String getDurationString(ApiCacheTime cacheTime) {
    return cacheTime.duration.toString();
  }

  /// Método helper para criar cache customizado com duração específica
  ///
  /// Exemplos de uso:
  /// ```dart
  /// // Cache por 30 segundos
  /// @Apicache.custom(Duration(seconds: 30))
  ///
  /// // Cache por 2 horas e 30 minutos
  /// @Apicache.custom(Duration(hours: 2, minutes: 30))
  ///
  /// // Cache por 3 dias
  /// @Apicache.custom(Duration(days: 3))
  ///
  /// // Cache complexo
  /// @Apicache.custom(Duration(days: 1, hours: 6, minutes: 30))
  /// ```
  static void setCustomCacheForEndpoint(String method, String path, Duration duration) {
    final key = '$method:$path';
    ApiCacheMetadata.setCustomDuration(key, duration);
  }

  static bool containsHeader(RequestOptions request) {
    return request.headers.containsKey('api-cache');
  }

  static ApiCacheTime? getCacheTime(RequestOptions request) {
    // Primeiro tenta obter do header api-cache
    final headerValue = request.headers['api-cache'] as String?;
    if (headerValue != null && headerValue != 'enabled' && headerValue != 'true') {
      try {
        // Tenta interpretar como duração string (ex: "0:05:00.000000")
        if (headerValue.contains(':')) {
          // Mapeia durações conhecidas para os enums
          if (headerValue.startsWith('0:05:')) {
            return ApiCacheTime.fiveMin;
          } else if (headerValue.startsWith('1:00:')) {
            return ApiCacheTime.oneHour;
          } else if (headerValue.startsWith('24:00:')) {
            return ApiCacheTime.oneDay;
          } else if (headerValue.startsWith('48:00:')) {
            return ApiCacheTime.twoDays;
          } else if (headerValue.startsWith('72:00:')) {
            return ApiCacheTime.threeDays;
          } else if (headerValue.startsWith('120:00:')) {
            return ApiCacheTime.fiveDays;
          } else if (headerValue.startsWith('168:00:')) {
            return ApiCacheTime.oneWeek;
          } else if (headerValue.startsWith('720:00:')) {
            return ApiCacheTime.oneMonth;
          }
        }
      } catch (e) {
        // Se não conseguir interpretar, busca nos metadados
      }
    }

    // Fallback para metadados
    final key = '${request.method}:${request.path}';
    return ApiCacheMetadata.getCacheTime(key);
  }

  static Duration? getCacheDuration(RequestOptions request) {
    // Primeiro verifica se há uma duração customizada no header
    final headerValue = request.headers['api-cache'] as String?;
    if (headerValue == 'custom') {
      final key = '${request.method}:${request.path}';
      final customDuration = ApiCacheMetadata.getCustomDuration(key);
      if (customDuration != null) {
        return customDuration;
      }
    }

    // Tenta interpretar o header como duração string diretamente
    if (headerValue != null && headerValue.contains(':') && headerValue != 'enabled' && headerValue != 'true') {
      try {
        // Se o header já contém a duração, tenta parsear
        final parts = headerValue.split(':');
        if (parts.length >= 3) {
          final hours = int.tryParse(parts[0]) ?? 0;
          final minutes = int.tryParse(parts[1]) ?? 0;
          final secondsParts = parts[2].split('.');
          final seconds = int.tryParse(secondsParts[0]) ?? 0;
          final microseconds = secondsParts.length > 1 ? int.tryParse(secondsParts[1].padRight(6, '0').substring(0, 6)) ?? 0 : 0;

          return Duration(
            hours: hours,
            minutes: minutes,
            seconds: seconds,
            microseconds: microseconds,
          );
        }
      } catch (e) {
        // Se não conseguir parsear, continua com o método normal
      }
    }

    // Fallback para o método normal
    final cacheTime = getCacheTime(request);
    return cacheTime?.duration;
  }
}

enum ApiCacheTime {
  fiveMin(Duration(minutes: 5)),
  oneHour(Duration(hours: 1)),
  oneDay(Duration(days: 1)),
  twoDays(Duration(days: 2)),
  threeDays(Duration(days: 3)),
  fiveDays(Duration(days: 5)),
  oneWeek(Duration(days: 7)),
  oneMonth(Duration(days: 30));

  final Duration duration;

  const ApiCacheTime(this.duration);
}

/// Interceptor para cache persistente usando Sembast
class ApiCacheSembastInterceptor extends Interceptor {
  static Database? _database;
  static final StoreRef<String, Map<String, Object?>> _store = stringMapStoreFactory.store('api_cache');
  static bool _isInitializing = false;

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Sempre inicializa o banco primeiro
    await initialize();
    // Verifica se deve limpar o cache
    if (_shouldClearCache(options)) {
      await _clearCache();
      // Remove o parâmetro para não interferir na requisição
      options.queryParameters.remove('appapicacheclean');
    }

    // Verifica se a requisição tem cache habilitado
    if (Apicache.containsHeader(options)) {
      // Atualiza o header com o valor correto do cache time se disponível
      _updateCacheHeaderWithValue(options);

      DebugUtils.debugLog('[CACHEAPI]: Requisição com cache detectada: ${options.method} ${options.path}');
      DebugUtils.debugLog('[CACHEAPI]: Header api-cache: ${options.headers['api-cache']}');

      final cacheKey = _generateCacheKey(options, 'onRequest');
      DebugUtils.debugLog('[CACHEAPI]: Chave do cache: $cacheKey');

      final cachedData = await _getCachedResponse(cacheKey);

      if (cachedData != null && !_isExpired(cachedData)) {
        DebugUtils.debugLog(
            '[CACHEAPI]: Retornando resposta do cache ${options.path} Expira em ${DateTime.fromMillisecondsSinceEpoch(cachedData['expiresAt'] as int).difference(DateTime.now()).inMinutes} minutos');

        // Retorna resposta do cache
        final response = Response(
          requestOptions: options,
          data: cachedData['data'],
          statusCode: cachedData['statusCode'] as int? ?? 200,
          statusMessage: cachedData['statusMessage'] as String?,
          headers: Headers.fromMap(Map<String, List<String>>.from(
              (cachedData['headers'] as Map? ?? {}).map((key, value) => MapEntry(key.toString(), (value is List) ? value.map((e) => e.toString()).toList() : [value.toString()])))),
        );
        return handler.resolve(response);
      } else {
        DebugUtils.debugLog('[CACHEAPI]: Cache não encontrado ou expirado, fazendo requisição');
      }
    }

    handler.next(options);
  }

  /// Atualiza o header api-cache com o valor da duração em formato string
  void _updateCacheHeaderWithValue(RequestOptions options) {
    final key = '${options.method}:${options.path}';

    // Verifica se há uma duração customizada primeiro
    final customDuration = ApiCacheMetadata.getCustomDuration(key);
    if (customDuration != null) {
      final durationString = customDuration.toString();
      options.headers['api-cache'] = durationString;
      DebugUtils.debugLog('[CACHEAPI]: Header atualizado com duração customizada: ${options.headers['api-cache']}');
      return;
    }

    // Senão, usa a duração do enum
    final cacheTime = ApiCacheMetadata.getCacheTime(key);
    if (cacheTime != null) {
      final durationString = cacheTime.duration.toString();
      options.headers['api-cache'] = durationString;
      DebugUtils.debugLog('[CACHEAPI]: Header atualizado para: ${options.headers['api-cache']}');
    }

    // Se o header for 'custom', tenta detectar a duração customizada da anotação
    if (options.headers['api-cache'] == 'custom') {
      DebugUtils.debugLog('[CACHEAPI]: Detectado cache customizado, mas duração não registrada para $key');
    }
  }

  @override
  Future<void> onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Verifica se a requisição tinha cache habilitado
    if (Apicache.containsHeader(response.requestOptions)) {
      await initialize();

      DebugUtils.debugLog('[CACHEAPI]: Salvando resposta no cache: ${response.requestOptions.method} ${response.requestOptions.path}');

      final cacheKey = _generateCacheKey(response.requestOptions, 'onResponse');
      final duration = Apicache.getCacheDuration(response.requestOptions) ?? const Duration(days: 1);

      DebugUtils.debugLog('[CACHEAPI]: Duração do cache: ${duration.inSeconds} segundos');

      await _saveToCache(cacheKey, response, duration);
    }

    handler.next(response);
  }

  /// Inicializa o banco de dados Sembast
  static Future<void> initialize() async {
    if (_database != null) return;
    if (_isInitializing) return;

    _isInitializing = true;
    try {
      final appDocumentDir = await getApplicationDocumentsDirectory();
      final dbPath = '${appDocumentDir.path}/api_cache.db';
      DebugUtils.debugLog('[CACHEAPI]: Inicializando banco em: $dbPath');

      _database = await databaseFactoryIo.openDatabase(dbPath);
      DebugUtils.debugLog('[CACHEAPI]: Banco inicializado com sucesso');

      // Testa se o banco está funcionando
      final testKey = 'test_connection';
      await _store.record(testKey).put(_database!, {'test': 'ok', 'timestamp': DateTime.now().millisecondsSinceEpoch});
      final testResult = await _store.record(testKey).get(_database!);

      if (testResult != null) {
        DebugUtils.debugLog('[CACHEAPI]: Teste de conexão bem-sucedido');
        await _store.record(testKey).delete(_database!); // Remove o teste
      } else {
        DebugUtils.debugLog('[CACHEAPI]: Falha no teste de conexão');
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao inicializar cache Sembast: $e');
      _database = null;
    } finally {
      _isInitializing = false;
    }
  }

  /// Limpa cache manualmente (método público)
  static Future<void> clearCache() async {
    try {
      await initialize();
      if (_database != null) {
        await _store.delete(_database!);
        DebugUtils.debugLog('[CACHEAPI]: Cache API limpo manualmente');
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao limpar cache manualmente: $e');
    }
  }

  /// Limpa todos os caches (método estático público)
  static Future<void> clearAllCaches() async {
    try {
      await initialize();
      if (_database != null) {
        await _store.delete(_database!);
        DebugUtils.debugLog('[CACHEAPI]: Todos os caches foram limpos via método estático');
      } else {
        DebugUtils.debugLog('[CACHEAPI]: Database não inicializado para limpeza');
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao limpar todos os caches: $e');
    }
  }

  /// Fecha o banco de dados
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Lista todos os caches salvos (para debug)
  static Future<void> listAllCaches() async {
    try {
      await initialize();
      if (_database == null) {
        DebugUtils.debugLog('[CACHEAPI]: Database não inicializado');
        return;
      }

      final records = await _store.find(_database!);
      DebugUtils.debugLog('[CACHEAPI]: Total de caches salvos: ${records.length}');

      for (final record in records) {
        final data = record.value;
        final expiresAt = data['expiresAt'] as int?;
        final savedAt = data['savedAt'] as int?;
        final duration = data['duration'] as int?;

        if (expiresAt != null) {
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiresAt);
          final isExpired = DateTime.now().millisecondsSinceEpoch > expiresAt;
          DebugUtils.debugLog('[CACHEAPI]: Chave: ${record.key}');
          DebugUtils.debugLog('[CACHEAPI]: Expira em: $expiryDate');
          DebugUtils.debugLog('[CACHEAPI]: Expirado: $isExpired');
          if (savedAt != null) {
            final saveDate = DateTime.fromMillisecondsSinceEpoch(savedAt);
            DebugUtils.debugLog('[CACHEAPI]: Salvo em: $saveDate');
          }
          if (duration != null) {
            DebugUtils.debugLog('[CACHEAPI]: Duração: $duration segundos');
          }
          DebugUtils.debugLog('[CACHEAPI]: ---');
        }
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao listar caches: $e');
    }
  }

  /// Verifica se deve limpar o cache baseado no parâmetro
  bool _shouldClearCache(RequestOptions options) {
    final cleanParam = options.queryParameters['appapicacheclean'];
    return cleanParam == 'true' || cleanParam == true;
  }

  /// Limpa todo o cache
  Future<void> _clearCache() async {
    try {
      await initialize();
      if (_database != null) {
        await _store.delete(_database!);
        DebugUtils.debugLog('[CACHEAPI]: Cache API limpo com sucesso');
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao limpar cache: $e');
    }
  }

  /// Gera chave única para o cache
  String _generateCacheKey(RequestOptions options, String step) {
    // ignore: unnecessary_null_comparison
    if (options.uri == null) {
      options.queryParameters = {};
    }
    final url = options.uri.toString();
    final queryParams = options.queryParameters.toString().replaceAll('appapicacheclean=true', '').replaceAll('appapicacheclean=false', '');
    final headersCopy = Map<String, dynamic>.from(options.headers);
    DebugUtils.debugLog('[CACHEAPI]: Chave gerada: ${queryParams.toString()}');
    headersCopy.remove('User-Agent');
    headersCopy.remove('Authorization');
    headersCopy.remove('fbchave');
    headersCopy.remove('fbcodEmpresa');
    headersCopy.remove('limiteTimeout');
    headersCopy.remove('appapicacheclean');
    headersCopy.remove('original');
    headersCopy.remove('originalQuerys');
    headersCopy.remove('content-length');
    headersCopy.remove('api-cache');
    final headers = headersCopy.toString();
    DebugUtils.debugLog('[CACHEAPI]: Chave gerada: ${headers}');
    final body = options.data.toString();
    final keyString = '$url-$queryParams-$body';
    var ret = md5.convert(utf8.encode(keyString)).toString();
    DebugUtils.debugLog('[CACHEAPI]: Chave gerada: $ret no passo $step');
    return ret;
  }

  /// Recupera resposta do cache
  Future<Map<String, Object?>?> _getCachedResponse(String key) async {
    try {
      if (_database == null) {
        DebugUtils.debugLog('[CACHEAPI]: Database não inicializado para recuperar cache');
        return null;
      }

      var from = await _store.record(key).get(_database!);
      var total = await _store.count(_database!);
      var existingKeys = await _store.findKeys(_database!);
      DebugUtils.debugLog('[CACHEAPI]: Chaves existentes: $existingKeys');
      DebugUtils.debugLog('[CACHEAPI]: Total de caches: $total');
      if (from != null) {
        DebugUtils.debugLog('[CACHEAPI]: Cache encontrado para chave $key');
        DebugUtils.debugLog('[CACHEAPI]: Dados do cache: ${from.keys}');
      } else {
        DebugUtils.debugLog('[CACHEAPI]: Nenhum cache encontrado para chave $key');
      }
      return from;
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao recuperar cache: $e');
      return null;
    }
  }

  /// Salva resposta no cache
  Future<void> _saveToCache(String key, Response response, Duration duration) async {
    try {
      if (_database == null) {
        DebugUtils.debugLog('[CACHEAPI]: Database não inicializado para salvar cache');
        return;
      }

      final expiresAt = DateTime.now().add(duration);
      final cacheData = {
        'data': response.data,
        'statusCode': response.statusCode,
        'statusMessage': response.statusMessage,
        'headers': response.headers.map,
        'expiresAt': expiresAt.millisecondsSinceEpoch,
        'savedAt': DateTime.now().millisecondsSinceEpoch,
        'duration': duration.inSeconds,
      };

      await _store.record(key).put(_database!, cacheData);
      DebugUtils.debugLog('[CACHEAPI]: Cache salvo para chave $key');
      DebugUtils.debugLog('[CACHEAPI]: Expira em: $expiresAt');
      DebugUtils.debugLog('[CACHEAPI]: Duração: ${duration.inSeconds} segundos');

      // Verifica se foi salvo corretamente
      final saved = await _store.record(key).get(_database!);
      if (saved != null) {
        DebugUtils.debugLog('[CACHEAPI]: Verificação: Cache salvo com sucesso');
      } else {
        DebugUtils.debugLog('[CACHEAPI]: ERRO: Cache não foi salvo corretamente');
      }
    } catch (e) {
      DebugUtils.debugLog('[CACHEAPI]: Erro ao salvar cache: $e');
    }
  }

  /// Verifica se o cache expirou
  bool _isExpired(Map<String, Object?> cachedData) {
    final expiresAt = cachedData['expiresAt'] as int?;
    if (expiresAt == null) {
      DebugUtils.debugLog('[CACHEAPI]: Cache sem data de expiração, considerando expirado');
      return true;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final isExpired = now > expiresAt;
    final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiresAt);

    DebugUtils.debugLog('[CACHEAPI]: Verificando expiração');
    DebugUtils.debugLog('[CACHEAPI]: Agora: ${DateTime.now()}');
    DebugUtils.debugLog('[CACHEAPI]: Expira em: $expiryDate');
    DebugUtils.debugLog('[CACHEAPI]: Expirado: $isExpired');

    return isExpired;
  }
}
