// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PlannerService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _PlannerService implements PlannerService {
  _PlannerService(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<List<DicaPlanner>> consultarDicas(
    limit,
    usuarioMovel,
    tipo,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'limit': limit,
      r'usuarioMovel': usuarioMovel,
      r'tipo': tipo,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<DicaPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{UCP}/prest/dicasNutri/{chave}/dicas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => DicaPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicaoPlanner>> consultarReceitas() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicaoPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/listaDeRefeicoes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => RefeicaoPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> avaliarRefeicao(
    refRefeicao,
    nota,
    refUsuario,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refRefeicao': refRefeicao,
      r'nota': nota,
      r'refUsuario': refUsuario,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/premiumRefeicao/avaliarRefeicao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> comentarRefeicao(
    refRefeicao,
    nota,
    comentario,
    refUsuario,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refRefeicao': refRefeicao,
      r'nota': nota,
      r'comentario': comentario,
      r'refUsuario': refUsuario,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/premiumRefeicao/comentarRefeicao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> manterPlanoRefeicao(
    plano,
    refUsuario,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'refUsuario': refUsuario};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(plano);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/premiumRefeicao/manterPlanoUsuario',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> bloquearRefeicao(
    refPrograma,
    refRefeicao,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refPrograma': refPrograma,
      r'refRefeicao': refRefeicao,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/premiumRefeicao/naoReceberEssaRefeicao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> subistituirRefeicao(
    refUsuario,
    refOriginal,
    refSubstituta,
    doDia,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refUsuario': refUsuario,
      r'refOriginal': refOriginal,
      r'refSubstituta': refSubstituta,
      r'doDia': doDia,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/premiumRefeicao/substituirRefeicaoParaODia',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PlanoUsuarioExibir> obeterListaRefeicoes(
    refUsuario,
    montarRefeicao, {
    doDia,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refUsuario': refUsuario,
      r'montarRefeicao': montarRefeicao,
      r'doDia': doDia,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PlanoUsuarioExibir>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/obterRefeicoes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PlanoUsuarioExibir.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<RefeicaoPlanner>> obterListaRefeicoesAlternativas(
    restricoes,
    tipo,
    refRefeicao,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'restricoes': restricoes,
      r'tipo': tipo,
      r'refRefeicao': refRefeicao,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicaoPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/refeicoesAlternativas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => RefeicaoPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<Ingrediente>> obterListaDeCompras(
    refUsuario,
    diaConsultar,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refUsuario': refUsuario,
      r'diaConsultar': diaConsultar,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<Ingrediente>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/listaDeCompras',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Ingrediente.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicaoPlanner>> obterRefeicoesMesmoAutor(
    refAutor, {
    montarRefeicao = true,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'refAutor': refAutor,
      r'montarRefeicao': montarRefeicao,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicaoPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/refeicooesDe',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => RefeicaoPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicaoPlanner>> obterRefeicoesDeAcordo(tipoRefeicao) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'tipoRefeicao': tipoRefeicao};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicaoPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/listaDeRefeicoes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => RefeicaoPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<RefeicaoPlanner>> obterUltimasRefeicoesCadastradas(
    tipoRefeicao,
    ultimaRefeicao, {
    quantidade = 5,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'tipoRefeicao': tipoRefeicao,
      r'ultimaRefeicao': ultimaRefeicao,
      r'quantidade': quantidade,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<RefeicaoPlanner>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/premiumRefeicao/ultimasRefeicoes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => RefeicaoPlanner.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
