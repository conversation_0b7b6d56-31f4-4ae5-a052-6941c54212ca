import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/vendaplanos/ConfiguracaoVendasUnidade.dart';
import 'package:app_treino/model/vendaplanos/PagamentoParcelaIserir.dart';
import 'package:app_treino/model/vendaplanos/PagamentoParcelasDto.dart';
import 'package:app_treino/model/vendaplanos/ParcelasVencidas.dart';
import 'package:app_treino/model/vendaplanos/PlanoVendidoUnidade.dart';
import 'package:app_treino/model/vendaplanos/UnidadeVendaDePlano.dart';
import 'package:app_treino/model/vendaplanos/VendaIserir.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'ServiceVendaDePlano.g.dart';

@RestApi()
abstract class ServiceVendaDePlano {
  factory ServiceVendaDePlano(Dio dio, {String? baseUrl}) = _ServiceVendaDePlano;

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/unidades/{chave}')
  Future<List<UnidadeVendaDePlano>> obterUnidadesParaChave();

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/unidade/{unidade}')
  Future<List<PlanoVendidoUnidade>> obterDadosDaUnidadePlanos(@Path('unidade') num unidade);

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/planos/{unidade}')
  Future<List<PlanoVendidoUnidade>> obterTodosPlanosDaUnidade(@Path('unidade') num unidade);

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/configs/{unidade}')
  Future<ConfiguracaoVendasUnidade> obterConfiguracaoVendasOnline(@Path('unidade') num unidade);

  @POST('${ConfigURL.TREINO}/prest/empresa/{chave}/convenioPagamento')
  Future<ConfigLinkPagamento> obterConfiguracaoLinkPagamento(@Query('codEmpresa') num codEmpresa);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/tkn/{tokenApp}')
  Future<String> gerarTokenVenda(@Body() VendaNova venda);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarClienteJson')
  Future<DadosDoUsuario> consultarAlunoViaEmailOuCPF(@Query('cpf') String cpf, @Query('email') String email);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/alunovendaapp/{token}')
  Future<String> cadastrarUmaNovaVenda(@Body() VendaNova planoInserir, @Path('token') String token);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/simular/{plano}/{unidade}')
  Future<ContratoUsuario> simularVenda(@Body() VendaNova planoInserir, @Path('plano') num plano, @Path('unidade') num unidade);

  @POST('${ConfigURL.APIZW}/prest/negociacao/{chave}/renovarContrato')
  Future<ContratoUsuario> renovarContrato(@Query('contrato') num contrato, @Query('simulacao') bool simulacao, @Query('cliente') num cliente, @Query('origemSistema') num origemSistema);

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/plano/{codPlano}/contrato/{codContrato}')
  Future<String> obterInformacaoContratual(
    @Path('codPlano') num codPlano,
    @Path('codContrato') num codContrato,
  );

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/aluno/{matricula}')
  Future<ClienteParcelas> consultarClienteParcelas(@Path('matricula') num matricula);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/cobrarparcelasabertas/{matricula}')
  Future<String> cobrarParcelas(@Path('matricula') num matricula, @Body() PagamentoParcelasNova cobrarParcelas);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/adicionarCupomDescontoSite')
  Future<String> validarCupomDesconto(@Query('numeroCupomDesconto') String numeroCupomDesconto, @Body() String body);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/cobrarParcelasAbertasPix/{matricula}')
  Future<Pix> cobrarParcelasComPix(@Path('matricula') num matricula, @Body() CobrarParcelaPix cobrarParcelas);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/consultarParcelasPix/{codigoPix}')
  Future<StatusParcelaPix> consultarStatusParcelaPix(@Path('codigoPix') num codigoPix);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/v2/cobrarParcelasAbertasPix/{matricula}')
  Future<Pix> cobrarParcelasSelecionadasComPix(@Path('matricula') num matricula, @Query('unidade') num unidade, @Query('origemCobranca') num origemCobranca, @Query('parcelasSelecionadas') String parcelasSelecionadas, @Query('enviarEmail') bool enviarEmail);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/cobrarparcelasabertas/{matricula}')
  Future<dynamic> cobrarParcelasSelecionadasComCartao(@Path('matricula') num matricula, @Body() ParcelasPagamentoDto cobrarParcelas);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/cobrarParcelasAbertasBoleto/{matricula}')
  Future<dynamic> cobrarParcelasSelecionadasComBoleto(@Path('matricula') num matricula, @Body() ParcelasPagamentoDto cobrarParcelas);
}
