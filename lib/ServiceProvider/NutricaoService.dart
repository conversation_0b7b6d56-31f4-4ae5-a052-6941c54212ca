import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'NutricaoService.g.dart';
//"${ConfigURL.FIREBASE}/

@RestApi(baseUrl: '')
abstract class NutricaoService {
  factory NutricaoService(Dio dio, {String? baseUrl}) = _NutricaoService;
  @GET('${ConfigURL.FIREBASE}/nutri/consultarHistoricoPeso')
  Future<List<PesoUsuario>> consultarPesoUsuario(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarPeso')
  Future<List<PesoUsuario>> atualizarPesoUsuario(@Query('usuarioApp') String usuarioApp, @Query('peso') double peso);

  @POST('${ConfigURL.FIREBASE}/nutri/configurarPlanoNutricional')
  Future<String> configurarPlanoNutricional(@Body() Map<String, dynamic> body);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarPlanoNutricional')
  Future<PlanoNutricional> consultarPlanoNutricional(@Query('usuarioApp') String usuarioApp, @Query('dataConsulta') String dataConsulta);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarMlConsumido')
  Future<List<HistoricoHidratacao>> atualizarMlConsumido(@Query('usuarioApp') String usuarioApp, @Query('ml') num ml);

  @POST('${ConfigURL.FIREBASE}/nutri/removerMlConsumido')
  Future<List<HistoricoHidratacao>> removerMlConsumido(@Query('usuarioApp') String usuarioApp, @Query('ml') num ml);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarMlConsumidoPorData')
  Future<List<HistoricoHidratacao>> consultarMlConsumidoPorData(@Query('usuarioApp') String usuarioApp, @Query('dataInicio') String dataInicio, @Query('dataFim') String dataFim);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarRefeicoes?tipoConsulta={APPTIPOCONSULTA}&chave={chave}')
  Future<List<Refeicoes>> consultarRefeicoes();

  @POST('${ConfigURL.FIREBASE}/nutri/gerarReceitasPlanoNutricional?tipoConsulta={APPTIPOCONSULTA}&chave={chave}')
  Future<List<RefeicoesDoPlano>> gerarReceitasPlanoNutricional(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarDataInicioTerminoPlano')
  Future<PlanoNutricional> atualizarDataInicioTerminoPlano(@Query('usuarioApp') String usuarioApp, @Query('dataInicio') String dataInicio, {@Query('duracao') num? duracao});

  @GET('${ConfigURL.FIREBASE}/nutri/consultarListaCompras')
  Future<List<ItemListaCompras>> consultarListaCompras(@Query('usuarioApp') String usuarioApp, @Query('dataInicio') String dataConsulta, @Query('dataFim') String dataFim);

  @POST('${ConfigURL.FIREBASE}/nutri/consumirRefeicao')
  Future<String> consumirRefeicao(@Query('usuarioApp') String usuarioApp, @Query('dataRefeicao') String dataRefeicao, @Query('consumido') bool consumido, @Query('id') String id);

  @GET('${ConfigURL.FIREBASE}/nutri/calendarioNutricao')
  Future<List<RefeicoesDoPlano>> calendarioNutricao(@Query('usuarioApp') String usuarioApp, @Query('dataConsulta') String dataConsulta);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarReceitasGeradas')
  Future<List<RefeicoesDoPlano>> consultarReceitasGeradas(@Query('usuarioApp') String usuarioApp, {@Query('dataConsulta') String? dataConsulta});

  @POST('${ConfigURL.FIREBASE}/nutri/consultarReceitasParaSubstituicao?tipoConsulta={APPTIPOCONSULTA}&chave={chave}')
  Future<List<Refeicoes>> consultarReceitasParaSubstituicao(@Body() Map<String, dynamic> map);

  @POST('${ConfigURL.FIREBASE}/nutri/substituirReceita')
  Future<String> substituirReceita(@Body() Map<String, dynamic> map);

  @POST('${ConfigURL.FIREBASE}/nutri/confirmarReceitasGeradas')
  Future<PlanoNutricional> confirmarReceitasGeradas(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarHorarioRefeicoes')
  Future<String> atualizarHorarioRefeicoes(@Query('usuarioApp') String usuarioApp, @Body() Map<String, dynamic> body);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarIngredientes')
  Future<List<Ingredientes>> consultarIngredientes();

  @POST('${ConfigURL.FIREBASE}/nutri/consumirRefeicaoAvulsa')
  Future<String> consumirRefeicaoAvulsa(@Query('usuarioApp') String usuarioApp, @Query('consumido') bool consumido, @Query('id') String id, @Body() Map<String, dynamic> body);

  @POST('${ConfigURL.FIREBASE}/nutri/adicionarLegendaRefeicaoAvulsa')
  Future<String> adicionarLegendaRefeicaoAvulsa(@Query('usuarioApp') String usuarioApp, @Query('id') String id, @Query('legenda') String legenda);

  @Headers(<String, dynamic>{'Content-Type': 'text/plain'})
  @POST('${ConfigURL.FIREBASE}/nutri/salvarFotoRefeicao')
  Future<String> salvarFotoRefeicao(@Query('usuarioApp') String usuarioApp, @Query('consumido') bool consumido, @Query('id') String id, @Body() String fotoBase64);

  @POST('${ConfigURL.FIREBASE}/nutri/favoritarRefeicao')
  Future<List<ReceitaFavorita>> favoritarRefeicao(@Query('usuarioApp') String usuarioApp, @Query('ref') String ref, @Query('favorita') bool favorita);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarReceitasFavoritas')
  Future<List<ReceitaFavorita>> consultarReceitasFavoritas(@Query('usuarioApp') String usuarioApp);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarListaComprasGerada')
  Future<List<ItemListaCompras>> consultarListaComprasGerada(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/editarItemListaCompras')
  Future<List<ItemListaCompras>> editarItemListaCompras(
      @Query('usuarioApp') String usuarioApp, @Query('ref') String ref, @Query('comprado') bool comprado, @Query('nome') String nome, @Query('quantidade') String quantidade);

  @POST('${ConfigURL.FIREBASE}/nutri/gravarListaCompras')
  Future<List<ItemListaCompras>> gravarListaCompras(@Query('usuarioApp') String usuarioApp, @Query('dataInicioListaCompras') String dataInicioListaCompras,
      @Query('dataFimListaCompras') String dataFimListaCompras, @Body() Map<String, dynamic> body);

  @GET('${ConfigURL.UCP}/prest/dicasNutri/{chave}/dicas')
  Future<List<DicasNutri>> consultarDicas(@Query('limit') num limit, @Query('usuarioMovel') num usuarioMovel, @Query('tipo') num tipo);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarHistoricoRespiracao')
  Future<List<HistoricoRespiracao>> consultarHistoricoRespiracao(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarRespiracao')
  Future<List<HistoricoRespiracao>> atualizarRespiracao(@Query('usuarioApp') String usuarioApp);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarPlanosNutricionais?tipoConsulta={APPTIPOCONSULTA}&chave={chave}')
  Future<List<PlanoNutricao>> consultarPlanosNutricionais();

  @GET('${ConfigURL.FIREBASE}/nutri/consultarPlanosNutricionaisSemCache?tipoConsulta={APPTIPOCONSULTA}&chave={chave}')
  Future<List<PlanoNutricao>> consultarPlanosNutricionaisSemCache();

  @GET('${ConfigURL.FIREBASE}/nutri/consultarNovidades')
  Future<List<Novidades>> consultarNovidades();

  @GET('${ConfigURL.FIREBASE}/nutri/consultarProgressoTotalPlano')
  Future<RetornoConclusao> consultarProgressoTotalPlano(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/substituirPlanoNutricional')
  Future<PlanoNutricional> substituirPlanoNutricional(@Query('usuarioApp') String usuarioApp, @Query('refNovoPlano') String refNovoPlano);

  @POST('${ConfigURL.FIREBASE}/nutri/criarAlimento')
  Future<List<AlimentoCriado>> criarAlimento(@Query('usuarioApp') String usuarioApp, @Query('gordura') num gordura, @Query('carboidratos') num carboidratos,
      @Query('proteinas') num proteinas, @Query('nome') String nome, @Query('calorias') num calorias);

  @GET('${ConfigURL.FIREBASE}/nutri/consultarAlimentosCriados')
  Future<List<AlimentoCriado>> consultarAlimentosCriados(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/nutri/0')
  Future<String> atualizarMetaAguaETamanhoCopo(
      @Query('usuarioApp') String usuarioApp, @Query('tamanhoCopo') num tamanhoCopo, @Query('metaHidratacao') num metaHidratacao, @Query('lembrete') bool lembrete);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarObjetivo')
  Future<PlanoNutricional> atualizarObjetivo(@Query('usuarioApp') String usuarioApp, @Query('objetivo') String objetivo);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarMetaDePeso')
  Future<String> atualizarMetaDePeso(@Query('usuarioApp') String usuarioApp, @Query('metaDePeso') num metaDePeso);

  @POST('${ConfigURL.FIREBASE}/nutri/atualizarLembretes')
  Future<String> atualizarLembretes(@Query('usuarioApp') String usuarioApp, @Body() Map<String, dynamic> body);

  @POST('${ConfigURL.FIREBASE}/nutri/resetarPlanoNutricional')
  Future<String> resetarPlanoNutricional(@Query('usuarioApp') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/aluno/enviarDadosMinhaAcademia')
  Future<String> enviarDadosMinhaAcademia(
    @Query('chave') String chave,
    @Query('nome') String nome,
    @Query('dispositivo') String dispositivo,
    @Query('usuarioMovel') String usuarioMovel,
  );

  @GET('${ConfigURL.FIREBASE}/planoAlimentar/listar')
  Future<List<PlanosNutricionaisFitStream>> consultarProgramasFitStream(@Query('chave') String chave);

  @POST('${ConfigURL.FIREBASE}/planoAlimentar/inserir')
  Future<PlanosNutricionaisFitStream> inserirProgramasFitStream(@Body() Map<String, dynamic> body);

  @DELETE('${ConfigURL.FIREBASE}/planoAlimentar/deletar/{ref}')
  Future<String> removerProgramaFitStream(@Path('ref') String ref);

  @GET('${ConfigURL.FIREBASE}/planoAlimentar/detalhes/{ref}/{user}')
  Future<PlanosNutricionaisFitStream> consularDetalhesProgramaFitStream(@Path('ref') String ref, @Path('user') String user);

  @POST('${ConfigURL.FIREBASE}/planoAlimentar/contents/concluidoLike')
  Future<Content> marcarProgramaFitStreamComoConcluidoELike(@Body() Map<String, dynamic> body);
}
