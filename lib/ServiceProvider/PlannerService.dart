import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/planner/DicasPlanner.dart';
import 'package:app_treino/model/planner/RefeicaoPlanner.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'PlannerService.g.dart';

@RestApi(baseUrl: '')
abstract class PlannerService {
  factory PlannerService(Dio dio, {String? baseUrl}) = _PlannerService;

  @GET('${ConfigURL.UCP}/prest/dicasNutri/{chave}/dicas')
  Future<List<DicaPlanner>> consultarDicas(@Query('limit') num limit, @Query('usuarioMovel') num usuarioMovel, @Query('tipo') num tipo);

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/listaDeRefeicoes')
  Future<List<RefeicaoPlanner>> consultarReceitas();

  @PATCH('${ConfigURL.FIREBASE}/premiumRefeicao/avaliarRefeicao')
  Future<String> avaliarRefeicao(@Query('refRefeicao') String refRefeicao, @Query('nota') num nota, @Query('refUsuario') String refUsuario);

  @PATCH('${ConfigURL.FIREBASE}/premiumRefeicao/comentarRefeicao')
  Future<String> comentarRefeicao(@Query('refRefeicao') String refRefeicao, @Query('nota') num nota, @Query('comentario') String comentario, @Query('refUsuario') String refUsuario);

  @PATCH('${ConfigURL.FIREBASE}/premiumRefeicao/manterPlanoUsuario')
  Future<String> manterPlanoRefeicao(@Body() Map<String, dynamic> plano, @Query('refUsuario') String refUsuario);

  @POST('${ConfigURL.FIREBASE}/premiumRefeicao/naoReceberEssaRefeicao')
  Future<String> bloquearRefeicao(@Query('refPrograma') String refPrograma, @Query('refRefeicao') String refRefeicao);

  @POST('${ConfigURL.FIREBASE}/premiumRefeicao/substituirRefeicaoParaODia')
  Future<String> subistituirRefeicao(@Query('refUsuario') String refUsuario, @Query('refOriginal') String refOriginal, @Query('refSubstituta') String refSubstituta, @Query('doDia') String doDia);

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/obterRefeicoes')
  Future<PlanoUsuarioExibir> obeterListaRefeicoes(@Query('refUsuario') String refUsuario, @Query('montarRefeicao') bool montarRefeicao, {@Query('doDia') String? doDia});

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/refeicoesAlternativas')
  Future<List<RefeicaoPlanner>> obterListaRefeicoesAlternativas(@Query('restricoes') String restricoes, @Query('tipo') String tipo, @Query('refRefeicao') String refRefeicao);

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/listaDeCompras')
  Future<List<Ingrediente>> obterListaDeCompras(@Query('refUsuario') String refUsuario, @Query('diaConsultar') String diaConsultar);

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/refeicooesDe')
  Future<List<RefeicaoPlanner>> obterRefeicoesMesmoAutor(@Query('refAutor') String refAutor, {@Query('montarRefeicao') bool montarRefeicao = true});

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/listaDeRefeicoes')
  Future<List<RefeicaoPlanner>> obterRefeicoesDeAcordo(@Query('tipoRefeicao') String tipoRefeicao);

  @GET('${ConfigURL.FIREBASE}/premiumRefeicao/ultimasRefeicoes')
  Future<List<RefeicaoPlanner>> obterUltimasRefeicoesCadastradas(@Query('tipoRefeicao') String tipoRefeicao, @Query('ultimaRefeicao') num ultimaRefeicao, {@Query('quantidade') num quantidade = 5});
}
