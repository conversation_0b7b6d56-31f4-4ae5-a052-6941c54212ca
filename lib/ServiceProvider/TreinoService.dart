import 'package:app_treino/ServiceProvider/ConexaoFracaInterceptor.dart';
import 'package:app_treino/ServiceProvider/RetryLaterDio.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisica.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/avalicaoFisica/GrupoMuscular.dart';
import 'package:app_treino/model/avalicaoFisica/ManterAvaliacaoFisica.dart';
import 'package:app_treino/model/avalicaoFisica/ParQ.dart';
import 'package:app_treino/model/graduacao/AtividadesUmNivel.dart';
import 'package:app_treino/model/graduacao/ResultadoGraduacao.dart';
import 'package:app_treino/model/treinoAluno/BodyCriarTreinoIA.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/screens/prescricaoDeTreino/mockia/mock.ia.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'TreinoService.g.dart';

@RestApi(baseUrl: '')
abstract class TreinoService {
  factory TreinoService(Dio dio, {String? baseUrl}) = _TreinoService;

  @POST('${ConfigURL.FIREBASE}/pactoIa/removerPlanoDeTreinoAtual')
  Future<String> excluirTreinoAtual(@Body() BodyExcluirPlanoATual body);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/criaProgramaTreinoPorIA')
  Future<String> solicitarTreinoParaIA(@Body() BodyCriarTreinoIa body);

  @PUT('${ConfigURL.TREINO}/prest/psec/programas/aprovar/{codigoPrograma}')
  Future<bool> aprovarTreinoPorIA(@Path('codigoPrograma') num codigoPrograma, @Header('empresaId') num empresaId);

  @GET('${ConfigURL.TREINO}/prest/psec/programas/lista-prescricaoV2')
  Future<List<DadosUsuarioProgramaIA>> consultarTreinosPorSituacao(@Header('empresaId') num empresaId, @Query('size') int size,
      {@Query('filters') String filters =
          '{"search":"","primario":[{"id":"TODOS_ALUNOS"}],"pessoaProfessorLogado":1,"secundario":[{"id":"A_APROVAR"}],"terciario":[],"quartenario":{"id":"ORIGEM_ACAO","nome":"APP_TREINO"}}'});

  @PATCH('${ConfigURL.FIREBASE}/pactoIa/validandoTreinoIA')
  Future<DadosUsuarioProgramaIA> validandoTreinoIA(@Body() Map<String, dynamic> body);

  @PATCH('${ConfigURL.FIREBASE}/pactoIa/concluirValidacao')
  Future<String> concluirValidacao(@Body() Map<String, dynamic> body);

  @GET('{urlTreino}/prest/psec/fichas/obterFichaPredefinida/{codigoFicha}')
  Future<Fichas> consultarFichasPredefinidasIA(@Path('urlTreino') String? urlTreino, @Query('empresaId') num empresaId, @Path('codigoFicha') num? codigoFicha);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/atual')
  Future<ProgramadeTreino> consultarProgramaAtual([@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/atual')
  Future<ProgramadeTreino> consultarProgramaAtualColaborador([@Query('username') String? username]);

  @SaveToSubmitLater()
  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/submittreinoCommentAPP')
  Future<Acompanhamento> concluirTreino(@Query('username') String userName, @Query('idPrograma') int idPrograma, @Query('idFicha') int idFicha, @Query('dia') String dia,
      @Query('nota') int nota, @Query('tempo') int tempo, @Query('comentario') String? comentario,
      [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/v2/submittreinoCommentAPP')
  Future<Acompanhamento> concluirTreinoV2(@Query('username') String userName, @Query('idPrograma') int idPrograma, @Query('idFicha') int idFicha, @Query('dia') String dia,
      @Query('nota') int nota, @Query('tempo') int tempo, @Query('comentario') String? comentario,
      [@Query('matricula') String? matricula]);
  

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/submitserie?atualizar=false')
  Future<String> concluirSerie(
      {@Query('username') String? userName,
      @Query('idPrograma') num? idPrograma,
      @Query('idFicha') num? idFicha,
      @Query('inicio') String? inicio,
      @Query('fim') String? fim,
      @Query('idAtividade') num? idAtividade,
      @Query('idSerie') num? idSerie,
      @Query('valor1') String? valor1,
      @Query('valor2') String? valor2,
      @Query('matricula') String? matricula});

  @SaveToSubmitLater()
  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.FIREBASE}/manter/marcarExecucaoTreino')
  Future<String> enviarTreinoFirebase(@Body() Map<String, dynamic> map, @Query('refUsuario') String refUsuario);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @GET('${ConfigURL.FIREBASE}/aluno/consultarTreinosConsecutivos')
  Future<List<String>> consultarTreinosConsecutivos(@Query('usuarioAPP') String usuarioApp);

  @GET('${ConfigURL.FIREBASE}/manter/historicoExecucaoDeTreinos')
  Future<List<ExecucaoTreinoFirebase>> consultarHistoricoExecucao(@Query('refUsuario') String refUsuario);

  @POST('${ConfigURL.TREINO}/prest/avaliacao/{chave}/historico')
  Future<List<AvaliacaoFisica>> consultarHistoricoAvaliacao(@Query('matricula') String matricula);

  @GET('${ConfigURL.TREINO}/prest/psec/avaliacoes-fisica/{idAvaliacao}')
  Future<AvaliacaoFisicaRecente> consultarDadosAvaliacao(@Path('idAvaliacao') String idAvaliacao, @Header('empresaId') num empresaId);

  @Headers({'AuthorizationMs': ''})
  @POST('${ConfigURL.TREINO}/prest/psec/avaliacoes-fisica/alunos/{codAluno}')
  Future<ManterAvaliacaoFisica> cadastrarAvaliacaoFisica(@Path('codAluno') String codAluno, @Body() ManterAvaliacaoFisica avaliacao);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.TREINO}/prest/ficha/{chave}/serie/alterar')
  Future<String> editarSerie(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.TREINO}/prest/avaliacao/{chave}/avaliadoresFisicos')
  Future<List<AvaliadorFisico>> consultarAvaliadoresFisicos(@Query('data') String data);

  @POST('${ConfigURL.TREINO}/prest/avaliacao/{chave}/horariosSugeridos')
  Future<List<EventoHorario>> consultarHorariosSugeridos(@Query('data') String data, @Query('professor') num codigoProfessor, @Query('empresa') num codigoEmpresa);

  @POST('${ConfigURL.TREINO}/prest/avaliacao/{chave}/inserirAgendamento')
  Future<DadosAgendamento> agendarAvaliacaoFisica(@Query('data') String data, @Query('horario') String horario, @Query('tipoEvento') num tipoEvento, @Query('professor') num codigoProfessor,
      @Query('empresa') num codigoEmpresa, @Query('matricula') String matricula);

  @POST('${ConfigURL.TREINO}/prest/config/{chave}/obterValorProdutoAvaliacao')
  Future<double> obterValorProdutoAvaliacao(@Query('matricula') String matricula);

  @GET('${ConfigURL.GRADUACAO}/fichas/{codigoAluno}/fichasaluno?key={chave}')
  Future<List<ResultadoGraduacao>> obterFichasGraduacaoAluno(@Path('codigoAluno') num codigoAluno);

  @GET('${ConfigURL.GRADUACAO}/niveis/{idNivel}')
  Future<AtividadesUmNivel> obterAtividadesAcademia(@Path('idNivel') String idNivel);
  //https://graduacao.ms.pactosolucoes.com.br/niveis/3

  @GET('${ConfigURL.TREINO}/prest/avaliacao/{chave}/obterPerguntasParQ')
  Future<ResponsePerguntasParQ> consultarPerguntasParQ();

  @GET('${ConfigURL.TREINO}/prest/avaliacao/{chave}/imprimirParQAssinaturaDigital')
  Future<dynamic> consultarSeAlunoRespondeuParQ({@Query('matricula') required num matricula});

  @POST('${ConfigURL.TREINO}/prest/avaliacao/{chave}/salvarRespostasParQ')
  Future<dynamic> salvarRespostasParQ({@Query('usuarioZw') required num usuarioZw, @Body() required Map<String, dynamic> body});

  @GET('${ConfigURL.TREINO}/prest/avaliacao/{chave}/listarRespostasParQPorCliente')
  Future<List<ItemRespostaParQ>> listarRespostasParQPorCliente({@Query('clienteCodigo') required num cod});

  @POST('${ConfigURL.URLZW}/app/prest/contratoassinatura')
  Future<AssinaturaParQ> consultarAssinaturaParq(@Body() String bodyAssinatura);

  @POST('${ConfigURL.TREINO}/prest/psec/programas/{chave}/consultarGruposTrabalhadosPeriodo')
  Future<GrupoMuscular> consultarGruposMusculares(
      {@Query('dataInicial') required String dataInicial, @Query('dataFinal') required String dataFinal, @Query('codigoCliente') required num codigoCliente});

  @UseAppCache()
  @GET('${ConfigURL.TREINO}/prest/psec/fichas/grupos-musculares/{codigoFicha}')
  Future<List<String>> consultarGruposMuscularesDaFicha({@Path('codigoFicha') required String codigoFicha, @Header('empresaId') required num empresaId});

  @PATCH('${ConfigURL.FIREBASE}/pactoIaV2/resetarUsuario')
  Future<String> resetarUsuario(@Body() Map<String, dynamic> body);

  @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/avaliacao-professor-por-cliente')
  Future<List<AvaliacaoDeProfessor>> listaAvaliacoesPorCliente( @Query('codUsuario') int codUsuario);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/v2/avaliacao-professor')
  Future<AssinaturaParQ> avaliarProfessor(@Body() Map<String, dynamic> body);

}
