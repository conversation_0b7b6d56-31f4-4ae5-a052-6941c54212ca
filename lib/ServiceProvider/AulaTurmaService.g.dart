// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'AulaTurmaService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _AulaTurmaService implements AulaTurmaService {
  _AulaTurmaService(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<SaldoAulaColetiva> consultarSaldoAulasColetivas(
      {required matricula}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'matricula': matricula};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<SaldoAulaColetiva>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/saldo-aulas-coletivas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = SaldoAulaColetiva.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasColetivas({
    required dia,
    required codigoEmpresa,
    required matricula,
    required aulasFuturas,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dia': dia,
      r'empresa': codigoEmpresa,
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<AulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/aula/{chave}/consultarAulas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AulaTurma>> consultarTurmasDoAluno(
    matricula,
    inicio,
    fim,
    contrato,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'inicio': inicio,
      r'fim': fim,
      r'contrato': contrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<AulaTurma>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/app/consultarTurmasDisponiveis',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AulaTurma>> consultarTurmasAgendada(
    matricula,
    aulasFuturas,
    contrato,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
      r'contrato': contrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<AulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/consultarAulas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<Map<String, String>> consultarSaldoDoUsuario({
    required matricula,
    idContrato,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'contrato': idContrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<Map<String, String>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/saldoAlunoReporEMarcar',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!.cast<String, String>();
    return value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaAula({
    required codigoAula,
    codigoContratoMarcacao,
    required aulaExperimental,
    required matricula,
    required dia,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'contrato': codigoContratoMarcacao,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<MarcacaoAulaTurma>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/aula/{chave}/marcarPresencaOrigem?origem=APP_TREINO',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = MarcacaoAulaTurma.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AulaTurmaSimplificado> marcarPresencaTodosAlunosNaAula(
    horarioAulaId,
    empresaId,
    dia,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'dia': dia};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AulaTurmaSimplificado>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/agenda/turmas/${horarioAulaId}/confirmar-todas-presencas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AulaTurmaSimplificado.fromJson(_result.data!);
    return value;
  }

  @override
  Future<MarcacaoAulaTurma> professorMarcarPresenca(
    codigoAula,
    aulaExperimental,
    matricula,
    dia,
    codUsuario,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
      r'codUsuario': codUsuario,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<MarcacaoAulaTurma>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/aula/{chave}/marcarPresenca?origem=APP_TREINO',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = MarcacaoAulaTurma.fromJson(_result.data!);
    return value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaTurma(
    matricula,
    data,
    codigoHorarioTurma,
    codigoContrato,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'data': data,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'contrato': codigoContrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<MarcacaoAulaTurma>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/marcarAula',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = MarcacaoAulaTurma.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> desmarcarPresencaAula(
    codigoHorarioTurma,
    matricula,
    data,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'matricula': matricula,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/aula/{chave}/desmarcarAula',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> desmarcarPresencaTurma(
    matricula,
    data,
    codigoHorarioTurma,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'data': data,
      r'codigoHorarioTurma': codigoHorarioTurma,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/alunoTurma/{chave}/desmarcarAula',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> marcarEuQueroAula(
    codigoAluno,
    codigoHorarioTurma,
    data,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAluno': codigoAluno,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/alunoTurma/{chave}/marcarEuQueroHorario',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> desmarcarEuQueroAula(
    codigoAluno,
    codigoHorarioTurma,
    data,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAluno': codigoAluno,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/alunoTurma/{chave}/desmarcarEuQueroHorario',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosTurma(
    codigoHorarioTurma,
    data,
    codigoEmpresa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
      r'empresa': codigoEmpresa,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlunoNaAulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/alunosTurma',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetiva(
    codigoAula,
    dia,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'dia': dia,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlunoNaAulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/aula/{chave}/consultarAlunosDeUmaAula',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasTurmasProfessor(
    empresa,
    dia,
    codColaborador,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'empresa': empresa,
      r'dia': dia,
      r'codColaborador': codColaborador,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<AulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/aula/{chave}/consultarAulasTurmaProfessorDia',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> confirmarAlunoAula(
    tipoAula,
    codigoAula,
    cliente,
    dia,
    usuario,
    matricula,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'horarioTurma': codigoAula,
      r'cliente': cliente,
      r'dia': dia,
      r'usuario': usuario,
      r'matricula': matricula,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/aula/{chave}/${tipoAula}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<AulaTurmaSimplificado> desmarcarPresencaAulaTurma(
    horarioAulaId,
    empresaId,
    dia,
    matricula,
    justificativa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dia': dia,
      r'matricula': matricula,
      r'justificativa': justificativa,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AulaTurmaSimplificado>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
      contentType: 'application/x-www-form-urlencoded',
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/agenda/turmas/${horarioAulaId}/desmarcar-aluno',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AulaTurmaSimplificado.fromJson(_result.data!);
    return value;
  }

  @override
  Future<dynamic> isAlunoNaAula({
    urlTreino,
    required codigoAula,
    required chaveUnidade,
    required matricula,
    required dia,
    required chaveOrigem,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'matricula': matricula,
      r'dia': dia,
      r'chaveOrigem': chaveOrigem,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '${urlTreino}/prest/aula/${chaveUnidade}/confirmarAlunoEmAula',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> inserirNaFila({
    required codigoAula,
    required dia,
    required codigoAluno,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoAula,
      r'dia': dia,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/aula/{chave}/inserirNaFila',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> removerNaFila({
    required codigoHorarioTurma,
    required dia,
    required codigoAluno,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'dia': dia,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/aula/{chave}/v2/removerDaFila',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<dynamic> professorRemoverDaFila({
    required codigoHorarioTurma,
    required dia,
    required codigoUsuario,
    required codigoAluno,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'dia': dia,
      r'codigoUsuario': codigoUsuario,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/aulas/removerDaFilaV2',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<List<AlunoFilaDeEspera>> consultarAlunosNaFila(
    codAula,
    dia,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlunoFilaDeEspera>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/aulas/consultar-fila-espera/${codAula}/${dia}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            AlunoFilaDeEspera.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<UrlDiscorver> descobrirURLTreino({required chaveUnidade}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'key': chaveUnidade};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UrlDiscorver>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{DESCOVERY}/find',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = UrlDiscorver.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasColetivasURLpersonalizada({
    required urlTreino,
    required chaveEmpresa,
    required chaveOrigem,
    required dia,
    required codigoEmpresa,
    required matricula,
    required aulasFuturas,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveOrigem': chaveOrigem,
      r'dia': dia,
      r'empresa': codigoEmpresa,
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<AulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '${urlTreino}/prest/aula/${chaveEmpresa}/consultarAulas',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetivaURLpersonalizada({
    required urlTreino,
    required chaveEmpresa,
    required codigoAula,
    required dia,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'dia': dia,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<AlunoNaAulaTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '${urlTreino}/prest/aula/${chaveEmpresa}/consultarAlunosDeUmaAula',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> desmarcarPresencaAulaURLpersonalizada({
    required urlTreino,
    required chaveEmpresa,
    required chaveOrigem,
    required codigoHorarioTurma,
    required matricula,
    required data,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveOrigem': chaveOrigem,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'matricula': matricula,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '${urlTreino}/prest/aula/${chaveEmpresa}/desmarcarAula',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaAulaURLpersonalizada({
    required urlTreino,
    required chaveEmpresa,
    required chaveAluno,
    codigoContratoMarcacao,
    codigoAula,
    required aulaExperimental,
    required matricula,
    required dia,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveAluno': chaveAluno,
      r'contrato': codigoContratoMarcacao,
      r'codigoAula': codigoAula,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<MarcacaoAulaTurma>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '${urlTreino}/prest/aula/${chaveEmpresa}/marcarPresencaOrigem?origem=APP_TREINO',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = MarcacaoAulaTurma.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<HistoricoAula>> consultarHistoricoAulas(
    matricula,
    dataInicial,
    dataFinal,
    page,
    size,
    contrato,
    empresaId,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataInicio': dataInicial,
      r'dataFim': dataFinal,
      r'page': page,
      r'size': size,
      r'contrato': contrato,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<HistoricoAula>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/programa/{chave}/aulasAgendadasPorAluno/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => HistoricoAula.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> reservarEquipamento({
    required horarioTurmaId,
    required dia,
    required empresaId,
    required equipamento,
    required usuarioId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'equipamento': equipamento,
      r'usuarioId': usuarioId,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/agendamento/{chave}/equipamento/${horarioTurmaId}/${dia}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> removerReservaEquipamento({
    required horarioTurmaId,
    required dia,
    required empresaId,
    required equipamento,
    required usuarioId,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'equipamento': equipamento,
      r'usuarioId': usuarioId,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/agendamento/{chave}/equipamento/${horarioTurmaId}/${dia}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
