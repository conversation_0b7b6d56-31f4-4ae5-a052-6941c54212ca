// import 'package:dio/dio.dart';
// import 'package:app_treino/ServiceProvider/ApiCache.dart';

// class ApiCacheInterceptor extends Interceptor {
//   // Cache em memória simples - em produção, considere usar shared_preferences ou hive
//   final Map<String, CachedResponse> _cache = {};

//   @override
//   void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
//     // Verifica se a requisição tem a anotação @Apicache
//     if (Apicache.containsHeader(options)) {
//       final cacheKey = _generateCacheKey(options);
//       final cachedResponse = _cache[cacheKey];
      
//       if (cachedResponse != null && !cachedResponse.isExpired()) {
//         // Retorna a resposta do cache
//         final response = Response(
//           requestOptions: options,
//           data: cachedResponse.data,
//           statusCode: cachedResponse.statusCode,
//           statusMessage: cachedResponse.statusMessage,
//           headers: Headers.fromMap(cachedResponse.headers),
//         );
//         return handler.resolve(response);
//       }
//     }
    
//     // Continua com a requisição normal
//     handler.next(options);
//   }

//   @override
//   void onResponse(Response response, ResponseInterceptorHandler handler) {
//     // Verifica se a requisição tinha a anotação @Apicache
//     if (Apicache.containsHeader(response.requestOptions)) {
//       final cacheKey = _generateCacheKey(response.requestOptions);
      
//       // Determina a duração do cache baseada na anotação
//       // Como não conseguimos acessar o valor do enum diretamente dos headers,
//       // vamos usar um valor padrão ou implementar uma lógica adicional
//       final cacheDuration = _getCacheDurationFromHeaders(response.requestOptions);
      
//       // Armazena a resposta no cache
//       _cache[cacheKey] = CachedResponse(
//         data: response.data,
//         statusCode: response.statusCode ?? 200,
//         statusMessage: response.statusMessage,
//         headers: response.headers.map,
//         expiresAt: DateTime.now().add(cacheDuration),
//       );
//     }
    
//     handler.next(response);
//   }

//   Duration _getCacheDurationFromHeaders(RequestOptions options) {
//     // Tenta obter a duração do cache dos metadados
//     final duration = Apicache.getCacheDuration(options);
//     return duration ?? const Duration(days: 1); // Valor padrão se não encontrar
//   }

//   String _generateCacheKey(RequestOptions options) {
//     // Gera uma chave única baseada na URL, método e parâmetros
//     final uri = options.uri.toString();
//     final method = options.method;
//     final queryParams = options.queryParameters.toString();
//     final data = options.data?.toString() ?? '';
    
//     return '$method:$uri:$queryParams:$data';
//   }

//   void clearCache() {
//     _cache.clear();
//   }

//   void removeCacheEntry(String key) {
//     _cache.remove(key);
//   }
// }

// class CachedResponse {
//   final dynamic data;
//   final int statusCode;
//   final String? statusMessage;
//   final Map<String, List<String>> headers;
//   final DateTime expiresAt;

//   CachedResponse({
//     required this.data,
//     required this.statusCode,
//     this.statusMessage,
//     required this.headers,
//     required this.expiresAt,
//   });

//   bool isExpired() {
//     return DateTime.now().isAfter(expiresAt);
//   }
// }
