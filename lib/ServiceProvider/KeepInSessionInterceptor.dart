import 'package:retrofit/http.dart' as retrofit;
import 'package:dio/dio.dart';
import 'dart:developer' as log;

class KeppSessionCacheInterceptor extends Interceptor {
  Map<String, Response> cache = {};
  Set<String> pendingRequests = {};

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final cacheKey = options.uri.toString();
    if (KeppCacheInSession.containsHeader(options)) {
      if (cache.containsKey(cacheKey)) {
        final cachedResponse = cache[cacheKey];
        if (cachedResponse != null) {
          cachedResponse.headers.add('veioDoCache', 'true');
          handler.resolve(cachedResponse);
          return;
        }
      } else if (pendingRequests.contains(cacheKey)) {
        final stopwatch = Stopwatch()..start();
        while (pendingRequests.contains(cacheKey)) {
          if (stopwatch.elapsed > const Duration(seconds: 15)) {
            break;
          }
          await Future.delayed(const Duration(milliseconds: 500));
        }
        stopwatch.stop();

        if (cache.containsKey(cacheKey)) {
          final cachedResponse = cache[cacheKey];
          if (cachedResponse != null) {
            cachedResponse.headers.add('veioDoCache', 'true');
            handler.resolve(cachedResponse);
            return;
          }
        }
      }
      pendingRequests.add(cacheKey);
    }
    handler.next(options);
  }

  @override
  Future<void> onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (KeppCacheInSession.containsHeader(response.requestOptions)) {
      final cacheKey = response.requestOptions.uri.toString();
      try {
        response.headers.add('veioDoCache', 'false');
        cache[cacheKey] = response;
      } catch (e) {
        log.log('Erro ao adicionar ao cache: $e');
      } finally {
        pendingRequests.remove(cacheKey);
      }
    }
    handler.next(response);
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    if (KeppCacheInSession.containsHeader(err.requestOptions)) {
      pendingRequests.remove(err.requestOptions.uri.toString());
    }
    handler.next(err);
  }
}

class KeppCacheInSession extends retrofit.Headers {
  static const Map<String, dynamic> headers = {'keepsession': 'true'};
  static bool containsHeader(RequestOptions request) {
    return request.headers.containsKey(headers.keys.first);
  }

  const KeppCacheInSession() : super(headers);
}
