import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/homefit/models.dart';
import 'package:app_treino/model/live/TreinoAoVivo.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'TreinoSimples.service.g.dart';

@RestApi(baseUrl: '')
abstract class TreinoSimplesService {
  factory TreinoSimplesService(Dio dio, {String? baseUrl}) = _TreinoSimplesService;

  @DELETE('${ConfigURL.FIREBASE}/treinoSimples/deletarUmaLive')
  Future<String> deletarLive(@Query('documentKey') String liveId);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/listarLives')
  Future<List<TreinoAoVivo>> consultarLives(@Query('clienteApp') String clienteApp, @Query('refUsuario') String refUsuario);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.FIREBASE}/treinoSimples/manterLive')
  Future<TreinoAoVivo> manterLive(@Body() Map<String, dynamic> map);

  // Treino simples
  @DELETE('${ConfigURL.FIREBASE}/treinoSimples/excluirTreinoSimples')
  Future<String> excluirTreinoSimples(@Query('treinoKey') String treinoKey);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/getInfoProfessor')
  Future<UsuarioApp> getInfoProfessor(@Query('usuarioApp') String usuarioApp);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.FIREBASE}/treinoSimples/salvarTreinoSimples')
  Future<Treino> salvarTreinoSimples(@Body() Map<String, dynamic> map);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/consultarQuemFezOTreino')
  Future<List<ExecucaoTreino>> consultarQuemFezOTreino(@Query('treinoKey') String treinoKey);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/rankingTreinoSimples')
  Future<List<RankingInfo>> consultarRankingTreinoSimples(@Query('clienteApp') String clienteApp);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/consultarTreinosSimples')
  Future<List<Treino>> consultarTreinosSimples(@Query('clienteApp') String clienteApp, @Query('refUsuario') String refUsuario);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.FIREBASE}/treinoSimples/salvarExecucao')
  Future<ExecucaoTreino> salvarExecucao(@Query('treinoKey') String treinoKey, @Query('refUsuario') String userId, @Query('dataExec') num dataExec);

  @Headers(<String, dynamic>{'Content-Type': 'text/plain'})
  @POST('${ConfigURL.FIREBASE}/treinoSimples/uploadImagem')
  Future<String> uploadImagem(@Body() String base64Image);

  // Filtros
  @DELETE('${ConfigURL.FIREBASE}/treinoSimples/manterFiltro')
  Future<String> excluirFiltro(@Query('refClienteApp') String clienteApp, @Body() Map<String, dynamic> map);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/listarFiltros')
  Future<List<Filtro>> listarFiltros(@Query('refClienteApp') String clienteApp);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.FIREBASE}/treinoSimples/manterFiltro')
  Future<Filtro> salvarFiltro(@Query('refClienteApp') String clienteApp, @Body() Map<String, dynamic> map);

  @GET('${ConfigURL.FIREBASE}/treinoSimples/listarVideosAjuda')
  Future<List<VideoAjuda>> listarVideosAjuda();

  @GET('${ConfigURL.URLZW}/app/prest/validarProdutoHomeFit')
  Future<ProdutoHomeFit> validarSeAcademiaVendeHomeFit(@Query('chave') String chave, @Query('empresa') num empresa, @Query('codCliente') num codCliente);

}
