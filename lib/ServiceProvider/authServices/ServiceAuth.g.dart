// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ServiceAuth.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _ServiceAuth implements ServiceAuth {
  _ServiceAuth(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<ResultCentralDeAjuda> getCentralDeAjuda() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ResultCentralDeAjuda>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://pactosolucoes.com.br/ajuda/kb/login/json',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ResultCentralDeAjuda.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ResponsePreviusUsers> veificarUsuariosPorDeviceID(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ResponsePreviusUsers>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/veificarUsuariosPorDeviceID',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ResponsePreviusUsers.fromJson(_result.data!);
    return value;
  }

  @override
  Future<TokenAutenticacao> consultarAuth(token) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(token);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<TokenAutenticacao>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TOKENWEB}/aut/gt/',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = TokenAutenticacao.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BodyLinkLogin> validarCodigoLinkLogin(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BodyLinkLogin>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/validarCodigoLinkLogin',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BodyLinkLogin.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BodyLinkLogin> solicitarLinkDeLogin(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BodyLinkLogin>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/solicitarLinkDeLogin',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BodyLinkLogin.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Usuario> loginSenha(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{
      r'limiteTimeout': 'true',
      r'encrypted': 'true',
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Usuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/vA6fM2oW4uY8tS4aA4bZ6tM2uA0tV8dB',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Usuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BodyAuthSocial> usuarioVinculadoApple(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BodyAuthSocial>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/usuarioVinculadoApple',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BodyAuthSocial.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BodyAuthSocial> vincularUsuariosSocial(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BodyAuthSocial>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/vincularUsuariosSocial',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BodyAuthSocial.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Usuario> loginRedeSocial(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{
      r'limiteTimeout': 'true',
      r'encrypted': 'true',
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Usuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Usuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<Usuario> loginRedeSocialV2(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{
      r'limiteTimeout': 'true',
      r'encrypted': 'true',
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result =
        await _dio.fetch<Map<String, dynamic>>(_setStreamType<Usuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/v2/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = Usuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> gerarTokenAuthColaboradorV3(dadosUsuario) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(dadosUsuario);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/login/v3/app',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> enviarLembreteSenha(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/usuario/{chave}/rO2aS2tZ5jJ2aM8hE3dZ3pH1jY4lG1mM',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> gerarUsuarioTR(
    email,
    senha, {
    codigoCliente,
    codigoColaborador,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'email': email,
      r'senha': senha,
      r'codigoCliente': codigoCliente,
      r'codigoColaborador': codigoColaborador,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{OAMD}/prest/usuarioapp/{chave}/gerarUsuarioTR',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarFotoPerfil(
    email,
    imagem, {
    matricula,
    usuario,
    atualizaFotoZW,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'email': email,
      r'matricula': matricula,
      r'usuario': usuario,
      r'atualizaFotoZW': atualizaFotoZW,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(imagem);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/usuario/{chave}/alterarFotoUsuarioAPP',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<UserAuthFirebase> pegaInfoAuthFirebase(bodyAuth) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyAuth);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UserAuthFirebase>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/authUser/auth',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = UserAuthFirebase.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> manterUsuarioFirebase(
    bodyAuth, {
    personalMantendo = false,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'personalMantendo': personalMantendo
    };
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyAuth);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/authUser/manterUsuario',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<UsuarioAppTelefone>> consultarPeloNumeroTelefone(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<UsuarioAppTelefone>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{OAMD}/prest/empresa/{chave}/iY7pJ3sO6sT0rA0nR3wS8jQ2yL8iO2qX',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            UsuarioAppTelefone.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<UsuarioAppTelefone>> consultarPeloEmailOuUsername(
      bodyAuth) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyAuth.toJson());
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<UsuarioAppTelefone>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{OAMD}/prest/empresa/{chave}/wV9zD7rW1yJ6tI4zG4nX7fT9oL2fG4fJ',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            UsuarioAppTelefone.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<DadosUsuario> consultarDadosUsuario(username) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'username': username};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<DadosUsuario>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/dados',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DadosUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<UsuarioAppTelefone>> consultarPeloNumeroCPF(cpf) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'cpf': cpf};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<UsuarioAppTelefone>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarClienteJson',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            UsuarioAppTelefone.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<DadosDoUsuario> consultarDadosCliente(matricula) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'matricula': matricula};
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<DadosDoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarDadosCliente',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DadosDoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BandeirasConvenio> obterBandeirasConvenio(matricula) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'matricula': matricula};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BandeirasConvenio>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/negociacao/{chave}/obterBandeirasConvenio',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BandeirasConvenio.fromJson(_result.data!);
    return value;
  }

  @override
  Future<List<PontuacaoAluno>> obterPontosPorCliente(
    matricula,
    analitico,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'analitico': analitico,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PontuacaoAluno>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/historicoPontosAluno',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PontuacaoAluno.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<Brindes>> obterDadosBrinde({ativo = true}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'ativo': ativo};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<Brindes>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/brindes',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => Brindes.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> incluirAutorizacaoCobrancaCartaoCredito(
    cliente,
    operadoraCartao,
    validadeCartao,
    convenio,
    numeroCartao,
    cvv,
    titularCartao,
    usarConfConvEmpresa,
    origemCobrancaEnum,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cliente': cliente,
      r'operadoraCartao': operadoraCartao,
      r'validadeCartao': validadeCartao,
      r'convenioCobranca': convenio,
      r'numeroCartao': numeroCartao,
      r'cvv': cvv,
      r'titularCartao': titularCartao,
      r'usarConfConvEmpresa': usarConfConvEmpresa,
      r'origemCobrancaEnum': origemCobrancaEnum,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/negociacao/{chave}/incluirAutorizacaoCobranca',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarTelefoneUsuario(
    codigoTelefone,
    tipo,
    numero,
    descricao,
    excluir,
    matricula,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigo': codigoTelefone,
      r'tipo': tipo,
      r'numero': numero,
      r'descricao': descricao,
      r'excluir': excluir,
      r'matricula': matricula,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/alterarTelefone',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarEmailUsuario(
    codigoEmail,
    email,
    emailCorrespondencia,
    excluir,
    matricula,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigo': codigoEmail,
      r'email': email,
      r'emailCorrespondencia': emailCorrespondencia,
      r'excluir': excluir,
      r'matricula': matricula,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/alterarEmail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarSenhaUsuario(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/usuario/{chave}/v2/bQ3jI3pM1zT1aB7mE3gC3zO6nC3nW1pF',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarEnderecoUsuario(
    codigoEndereco,
    tipo,
    numero,
    cep,
    excluir,
    enderecoCorrespondencia,
    complemento,
    bairro,
    endereco,
    matricula,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigo': codigoEndereco,
      r'tipo': tipo,
      r'numero': numero,
      r'cep': cep,
      r'excluir': excluir,
      r'enderecoCorrespondencia': enderecoCorrespondencia,
      r'complemento': complemento,
      r'bairro': bairro,
      r'endereco': endereco,
      r'matricula': matricula,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/alterarEndereco',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<NotificacaoCRM> consultarNotificacoes(
    username, [
    matricula,
  ]) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'username': username,
      r'matricula': matricula,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<NotificacaoCRM>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/notificacoes/{chave}/get',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = NotificacaoCRM.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> responderNotificacao(
    idNotificacao,
    resposta,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'idNotf': idNotificacao,
      r'reposta': resposta,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/notificacoes/{chave}/respostaNotificacao',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> marcarNotificacaoCrmLida(
    username,
    idNotificacao,
    marcarTodas, [
    matricula,
  ]) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'username': username,
      r'idNotificacao': idNotificacao,
      r'marcarTodas': marcarTodas,
      r'matricula': matricula,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/notificacoes/{chave}/marcarLida',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> gerarTokenSMSNovo(
    msg,
    nomeApp,
    telefone,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'msg': msg,
      r'idRemetente': nomeApp,
      r'telefone': telefone,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{OAMD}/prest/token/aae06a9469a47e5b58da769ec6041af0/gerar?tipoChavePrimaria=0&tipoEnvioToken=SMS&chavePrimaria=1&email=',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<SituacaoCliente> consultarSituacaoAluno(matricula) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{
      r'limiteTimeout': 'true',
      r'keepsession': 'true',
    };
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<SituacaoCliente>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{PERSONAGEM}/clientes/{chave}/situacao/${matricula}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = SituacaoCliente.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> gerarTokenAuthColaborador(dadosUsuario) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(dadosUsuario);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/login/app',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<PerfilAcesso> validarToken(empresaId) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<PerfilAcesso>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/validateToken',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = PerfilAcesso.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> notificarRecursoEmpresa(recursoEmpresa) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(recursoEmpresa.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{OAMD}/prest/recursoEmpresa/notificar',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<String>> consultarRedesSuspeitas() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result =
        await _dio.fetch<List<dynamic>>(_setStreamType<List<String>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://app.pactosolucoes.com.br/login/prest/session/getSuspectNets',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!.cast<String>();
    return value;
  }

  @override
  Future<List<PushUsuario>> consultarPushs(
    chave,
    userNameUsuario,
    codUsuario,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chave': chave,
      r'userNameUsuario': userNameUsuario,
      r'codUsuario': codUsuario,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<PushUsuario>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/consultarPushs',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => PushUsuario.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> marcaPushComoLida(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/usuario/marcarPushComoLida',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<RetornoUsoApp> registarUsoAppTreinoWeb(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = body.map((e) => e.toJson()).toList();
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<RetornoUsoApp>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/usuario/{chave}/registrar-uso-app',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RetornoUsoApp.fromJson(_result.data!);
    return value;
  }

  @override
  Future<DevModeCodigo> obterPasseDevMode(email) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(email);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<DevModeCodigo>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/usuario/obterPaseDeUsoDevMove',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = DevModeCodigo.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ReponseContent<List<LocalDeAcesso>>> listarPontosDeAcesso(
      empresa) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'empresa': empresa};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<ReponseContent<List<LocalDeAcesso>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{ACESSOMS}/locaisAcesso/find-all',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ReponseContent<List<LocalDeAcesso>>.fromJson(
      _result.data!,
      (json) => (json as List<dynamic>)
          .map<LocalDeAcesso>(
              (i) => LocalDeAcesso.fromJson(i as Map<String, dynamic>))
          .toList(),
    );
    return value;
  }

  @override
  Future<String> aceitarTermosAplicativo({
    required cpf,
    required data,
    required ip,
    required nome,
    required termo,
    required email,
    required codigoMatricula,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cpf': cpf,
      r'data': data,
      r'ip': ip,
      r'nome': nome,
      r'termo': termo,
      r'email': email,
      r'codigoMatricula': codigoMatricula,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/termo-aceite/assinatura',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<TermoDeAceite>> buscarTermosDeAceite() async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<TermoDeAceite>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/psec/termo-aceite',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => TermoDeAceite.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> salvarTermosAceite(termo) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'termo': termo};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/termo-aceite',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<dynamic> consultarAssinaturaTermos(codigoMatricula) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/psec/termo-aceite/assinaturas/${codigoMatricula}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<AcessoRegistrado> registrarAcesso(
    key,
    operacao,
    empresa,
    codigo,
    dataAcesso,
    direcao,
    meioIdentificacao,
    situacao,
    tipo,
    usuario,
    app,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'key': key,
      r'operacao': operacao,
      r'empresa': empresa,
      r'codigo': codigo,
      r'dataAcesso': dataAcesso,
      r'direcao': direcao,
      r'meioIdentificacao': meioIdentificacao,
      r'situacao': situacao,
      r'tipo': tipo,
      r'usuario': usuario,
      r'app': app,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<AcessoRegistrado>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{URLZW}/app/prest/registraracesso',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AcessoRegistrado.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AutLoginMs> loginMs(dadosUsuario) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'limiteTimeout': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(dadosUsuario);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<AutLoginMs>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://auth.ms.pactosolucoes.com.br/aut/login',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AutLoginMs.fromJson(_result.data!);
    return value;
  }

  @override
  Future<HistoricoPresenca> obterHistoricoPresenca({
    required matricula,
    required empresa,
    required atualizaCache,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'empresa': empresa,
      r'atualizaCache': atualizaCache,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<HistoricoPresenca>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/historico-presenca',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = HistoricoPresenca.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> enviarContatoEmailOtp(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/aluno/enviarContatoOtp',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> validarContatoEmailOtp(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{FIREBASE}/aluno/validarContatoOtp',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
