import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/avaliarProfessor/ProfessorAvaliar.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/doClienteApp/AlunoNaAcademia.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/SituacaoCliente.dart';
import 'package:app_treino/model/geral/AvaliacaoNPS.dart';
import 'package:app_treino/model/graduacao/NivelAlunoGraduacao.dart';
import 'package:app_treino/model/manterExercicio/AtividadeManter.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/personal/BashBoardTreinoIdependente.dart';
import 'package:app_treino/model/personal/CadastroAluno.dart';
import 'package:app_treino/model/personal/CadastroColaborador.dart';
import 'package:app_treino/model/personal/ConfiguracoesBaseFicha.dart';
import 'package:app_treino/model/personal/EventoTimeLineAluno.dart';
import 'package:app_treino/model/personal/InfoIndicacoesPersonal.dart';
import 'package:app_treino/model/personal/Notificacao.dart';
import 'package:app_treino/model/personal/ObjectConsultaAluno.dart';
import 'package:app_treino/model/personal/ProfTreinoWeb.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/personal/ProgramaTreinoPerfilAluno.dart';
import 'package:app_treino/model/personal/ReportExecucao.dart';
import 'package:app_treino/model/personal/ReportSatisfacao.dart';
import 'package:app_treino/model/personal/cadastroProfessorTreinoWeb.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'PersonalService.g.dart';

@RestApi(baseUrl: '')
abstract class PersonalService {
  factory PersonalService(Dio dio, {String? baseUrl}) = _PersonalService;

  @PUT('${ConfigURL.TREINO}/prest/usuario/{chave}/personal/{idPersonal}')
  Future<CadastroColaborador> salvarPersonal(@Path('idPersonal') num idAluno, @Body() Map<String, dynamic> body);

  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/simplificado')
  Future<CadastroAluno> salvarAluno(@Body() Map<String, dynamic> body);

  @PUT('${ConfigURL.TREINO}/prest/cliente/{chave}/simplificado/{idAluno}')
  Future<CadastroAluno> manterAluno(@Path('idAluno') num idAluno, @Body() Map<String, dynamic> body);

  @GET('${ConfigURL.TREINO}/prest/personal/{chave}/notas')
  Future<List<ReportSatisfacao>> resultadoNivelSatisfacao();

  @GET('${ConfigURL.TREINO}/prest/personal/{chave}/execucoes')
  Future<List<ReportExecucao>> resultadoExecucoes();

  @GET('${ConfigURL.TREINO}/prest/ficha/{chave}/app/consultarConfiguracaoFichaApp')
  Future<ConfiguracoesBaseFicha> consultarObjetivos();

//   @GET("${ConfigURL.TREINO}/prest/usuario/{chave}/dash")
//   Future<DashBoardTreinoIdependente> consultarDashBoardFiti();

  @GET('${ConfigURL.TREINO}/prest/gestao/{chave}/app/consultarDashColaborador')
  Future<DashBoardTreinoIdependente> consultarDashBoardFiti(@Query('codigoProfessor') num codigoProfessor, @Query('empresa') num empresa);

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/dash')
  Future<DashboardNovoTreino> consultarDashboardNovoTreino(@Header('empresaId') num id, @Query('idProfessor') num idProfessor, @Query('idPessoa') num idPessoa);

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/atualizar')
  Future<DashboardNovoTreino> atualizarDashboardNovoTreino(@Header('empresaId') num id, @Query('idProfessor') num idProfessor, @Query('codigoPessoa') num codigoPessoa);

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-treino-vencido/{cod}')
  @FormUrlEncoded()
  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  Future<List<AlunoSimplificadoCarteira>> treinosVencidosPorProfessor(
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );

  @PUT('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-sem-acompanhamento/{codigoPessoa}')
  Future<dynamic> contabilizarAcompanhar(@Header('empresaId') num id, @Path('codigoPessoa') num codigoPessoa);

  @PUT('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-com-acompanhamento/{codigoPessoa}/{programaId}/{fichaId}')
  Future<dynamic> finalizarAcompanhar(@Header('empresaId') num id, @Path('codigoPessoa') num codigoPessoa, @Path('programaId') num programaId, @Path('fichaId') num fichaId);

  @POST('${ConfigURL.TREINO}/prest/psec/treino-bi/iniciar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}')
  Future<dynamic> contabilizarAcompanharV2(@Header('empresaId') num id, @Path('codigoCliente') num codigoCliente, @Path('codigoProfessor') num codigoProfessor);

  @PUT('${ConfigURL.TREINO}/prest/psec/treino-bi/finalizar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}/{programaId}/{fichaId}')
  Future<dynamic> finalizarAcompanharV2(@Header('empresaId') num id, @Path('codigoCliente') num codigoCliente, @Path('codigoProfessor') num codigoProfessor, @Path('programaId') num programaId, @Path('fichaId') num fichaId);

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-treino-a-renovar/{cod}')
  @FormUrlEncoded()
  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  Future<List<AlunoSimplificadoCarteira>> treinosAVencerPorProfessor(    
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('size') num size,
     @Header('empresaId') num empresaId
  );

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-treino-em-dia/{cod}')
  @FormUrlEncoded()
  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  Future<List<AlunoSimplificadoCarteira>> treinosEmDiaPorProfessor(
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('size') num size,
    @Header('empresaId') num empresaId    
  );

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/lista-alunos-vencer-30-dias/{cod}')
  @FormUrlEncoded()
  Future<List<AlunoSimplificadoCarteiraAVencer>> contratoAvencer(
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('page') num page,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-ativos/{cod}')
  @FormUrlEncoded()
  Future<List<AlunoSimplificadoCarteira>> contratoAtivo(
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );

  @GET('${ConfigURL.TREINO}/prest/psec/treino-bi/alunos-inativos/{cod}')
  @FormUrlEncoded()
  Future<List<AlunoSimplificadoCarteira>> contratoInativo(
    @Path('cod') num cod,
    @Query('filters') String filters,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );

  @GET('${ConfigURL.TREINO}/prest/psec/avaliacao-fisica-bi')
  Future<BiAvaliacao> consultarDashAvaliacao(@Header('empresaId') num id, @Query('dataInicio') num dataInicio, @Query('dataFim') num dataFim, @Query('codigoProfessor') num codigoProfessor);

//   @POST("${ConfigURL.TREINO}/prest/psec/grafico-bi/views")
//   Future<ResponseCriacaoBiAvaliacao> criarBiTreinoWebDoProfessor(@Body() Map<String,dynamic> map);

  @GET('${ConfigURL.FIREBASE}/manter/avaliacaoProfessorApp?chave={chave}')
  Future<AvaliacaoProfessorDash> consultarAvaliacoes(@Query('codigoProfessor') num codigoProfessor);

  @GET('${ConfigURL.ALCIDES}/?key={chave}')
  Future<InfoIndicacoesPersonal> consultarInfoIndicacoes();

  @GET('${ConfigURL.TREINO}/prest/usuario/{chave}/app/v2/consultarAlunosColaborador')
  Future<List<AlunoDeProfessor>> consultarAlunos(
    @Query('statusAluno') num statusAluno,
    @Query('codUsuario') num codUsuario,
    @Query('empresa') num empresa,
    @Query('max') num max, {
    @Query('porNome') String? nomePesquisa,
  });
  
  
  @GET('${ConfigURL.TREINO}/prest/psec/alunos')
  Future<ResponseAlunosPaginado> consultarAlunosTreino(
    @Query('filters') String filters,
    @Query('configs') String configs,
    @Query('page') num page,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );
  
  
  @GET('${ConfigURL.TREINO}/prest/psec/alunos/alunoColaborador')
  Future<List<ContentAlunoAcademia>> consultarAlunosColaborador(
    @Query('carteiras') bool carteiras,
    @Query('empresas') bool empresas,
    @Query('filters') String filters,
    @Query('configs') String configs,
    @Query('page') num page,
    @Query('size') num size,
    @Header('empresaId') num empresaId
  );

  
  @GET('${ConfigURL.TREINO}/prest/psec/avaliacoes-fisica/alunos/{id}/avaliacao-recente')
  Future<AvaliacaoFisicaRecente> consultarAvaliacaoFisicaRecente(@Query('empresaId') num empresaId, @Path('id') num id);

  @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/app/consultarQuantidadeAcessosClientesAgrupadosDia')
  Future<AcessosSemanalAluno> consultarAcessos(@Query('username') String username, @Query('codigoAluno') num codigoAluno, @Query('dataInicial') num dataInicial, @Query('dataFinal') num dataFinal);

  @GET('${ConfigURL.TREINO}/prest/programa/{chave}/app/consultarHistoricoExecs')
  Future<List<HistoricoExecucaoAluno>> consultarHistoricoExecucoes(@Query('codigoCliente') num codAluno, @Query('index') num index, @Query('maxResults') num maxResult);

  // @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/app/consultarObservacoesDeAluno')
  // Future<List<ObservacaoAlunoColaborador>> consultarObservacoesAluno(@Query('matriculaAluno') num matricula);

    @POST('${ConfigURL.ADMCORE}/cliente-observacao')
  Future<ObservacaoAlunoColaborador> gravarObservacao(@Body()  Map<String, dynamic> observacao);

  @DELETE('${ConfigURL.ADMCORE}/cliente-observacao/{codigoObservacao}')
  Future<dynamic> removerObservacao(@Path('codigoObservacao') num codigoObservacao);

  @GET('${ConfigURL.TREINO}/prest/programa/{chave}/app/consultarProgramaBase')
  Future<ProgramaDeTreino> consultarProgramaBase(@Query('codigoCliente') num codigo, @Query('userName') String userName);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/app/renovarPrograma')
  Future<String> renovarPrograma(@Query('username') String username, @Query('empresa') num empresa, @Query('codigoPrograma') num codigoPrograma);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/app/revisaoPrograma')
  Future<String> revisarPrograma(@Query('empresa') num empresa, @Query('username') String username, @Query('justificativa') String justificativa, @Query('codigoPrograma') num codigoPrograma);

  // @GET("prest/gestao/{chave}/app/consultarDashColaborador")
  // Future<> consultarDashBoard(@Query("codigoProfessor") codigoProfessor: Number?, @Query("empresa") empresa: Number?); DashColaborador

  @GET('${ConfigURL.TREINO}/prest/programa/{chave}/app/consultarUltimoPrograma')
  Future<ProgramaDeTreino> consultarUltimoProgramaDoAluno(@Query('userName') String usernameProfessor, @Query('codigoAluno') num codigoAluno);

  @POST('${ConfigURL.TREINO}/prest/programa/{chave}/app/persistirPrograma')
  Future<ProgramaFicha> manterPrograma(@Body() Map<String, dynamic> programa);

  @GET('${ConfigURL.TREINO}/prest/ficha/{chave}/app/getFicha')
  Future<ProgramaFicha> consultarDadosDaFicha(@Query('userName') String usernameProfessor, @Query('codigoFicha') num codigoFicha);

  @GET('${ConfigURL.TREINO}/prest/ficha/{chave}/app/consultarConfiguracaoFichaApp')
  Future<ConfiguracoesBaseFicha> consultarInfoBasePrograma();

  @GET('${ConfigURL.TREINO}/prest/ficha/{chave}/app/consultarFichasPreDefinidas')
  Future<List<ProgramaFicha>> consultarFichasPreDefinidas({@Query('userName') String? userName, @Query('max') num? max, @Query('nomeFicha') String? nomeFicha, @Query('codigoProfessor') num? codigoProfessor});

  @DELETE('${ConfigURL.TREINO}/prest/ficha/{chave}/app/deletarFicha?username={user}&codigoDaFicha={codigo}')
  Future<String> deletarFichaDeUmPrograma(@Path('user') String username, @Path('codigo') num codigoDaFicha);

  @POST('${ConfigURL.TREINO}/prest/ficha/{chave}/excluir')
  Future<String> deletarTreinoModelo(@Query('username') String username, @Query('codigoFicha') num codigoFicha);

  @POST('${ConfigURL.TREINO}/prest/ficha/{chave}/app/persistirFicha')
  Future<ProgramaFicha> persistirFicha(@Body() Map<String, dynamic> ficha, @Query('username') String usernameProfessor);

  @POST('${ConfigURL.TREINO}/prest/ficha/{chave}/app/persistirFichaPredefinida')
  Future<ProgramaFicha> persistirFichaPreDef(@Body() Map<String, dynamic> ficha, @Query('username') String usernameProfessor);

  @POST('${ConfigURL.TREINO}/prest/ficha/{chave}/app/consultarProgramaDeAlunosAlunos')
  Future<List<ProgramaSimplesAluno>> consultarProgramasBaisocosAluno(@Body() List<dynamic> alunos, @Query('username') String usernameProfessor);

  @GET('${ConfigURL.TREINO}/prest/atividades/{chave}/app/consultarTodasAtividades?crossfit=false')
  Future<List<AtividadesFicha>> consultarTodasAtividades();

  @POST('${ConfigURL.FIREBASE}/manter/avaliarClienteApp')
  Future<AvaliacaoNPS> avaliarClienteApp(@Body() Map<String, dynamic> avaliacao);

  @GET('${ConfigURL.FIREBASE}/personalFit/consultarNotificacoesApp?clienteApp={chave}')
  Future<List<NotificacaoPersonal>> consultarNotificacaoPersonal({@Query('dataUltimoItem') num? dataUltimoItem});

  @POST('${ConfigURL.FIREBASE}/personalFit/registrarComoLida?clienteApp={chave}')
  Future<String> marcarNotificacoesComoLidaPersonal();

  @POST('${ConfigURL.TREINO}/prest/atividades/{chave}')
  Future<AtividadeBase> manterAtividade(@Body() Map<String, dynamic> atividade);

  @GET('${ConfigURL.TREINO}/prest/atividades/{chave}/personal/consultarTodasAtividades')
  Future<List<AtividadeBase>> consultarAtividadeBase();

  @GET('${ConfigURL.TREINO}/prest/atividades/{chave}/imagens-catalogo')
  Future<List<ImagemBase>> consultarCatalogoDeImagens();

  @GET('${ConfigURL.TREINO}/prest/atividades/{chave}/grupos-musculares')
  Future<List<GruposMuscular>> consultarGruposMusculares();

  @POST('${ConfigURL.FIREBASE}/personalFit/registrarNotificaoPush?clienteApp={chave}')
  Future<String> registrarPushPersonal(@Body() Map<String, dynamic> push);

  @GET('${ConfigURL.TREINO}/prest/gestao/{chave}/obterTodosProfessores')
  Future<List<ProfessorAvaliar>> consultarTodosProfessores(@Query('cliente') num id);

  @PATCH('${ConfigURL.FIREBASE}/manter/avaliarProfessor')
  Future<ProfessorNota> avaliarProfessor(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.MOCK}/facial/gravar')
  Future<RegistroFacialSucesso> registrarFace(@Body() Map<String, dynamic> json);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/consultarCodAcessoAluno')
  Future<String> consultarCodAcesso({@Query('matricula') required num matricula});
  
  @GET('${ConfigURL.TREINO}/prest/psec/colaboradores')
  Future<List<ProfessorAvaliar>> consultarColaboradorTreinoWeb(@Header('empresaId') num id);

  @GET('https://us-central1-personalfit-55234.cloudfunctions.net/usuario/consultarStatusEduzz')
  Future<SituacaoEduzz> situacaoEduzz(@Query('email') String user);

  @GET('${ConfigURL.ADMCORE}/cliente-observacao/by-matricula/{codigoMatricula}?filters={}&configs={}&page={page}&size={size}')
  Future<List<ObservacaoAluno>> consultarObservacoesAluno(@Path('codigoMatricula') num codigoMatricula,{@Path('page') required num page,@Path('size') required num size});

  @GET('https://graduacao.ms.pactosolucoes.com.br/fichas/{codigoCliente}/fichasaluno')
  Future<List<NivelAlunoGraduacao>> consultarGraduacaoAluno({@Path('codigoCliente') required num codigoCliente, @Query('key') required String chaveZW});

  @GET('https://graduacao.ms.pactosolucoes.com.br/avaliacoes-progresso')
  Future<List<AvaliacaoProgresso>> consultarAvaliacaoProgresso({@Query('alunoId') required num alunoId, @Query('fichaId') required num fichaId});

  @GET('https://graduacao.ms.pactosolucoes.com.br/niveis/{codigoCliente}/aulas/{matriculaZW}/{idNivel}')
  Future<num> consultarQtdeAulasFeitasNoNivel({@Path('codigoCliente') required num codigoCliente, @Path('matriculaZW') required num matriculaZW, @Path('idNivel') required num idNivel});

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/linha-tempo/{idAluno}')
  Future<List<EventoTimeLineAluno>> consultarTimeLineAluno({@Query('dataInicio') required num dataInicio, @Query('dataFim') required num dataFim, @Query('tiposEvento') required String tiposEvento, @Path('idAluno') required num idAluno});

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/linha-tempo/{idAluno}')
  @FormUrlEncoded()
  Future<List<EventoTimeLineAluno>> consultarTimeLineAlunoComFiltro({@Query('dataInicio') required num dataInicio, @Query('dataFim') required num dataFim, 
  @Query('tiposEvento') required List<String> tiposEvento, @Path('idAluno') required num idAluno, @Header('empresaId') required num empresaId});

  @GET('${ConfigURL.FIREBASE}/usuario/listarAtividadeExtras')
  Future<List<ExercicioTreinoExtraFirebase>> listarAtividadeExtras(@Query('refUser') String usuarioApp);

  @POST('${ConfigURL.FIREBASE}/usuario/inserirAtividadeExtras')
  Future<List<ExercicioTreinoExtraFirebase>> inserirAtividadeExtras(@Query('refUser') String usuarioApp, @Body() Map<String, dynamic> body);

  @DELETE('${ConfigURL.FIREBASE}/usuario/deletarAtividadeExtra')
  Future<String> deletarAtividadeExtras(@Query('refUser') String usuarioApp, @Query('atividadeId') String atividadeId);

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/{codigoAluno}')
  @FormUrlEncoded()
  Future<Aluno> consultarAlunoDetalhado({ @Path('codigoAluno') required num codigoAluno, @Header('empresaId') required num empresaId });

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/obter-aluno-completo-por-matricula/{matricula}')
  @FormUrlEncoded()
  Future<Aluno> consultarAlunoDetalhadoMatricula({ @Path('matricula') required num matricula, @Header('empresaId') required num empresaId });

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/perfil/programa-treino/{idAluno}')
  Future<ProgramaTreinoPerfilAluno> consultarProgramaTreinoPerfilAluno({@Path('idAluno') required num idAluno, @Header('empresaId') required num idEmpresa});

  @GET('${ConfigURL.TREINO}/prest/psec/colaboradores/{colaborador}')
  Future<SituacaoCliente> consultarSituacaoProfessor({@Path('colaborador') required num colaborador, @Header('empresaId') required num idEmpresa});

  @POST('${ConfigURL.TREINO}/prest/psec/programas?renovarAtual=true')
  Future<dynamic> renovarProgramaDeTreinoAtual ({ @Body() required Map<String, dynamic> objetoComIdAluno, @Header('empresaId') required num idEmpresa });
   
   @GET('${ConfigURL.TREINO}/prest/psec/colaboradores/professores-dados-basicos/{cod}')
   @FormUrlEncoded()
   Future<List<ProfTreinoWeb>> consultarProfessorTreinoWeb({
   @Path('cod') required num cod,
   @Query('page') required int page,
   @Query('size') required int size,
   @Query('filters') required String filters,
   @Header('empresaId') required num empresaId,
  });
   
   
   @PUT('${ConfigURL.TREINO}/prest/psec/alunos/{idAluno}')
   Future<CadastroProfessorTreinoWeb> cadastrarProfessorTreinoWeb({
   @Path('idAluno') required num idAluno,
   @Header('empresaId') required num empresaId,
   @Body() required Map<String, dynamic> body
  });



}
