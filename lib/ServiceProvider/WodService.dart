import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/wod/WorkoutOfDay.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'WodService.g.dart';

@RestApi(baseUrl: '')
abstract class WodService {
  factory WodService(Dio dio, {String? baseUrl}) = _WodService;

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/wods')
  Future<List<WorkoutOfDay>> consultarWodsUrlAntigo(@Query('usuario') num usuario, @Query('inicio') String dataInicio, @Query('fim') String dataFim);

  @GET('${ConfigURL.TREINO}/prest/crossfit/{chave}/v2/wods')
  Future<List<WorkoutOfDay>> consultarWods(@Query('usuario') num usuario, @Query('inicio') String dataInicio, @Query('fim') String dataFim,@Query('empresaCodigoZw') num empresaCodigoZw);

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/ranking')
  Future<List<SpotRankingWod>> obterRanking(@Query('wod') num codigoWod);

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/gravarResultadoAPP')
  Future<SpotRankingWod> gravarResultado(@Query('wod') num codigoWod, @Query('usuario') num usuario,
      {@Query('rounds') num? rounds, @Query('tempo') num? tempo, @Query('repeticoes') num? repeticoes, @Query('peso') num? peso, @Query('comentario') String? comentario, @Query('nivel') num? nivel});

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/obterAtividadesCrossFit')
  Future<List<AtividadeWod>> obterAtividadesCross();

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/obterAparelhosCrossFit')
  Future<List<AparelhoWod>> obterAparelhosCrossfit();

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/tipoWod')
  Future<List<TipoWodTabela>> obterTiposDeWod();

  @FormUrlEncoded()
  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/cadastrarWod')
  Future<WorkoutOfDay> manterWodOnline(@Field('dadosWodJSON') String dadosWodJSONString);

  @FormUrlEncoded()
  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/cadastrarWod')
  Future<String> excluirWod(@Field('dadosWodJSON') String dadosWodJSONString);

  @POST('${ConfigURL.TREINO}/prest/crossfit/{chave}/avaliarWod')
  Future<dynamic> avaliarWod(@Body() AvaliarWod avaliar);

  @GET('${ConfigURL.TREINO}/prest/crossfit/{chave}/avaliacoesWod')
  Future<List<AvaliarWod>> consultarAvaliacoesWod();

  @GET('${ConfigURL.TREINO}/prest/crossfit/{chave}/listaNivelWod')
  Future<List<WodNiveis>> consultarNiveisWod();
}
