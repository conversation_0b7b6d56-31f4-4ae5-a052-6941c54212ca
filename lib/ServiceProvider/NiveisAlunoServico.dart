
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/NivelAluno.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'NiveisAlunoServico.g.dart';

@RestApi(baseUrl: '')
abstract class Niveisalunoservico {
  factory Niveisalunoservico(Dio dio, {String? baseUrl}) = _Niveisalunoservico;

  @GET('${ConfigURL.TREINO}/prest/psec/niveis?page=0&size=50&filters=%7B%22situacoes%22:%5B%22ATIVO%22%5D,%22quicksearchValue%22:null,%22quicksearchFields%22:%5B%22nome%22%5D%7D')
  Future<List<NivelAluno>> obterNivel({@Header('empresaId') required num empresaId});

  @PUT('${ConfigURL.TREINO}/prest/psec/alunos/nivel/{matricula}')
  Future<dynamic> editarNivel({@Header('empresaId') required num empresaId, @Body() required Map<String,num> nivel,@Path('matricula') required num matricula});

}
