
import 'package:app_treino/ServiceProvider/ServiceDioProvider.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

/// Trata erros de autorização.
///
/// Tenta regenerar o token de autenticação para colaboradores ou usuários,
/// retransmitindo o erro para o interceptor após o processo.
class TokenDioInterceptor extends Interceptor {
  final Dio _dio;
  String _accessToken = '';
  final Response<dynamic> Function(Response origina, Function(Response done) resonseDone) responseHandler;
  final Function(DioException e, ErrorInterceptorHandler handler) rejectHandler;
  TokenDioInterceptor(this._dio, {required this.responseHandler, required this.rejectHandler});

  @override
  Future onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      //   _isRefreshing = true;
      try {
        final newToken = await fetchNewToken(request: err.requestOptions);
        if (newToken != null) {
          _accessToken = newToken;
          final requestOptions = err.requestOptions;
          requestOptions.headers['Authorization'] = 'Bearer $_accessToken';

          final retryResponse = await _dio.fetch(requestOptions);
          handler.resolve(handleResponse(retryResponse));
          //   return handler.resolve(this.responseHandler(retryResponse));
        }
      } catch (e) {
        try {
          rejectHandler(err, handler);
        } catch (e) {}
      } finally {
        // _isRefreshing = false;
      }
    }
    if (err.response?.statusCode != 401) {
      return super.onError(err, handler);
    }
  }

  static Future<String?> fetchNewToken({RequestOptions? request}) async {
    final controladorCliente = GetIt.I.get<ControladorCliente>();
    final controladorSplash = GetIt.I.get<ControladorSplash>();
    final controladorApp = GetIt.I.get<ControladorApp>();

    if (controladorCliente.isUsuarioColaborador) {
      controladorSplash.currentTokenPsec = await controladorSplash.gerarTokenUsuarioLogado((x) => {}) ?? '';
    } else if (controladorApp.userRelogin != null || controladorCliente.mUsuarioLogado != null) {
      controladorSplash.currentTokenPsec = await controladorSplash.gerarTokenUsuarioLogado((x) => {}, relogin: controladorApp.userRelogin != null) ?? '';
    } else if (controladorCliente.mUsuarioLogado == null) {
      controladorSplash.currentToken = await controladorSplash.pegaTokenAgora((x) => {}) ?? '';
    }

    if (request?.uri.toString().contains('prest/psec/') ?? false) {
      return controladorSplash.currentTokenPsec;
    }

    if (controladorSplash.currentToken.isEmpty) {
      controladorSplash.currentToken = await controladorSplash.pegaTokenAgora((x) => {}) ?? '';
    }

    return controladorSplash.currentToken;
  }
}
