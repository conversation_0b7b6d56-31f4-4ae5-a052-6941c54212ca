import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/aulaTurma/AlunoFilaDeEspera.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/doClienteApp/UrlDiscorver.dart';
import 'package:app_treino/model/doClienteApp/historico_aula.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;
part 'AulaTurmaService.g.dart';

@RestApi(baseUrl: '')
abstract class AulaTurmaService {
  factory AulaTurmaService(Dio dio, {String? baseUrl}) = _AulaTurmaService;

  @FormUrlEncoded()
  @GET('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/saldo-aulas-coletivas')
  Future<SaldoAulaColetiva> consultarSaldoAulasColetivas({@Query('matricula') required num matricula});
  
  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/consultarAulas')
  Future<List<AulaTurma>> consultarAulasColetivas({@Query('dia') required String dia, @Query('empresa') required num codigoEmpresa, @Query('matricula') required num matricula, @Query('aulasFuturas') required bool aulasFuturas});

  @GET('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/app/consultarTurmasDisponiveis')
  Future<List<AulaTurma>> consultarTurmasDoAluno(@Query('matricula') num matricula, @Query('inicio') String inicio, @Query('fim') String fim, @Query('contrato') num? contrato);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/consultarAulas')
  Future<List<AulaTurma>> consultarTurmasAgendada(@Query('matricula') String matricula, @Query('aulasFuturas') bool aulasFuturas, @Query('contrato') num? contrato);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/saldoAlunoReporEMarcar')
  Future<Map<String,String>> consultarSaldoDoUsuario({ @Query('matricula') required num matricula, @Query('contrato') num? idContrato });

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/marcarPresencaOrigem?origem=APP_TREINO')
  Future<MarcacaoAulaTurma> marcarPresencaAula({@Query('codigoAula') required num codigoAula, @Query('contrato') num? codigoContratoMarcacao, @Query('aulaExperimental') required bool aulaExperimental, @Query('matricula') required String matricula, @Query('dia') required String dia});

  @FormUrlEncoded()
  @PUT('${ConfigURL.TREINO}/prest/psec/agenda/turmas/{horarioAulaId}/confirmar-todas-presencas')
  Future<AulaTurmaSimplificado> marcarPresencaTodosAlunosNaAula(@Path('horarioAulaId') String horarioAulaId, @Header('empresaId') num empresaId, @Query('dia') num dia);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/marcarPresenca?origem=APP_TREINO')
  Future<MarcacaoAulaTurma> professorMarcarPresenca(@Query('codigoAula') num codigoAula, @Query('aulaExperimental') bool aulaExperimental, @Query('matricula') String matricula, @Query('dia') String dia, @Query('codUsuario') num codUsuario);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/marcarAula')
  Future<MarcacaoAulaTurma> marcarPresencaTurma(@Query('matricula') String matricula, @Query('data') String data, @Query('codigoHorarioTurma') num codigoHorarioTurma, @Query('contrato') num? codigoContrato);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/desmarcarAula')
  Future<String> desmarcarPresencaAula(@Query('codigoHorarioTurma') num codigoHorarioTurma, @Query('matricula') String matricula, @Query('data') String data);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/desmarcarAula')
  Future<String> desmarcarPresencaTurma(@Query('matricula') String matricula, @Query('data') String data, @Query('codigoHorarioTurma') num codigoHorarioTurma);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/marcarEuQueroHorario')
  Future<String> marcarEuQueroAula(@Query('codigoAluno') num codigoAluno, @Query('codigoHorarioTurma') num codigoHorarioTurma, @Query('data') String data);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/desmarcarEuQueroHorario')
  Future<String> desmarcarEuQueroAula(@Query('codigoAluno') num codigoAluno, @Query('codigoHorarioTurma') num codigoHorarioTurma, @Query('data') String data);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/alunosTurma')
  Future<List<AlunoNaAulaTurma>> consultarAlunosTurma(@Query('codigoHorarioTurma') num codigoHorarioTurma, @Query('data') String data, @Query('empresa') num codigoEmpresa);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/consultarAlunosDeUmaAula')
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetiva(@Query('codigoAula') num codigoAula, @Query('dia') String dia);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/consultarAulasTurmaProfessorDia')
  Future<List<AulaTurma>> consultarAulasTurmasProfessor(@Query('empresa') num empresa, @Query('dia') String dia, @Query('codColaborador') num codColaborador);

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/{tipoDeMatricula}')
  Future<String> confirmarAlunoAula(
  @Path('tipoDeMatricula') String tipoAula, @Query('horarioTurma') num codigoAula, @Query('cliente') num cliente, @Query('dia') String dia, @Query('usuario') num usuario, @Query('matricula') num matricula);

  @FormUrlEncoded()
  @DELETE('${ConfigURL.TREINO}/prest/psec/agenda/turmas/{horarioAulaId}/desmarcar-aluno')
  Future<AulaTurmaSimplificado> desmarcarPresencaAulaTurma(@Path('horarioAulaId') String horarioAulaId, @Header('empresaId') num empresaId, @Query('dia') num dia, @Query('matricula') num matricula, @Query('justificativa') String justificativa);

  @POST('{urlTreino}/prest/aula/{chaveUnidade}/confirmarAlunoEmAula')
  Future<dynamic> isAlunoNaAula({ @Path('urlTreino') String? urlTreino, @Query('codigoAula') required num codigoAula, @Path('chaveUnidade') required String chaveUnidade, @Query('matricula') required num matricula, @Query('dia') required String dia, @Query('chaveOrigem') required String chaveOrigem});

  @POST('${ConfigURL.TREINO}/prest/aula/{chave}/inserirNaFila')
  Future<dynamic> inserirNaFila({ @Query('codigoHorarioTurma') required num codigoAula, @Query('dia') required String dia, @Query('codigoAluno') required num codigoAluno});

  @DELETE('${ConfigURL.TREINO}/prest/aula/{chave}/v2/removerDaFila')
  Future<dynamic> removerNaFila({@Query('codigoHorarioTurma') required num codigoHorarioTurma, @Query('dia') required String dia, @Query('codigoAluno') required num codigoAluno});

  @DELETE('${ConfigURL.TREINO}/prest/psec/aulas/removerDaFilaV2')
  Future<dynamic> professorRemoverDaFila({@Query('codigoHorarioTurma') required num codigoHorarioTurma, @Query('dia') required String dia, @Query('codigoUsuario') required num codigoUsuario, @Query('codigoAluno') required num codigoAluno});

  @GET('${ConfigURL.TREINO}/prest/psec/aulas/consultar-fila-espera/{codAula}/{dia}')
  Future<List<AlunoFilaDeEspera>> consultarAlunosNaFila(@Path('codAula') num codAula, @Path('dia') String dia);

  @GET('${ConfigURL.DESCOVERY}/find')
  Future<UrlDiscorver> descobrirURLTreino({@Query('key') required String chaveUnidade});

  @POST('{urlTreino}/prest/aula/{chaveEmpresa}/consultarAulas')
  Future<List<AulaTurma>> consultarAulasColetivasURLpersonalizada({@Path('urlTreino') required String urlTreino, @Path('chaveEmpresa') required String chaveEmpresa, @Query('chaveOrigem') required String chaveOrigem, @Query('dia') required String dia, @Query('empresa') required num codigoEmpresa, @Query('matricula') required num matricula, @Query('aulasFuturas') required bool aulasFuturas});
  
  @POST('{urlTreino}/prest/aula/{chaveEmpresa}/consultarAlunosDeUmaAula')
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetivaURLpersonalizada({@Path('urlTreino') required String urlTreino, @Path('chaveEmpresa') required String chaveEmpresa, @Query('codigoAula') required num codigoAula, @Query('dia') required String dia});

  @POST('{urlTreino}/prest/aula/{chaveEmpresa}/desmarcarAula')
  Future<String> desmarcarPresencaAulaURLpersonalizada({@Path('urlTreino') required String urlTreino, @Path('chaveEmpresa') required String chaveEmpresa, @Query('chaveOrigem') required String chaveOrigem, @Query('codigoHorarioTurma') required num codigoHorarioTurma, @Query('matricula') required String matricula, @Query('data') required String data});

  @POST('{urlTreino}/prest/aula/{chaveEmpresa}/marcarPresencaOrigem?origem=APP_TREINO')
  Future<MarcacaoAulaTurma> marcarPresencaAulaURLpersonalizada({@Path('urlTreino') required String urlTreino, @Path('chaveEmpresa') required String chaveEmpresa, @Query('chaveAluno') required String chaveAluno, @Query('contrato') num? codigoContratoMarcacao, @Query('codigoAula') num? codigoAula, @Query('aulaExperimental') required bool aulaExperimental, @Query('matricula') required String matricula, @Query('dia') required String dia});

   @GET('${ConfigURL.TREINO}/prest/programa/{chave}/aulasAgendadasPorAluno/{matricula}')
   Future<List<HistoricoAula>> consultarHistoricoAulas(@Path('matricula') num matricula, @Query('dataInicio') String dataInicial, @Query('dataFim') String dataFinal,@Query('page') num page,@Query('size') num size, @Query('contrato') num contrato, @Header('empresaId') num empresaId);

   @POST('${ConfigURL.TREINO}/prest/agendamento/{chave}/equipamento/{horarioTurmaId}/{dia}')
   Future<String> reservarEquipamento({@Path('horarioTurmaId') required String horarioTurmaId, @Path('dia') required String dia, @Header('empresaId') required num empresaId, @Query('equipamento') required String equipamento,  @Query('usuarioId') required num usuarioId});

   @DELETE('${ConfigURL.TREINO}/prest/agendamento/{chave}/equipamento/{horarioTurmaId}/{dia}')
   Future<String> removerReservaEquipamento({@Path('horarioTurmaId') required String horarioTurmaId, @Path('dia') required String dia, @Header('empresaId') required num empresaId, @Query('equipamento') required String equipamento,  @Query('usuarioId') required num usuarioId});
}
