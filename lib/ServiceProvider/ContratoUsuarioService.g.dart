// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ContratoUsuarioService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _ContratoUsuarioService implements ContratoUsuarioService {
  _ContratoUsuarioService(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<List<ContratoUsuario>> consultarContratos(
    codigoCliente,
    numRegistros,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cliente': codigoCliente,
      r'registros': numRegistros,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ContratoUsuario>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarContratos',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ContratoUsuario.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ParcelaContrato>> consultarParcelas(
    codigoCliente,
    numRegistros,
    pesquisarApenasParcelasEmAberto,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cliente': codigoCliente,
      r'registros': numRegistros,
      r'emAberto': pesquisarApenasParcelasEmAberto,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ParcelaContrato>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarParcelasCliente',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) => ParcelaContrato.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<List<ContratoOperacao>> obterContratoOperacao(codigoContrato) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'contrato': codigoContrato};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ContratoOperacao>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/obterContratoOperacao',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map(
            (dynamic i) => ContratoOperacao.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<CarenciaOuTrancamento> consultarTrancamento(codigoContrato) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'contrato': codigoContrato};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<CarenciaOuTrancamento>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarDadosOperacao?tipoOperacao=TR',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CarenciaOuTrancamento.fromJson(_result.data!);
    return value;
  }

  @override
  Future<CarenciaOuTrancamento> consultarCarencia(codigoContrato) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'contrato': codigoContrato};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<CarenciaOuTrancamento>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/consultarDadosOperacao?tipoOperacao=CR',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CarenciaOuTrancamento.fromJson(_result.data!);
    return value;
  }

  @override
  Future<ContratoUsuario> renovarContrato(
    contrato,
    simulacao,
    cliente,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'contrato': contrato,
      r'simulacao': simulacao,
      r'cliente': cliente,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ContratoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/negociacao/{chave}/renovarContrato',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ContratoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<OperacaoValidacao> validarDadosOperacaoContrato(
    codigoContrato,
    tipoOperacao,
    dataInicio,
    dataFinal,
    produto,
    justificativa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'contrato': codigoContrato,
      r'tipoOperacao': tipoOperacao,
      r'dataInicio': dataInicio,
      r'dataFinal': dataFinal,
      r'produto': produto,
      r'justificativa': justificativa,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<OperacaoValidacao>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/validarDadosOperacaoContrato',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = OperacaoValidacao.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> gravarAlteracaoVencimentoParcela({
    required dadosAlteracaoVencimentoParcela,
    operacao = 'TRANCAMENTO_CONTRATO_API',
    required empresa,
    required chave,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'operacao': operacao,
      r'empresa': empresa,
      r'chave': chave,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(dadosAlteracaoVencimentoParcela.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{URLZW}/app/prest/operacoescontratoservlet',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> cancelarContrato(
    body,
    codEmpresa,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'empresa': codEmpresa};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/importacao/{chave}/cancelarcontrato',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<ContratoUsuario> gravarDadosOperacaoContrato(
    codigoContrato,
    tipoOperacao,
    dataInicio,
    dataFinal,
    produto,
    justificativa,
    obs,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'contrato': codigoContrato,
      r'tipoOperacao': tipoOperacao,
      r'dataInicio': dataInicio,
      r'dataFinal': dataFinal,
      r'produto': produto,
      r'justificativa': justificativa,
      r'obs': obs,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<ContratoUsuario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{APIZW}/prest/cliente/{chave}/gravarDadosOperacaoContrato',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = ContratoUsuario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<String> obterDiasBonusContrato(codigoContrato) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'contrato': codigoContrato};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/negociacao/{chave}/obterDiasBonusContrato',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<List<ExtratoCreditoTurma>> consultarHistoricoDeUtilizacaoDeCredito(
    matricula,
    dataAPartirDe,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'data': dataAPartirDe,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<List<dynamic>>(_setStreamType<List<ExtratoCreditoTurma>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/alunoTurma/{chave}/extrato',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    var value = _result.data!
        .map((dynamic i) =>
            ExtratoCreditoTurma.fromJson(i as Map<String, dynamic>))
        .toList();
    return value;
  }

  @override
  Future<String> retornoTrancamentoFerias(
    codigoContrato,
    origemSistema,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'contrato': codigoContrato,
      r'origemSistema': origemSistema,
    };
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/retornoTrancamento',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> gerarBoletoParcelaEspecifica(
    empresa,
    matricula,
    movParcela,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/boleto/{chave}/${empresa}/${matricula}/todos/${movParcela}/1/0',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> consultarLinkConviteAmigo(
    empresa,
    matricula,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/gerarLinkConvite/${matricula}/${empresa}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> consultarContratoAluno(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/rB3mU7oX8eS4eA6iC4lH9eY2fU6sF5kR',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> consultarContratoAlunoPorContrato(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'encrypted': 'true'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/kC8zT9lL2xH6qG9wB0mP9vR1mM2bA7aE',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> inserirAssinaturaBase64Aditivo(
    empresa,
    contrato,
    aditivo,
    assinaturaBase64,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'Content-Type': 'text/plain'};
    _headers.removeWhere((k, v) => v == null);
    final _data = assinaturaBase64;
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'text/plain',
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/aluno-contrato-assinatura-digital-incluir/${contrato}/${aditivo}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> inserirAssinaturaBase64(
    empresa,
    contrato,
    assinaturaBase64,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'Content-Type': 'text/plain'};
    _headers.removeWhere((k, v) => v == null);
    final _data = assinaturaBase64;
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'text/plain',
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/aluno-contrato-assinatura-digital-incluir/${contrato}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> inserirAssinaturaBase64AditivoAutenticador({
    required empresa,
    required contrato,
    aditivo,
    required assinaturaDigitalRequest,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'Content-Type': 'application/json'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(assinaturaDigitalRequest.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'application/json',
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/v2/aluno-contrato-assinatura-digital-incluir/${contrato}/${aditivo}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> inserirAssinaturaBase64Autenticador({
    required empresa,
    required contrato,
    required assinaturaDigitalRequest,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'Content-Type': 'application/json'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(assinaturaDigitalRequest.toJson());
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'application/json',
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/v2/aluno-contrato-assinatura-digital-incluir/${contrato}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<String> alterarFotoAlunoZW(
    codigopessoa,
    fotoBase64,
  ) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'Content-Type': 'application/json'};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(fotoBase64);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'application/json',
    )
        .compose(
          _dio.options,
          '{APIZW}/prest/cliente/{chave}/v2/atualizarFotoCliente?codigopessoa=${codigopessoa}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<dynamic> validarTotalPass(objValidarTotalPass) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(objValidarTotalPass.toJson());
    final _result = await _dio.fetch(_setStreamType<dynamic>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '{TREINO}/prest/cliente/{chave}/autoriza-acesso-totalpass',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data;
    return value;
  }

  @override
  Future<String> enviarEmailComCodigo(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          'https://pacto-vendas.web.app/app/solicitarEmail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<RetornoEmailValidacao> validarEmailComCodigo(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<RetornoEmailValidacao>(Options(
      method: 'PATCH',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'https://pacto-vendas.web.app/app/validarCodiogEmail',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RetornoEmailValidacao.fromJson(_result.data!);
    return value;
  }

  @override
  Future<RetornoValidacaoIA> analisarImagemComDocumentoIA(body) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(body);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<RetornoValidacaoIA>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{FIREBASE}/aluno/analisarImagemIA',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RetornoValidacaoIA.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AlunoDestinatario> buscarAlunoParaTransferirCreditos({
    cpf,
    email,
    empresa,
  }) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'cpf': cpf,
      r'email': email,
      r'empresa': empresa,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<AlunoDestinatario>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/buscar-aluno-para-transferir-creditos',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = AlunoDestinatario.fromJson(_result.data!);
    return value;
  }

  @override
  Future<CreditoPermitidos> consultarQuantosCreditosPodeTransferir(
      {contratoId}) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'contrato_id': contratoId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<CreditoPermitidos>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/consultar-quantos-creditos-pode-transferir',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = CreditoPermitidos.fromJson(_result.data!);
    return value;
  }

  @override
  Future<RespostaTransferenciaCredito> transferirCreditos(
      transferenciaCredito) async {
    const _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(transferenciaCredito.toJson());
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<RespostaTransferenciaCredito>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '{TREINO}/prest/cliente/{chave}/transferir-creditos',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = RespostaTransferenciaCredito.fromJson(_result.data!);
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
