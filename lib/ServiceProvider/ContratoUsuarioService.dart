// ignore_for_file: prefer_single_quotes

import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/bodyscrypto.dart';
import 'package:app_treino/model/contrato/BodyCancelarContrato.dart';
import 'package:app_treino/model/contrato/ContratoOperacao.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/contrato/ExtratoCreditoTurma.dart';
import 'package:app_treino/model/contrato/OperacaoValidacao.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/contrato/CarenciaOuTrancamento.dart';
import 'package:app_treino/model/contrato/TransferenciaCredito.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/doClienteApp/ValidarCheckInTotalPass.dart';
import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

part 'ContratoUsuarioService.g.dart';

@RestApi(baseUrl: '')
abstract class ContratoUsuarioService {
  factory ContratoUsuarioService(Dio dio, {String? baseUrl}) = _ContratoUsuarioService;

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarContratos')
  Future<List<ContratoUsuario>> consultarContratos(@Query('cliente') num codigoCliente, @Query('registros') num numRegistros);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarParcelasCliente')
  Future<List<ParcelaContrato>> consultarParcelas(@Query('cliente') num codigoCliente, @Query('registros') num numRegistros, @Query('emAberto') bool pesquisarApenasParcelasEmAberto);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/obterContratoOperacao')
  Future<List<ContratoOperacao>> obterContratoOperacao(@Query('contrato') num codigoContrato);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarDadosOperacao?tipoOperacao=TR')
  Future<CarenciaOuTrancamento> consultarTrancamento(@Query('contrato') num codigoContrato);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarDadosOperacao?tipoOperacao=CR')
  Future<CarenciaOuTrancamento> consultarCarencia(@Query('contrato') num codigoContrato);

  @POST('${ConfigURL.APIZW}/prest/negociacao/{chave}/renovarContrato')
  Future<ContratoUsuario> renovarContrato(@Query('contrato') num contrato, @Query('simulacao') bool simulacao, @Query('cliente') num cliente, @Query('origemSistema') num origemSistema);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/validarDadosOperacaoContrato')
  Future<OperacaoValidacao> validarDadosOperacaoContrato(@Query('contrato') num codigoContrato, @Query('tipoOperacao') String tipoOperacao, @Query('dataInicio') String dataInicio,
      @Query('dataFinal') String dataFinal, @Query('produto') num produto, @Query('justificativa') num justificativa);

  @POST('${ConfigURL.URLZW}/app/prest/operacoescontratoservlet')
  Future<String> gravarAlteracaoVencimentoParcela({
    @Body() required DadosAlteracaoVencimentoParcela dadosAlteracaoVencimentoParcela,
    @Query('operacao') String operacao = 'TRANCAMENTO_CONTRATO_API',
    @Query('empresa') required num empresa,
    @Query('chave') required String chave,
  });
  
  @POST('${ConfigURL.APIZW}/prest/importacao/{chave}/cancelarcontrato')
  Future<String> cancelarContrato(
    @Body() BodyCancelarContrato body,
    @Query('empresa') int codEmpresa,
  );

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/gravarDadosOperacaoContrato')
  Future<ContratoUsuario> gravarDadosOperacaoContrato(@Query('contrato') num codigoContrato, @Query('tipoOperacao') String tipoOperacao, @Query('dataInicio') String dataInicio,
      @Query('dataFinal') String dataFinal, @Query('produto') num produto, @Query('justificativa') num justificativa, @Query('obs') String obs, @Query('origemSistema') num origemSistema);

  @POST('${ConfigURL.APIZW}/prest/negociacao/{chave}/obterDiasBonusContrato')
  Future<String> obterDiasBonusContrato(@Query('contrato') num codigoContrato);

  @POST('${ConfigURL.TREINO}/prest/alunoTurma/{chave}/extrato')
  Future<List<ExtratoCreditoTurma>> consultarHistoricoDeUtilizacaoDeCredito(@Query('matricula') String matricula, @Query('data') String dataAPartirDe); //dd/MM/yyyy hh:mm:ss

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/retornoTrancamento')
  Future<String> retornoTrancamentoFerias(@Query('contrato') num codigoContrato, @Query('origemSistema') num origemSistema);

  @GET('${ConfigURL.APIZW}/prest/boleto/{chave}/{empresa}/{matricula}/todos/{movParcela}/1/0')
  Future<String> gerarBoletoParcelaEspecifica(@Path('empresa') num empresa, @Path('matricula') num matricula, @Path('movParcela') num movParcela);

  @GET('${ConfigURL.APIZW}/prest/cliente/{chave}/gerarLinkConvite/{matricula}/{empresa}')
  Future<String> consultarLinkConviteAmigo(@Path('empresa') num empresa, @Path('matricula') num matricula);

  @Headers({"encrypted": "true"})
  @PATCH('${ConfigURL.TREINO}/prest/cliente/{chave}/rB3mU7oX8eS4eA6iC4lH9eY2fU6sF5kR')
  Future<String> consultarContratoAluno(@Body() BodyContratoAlunoCrypto body);
  @Headers({"encrypted": "true"})
  @PATCH('${ConfigURL.TREINO}/prest/cliente/{chave}/kC8zT9lL2xH6qG9wB0mP9vR1mM2bA7aE')
  Future<String> consultarContratoAlunoPorContrato(@Body() BodyConsultarContratoPorCodigoCrypto body);

  @Headers(<String, dynamic>{'Content-Type': 'text/plain'})
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/aluno-contrato-assinatura-digital-incluir/{contrato}/{aditivo}')
  Future<String> inserirAssinaturaBase64Aditivo(@Path('empresa') num empresa, @Path('contrato') num contrato, @Path('aditivo') num? aditivo, @Body() String assinaturaBase64);

  @Headers(<String, dynamic>{'Content-Type': 'text/plain'})
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/aluno-contrato-assinatura-digital-incluir/{contrato}')
  Future<String> inserirAssinaturaBase64(@Path('empresa') num empresa, @Path('contrato') num contrato,  @Body() String assinaturaBase64);

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/v2/aluno-contrato-assinatura-digital-incluir/{contrato}/{aditivo}')
  Future<String> inserirAssinaturaBase64AditivoAutenticador(
  {@Path('empresa') required num empresa, @Path('contrato') required num contrato, @Path('aditivo') num? aditivo, @Body() required AssinaturaDigitalRequest assinaturaDigitalRequest});

  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/v2/aluno-contrato-assinatura-digital-incluir/{contrato}')
  Future<String> inserirAssinaturaBase64Autenticador(
  {@Path('empresa') required num empresa, @Path('contrato') required num contrato, @Body() required AssinaturaDigitalRequest assinaturaDigitalRequest});
      
  @Headers(<String, dynamic>{'Content-Type': 'application/json'})
  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/v2/atualizarFotoCliente?codigopessoa={codigopessoa}')
  Future<String> alterarFotoAlunoZW(@Path('codigopessoa') num codigopessoa, @Body() Map<String, dynamic> fotoBase64);

  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/autoriza-acesso-totalpass')
  Future<dynamic> validarTotalPass(@Body() ObjValidarTotalPass objValidarTotalPass);

  @POST('https://pacto-vendas.web.app/app/solicitarEmail')
  Future<String> enviarEmailComCodigo(@Body() Map<String, dynamic> body);
  @PATCH('https://pacto-vendas.web.app/app/validarCodiogEmail')
  Future<RetornoEmailValidacao> validarEmailComCodigo(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.FIREBASE}/aluno/analisarImagemIA')
  Future<RetornoValidacaoIA> analisarImagemComDocumentoIA(@Body() Map<String, dynamic> body);
  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/buscar-aluno-para-transferir-creditos')
  Future<AlunoDestinatario> buscarAlunoParaTransferirCreditos({
    @Query('cpf') String? cpf,
    @Query('email') String? email,
    @Query('empresa') num? empresa,
  });

  @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/consultar-quantos-creditos-pode-transferir')
  Future<CreditoPermitidos> consultarQuantosCreditosPodeTransferir({@Query('contrato_id') num? contratoId});

  @POST('${ConfigURL.TREINO}/prest/cliente/{chave}/transferir-creditos')
  Future<RespostaTransferenciaCredito> transferirCreditos(@Body() TransferenciaCredito transferenciaCredito);
}