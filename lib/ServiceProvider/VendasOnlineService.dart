import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/vendasOnline/ConsultaDeCep.dart';
import 'package:app_treino/model/vendasOnline/PlanosUnidadeVendaOnline.dart';
import 'package:app_treino/model/vendasOnline/UnidadeVendaOnline.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'VendasOnlineService.g.dart';

@RestApi(baseUrl: '${ConfigURL.FIREBASE}/')
abstract class VendasOnlineService {
  factory VendasOnlineService(Dio dio, {String? baseUrl}) = _VendasOnlineService;

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/unidades/{chave}')
  Future<List<UnidadeVendaOnline>> getUnidadesEmpresa();

  @GET('${ConfigURL.APIZW}/prest/v2/vendas/{chaveUnidade}/planos/{codigoUnidade}')
  Future<List<PlanosUnidadeVendaOnline>> getPlanosUnidade(@Path('chaveUnidade') String chaveUnidade, @Path('codigoUnidade') int codigoUnidade);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/alunovendaapp/{token}')
  Future<String> submitIncluirPlano(@Path('token') String token, @Body() Map<String, String> venda);

  @GET('${ConfigURL.APIZW}/prest/cep/consultar')
  Future<ConsultaDeCep> getInfoCep(@Query('cep') String cep);

  @POST('${ConfigURL.APIZW}/prest/v2/vendas/{chave}/tkn/c2f0154d58b092c88d933ddfe1e0342d')
  Future<String> getTokenVenda();
}
