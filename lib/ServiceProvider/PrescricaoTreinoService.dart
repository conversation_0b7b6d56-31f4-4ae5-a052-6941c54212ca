import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/treinoAluno/ResponseTrocaAtividade.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
part 'PrescricaoTreinoService.g.dart';

@RestApi(baseUrl: '')
abstract class PrescricaoTreinoService {
  factory PrescricaoTreinoService(Dio dio, {String? baseUrl}) = _PrescricaoTreinoService;

  @GET('${ConfigURL.TREINO}/prest/psec/fichas/pre-definido')
  Future<List<Fichas>> consultarFichasPredefinidas(@Query('empresaId') num empresaId);

  @GET('${ConfigURL.TREINO}/prest/psec/fichas/pre-definido')
  Future<List<Fichas>> consultarFichasPredefinidasPorNome(@Query('empresaId') num empresaId, @Query('filters') String body);

  @GET('${ConfigURL.TREINO}/prest/psec/programas/pre-definido')
  Future<List<ProgramaTreino>> consultarProgramaTreinoPredefinido(@Query('empresaId') num empresaId, @Query('filters') String body);

  @GET('${ConfigURL.TREINO}/prest/psec/programas/pre-definido')
  Future<List<ProgramaTreino>> consultarProgramaTreinoPredefinidoPorNome(@Query('empresaId') num empresaId, @Query('filters') String body, @Query('size') num size, @Query('page') num pages);

  @PUT('${ConfigURL.TREINO}/prest/psec/fichas/{codigoFicha}')
  Future<Fichas> gravarFicha(@Path('codigoFicha') num codigoFicha, @Body() Map<String, dynamic> body);

  @PUT('${ConfigURL.TREINO}/prest/psec/fichas/{codigoFicha}/tornar-predefinida')
  @FormUrlEncoded()
  Future<bool> tornarFichaPredefinida(@Path('codigoFicha') num codigoFicha);

  @PUT('${ConfigURL.TREINO}/prest/psec/fichas/criarPredefinida')
  Future<Fichas> criarFichaPredefinida(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.TREINO}/prest/psec/programas/criar-predefinido')
  Future<ProgramaTreino> criarProgramaPreDefinido(@Query('empresaId') num empresaId, @Body() Map<String, dynamic> body);

  @Headers(<String, dynamic>{'empresaId': '{empresaId}'})
  @PUT('${ConfigURL.TREINO}/prest/psec/programas/{codPrograma}')
  Future<ProgramaTreino> gravarProgramaPredefinido(@Header('empresaId') num empresaId, @Path('codPrograma') num codPrograma, @Body() Map<String, dynamic> body);

  @PUT('${ConfigURL.TREINO}/prest/psec/programas/{codPrograma}/tornar-predefinido')
  @FormUrlEncoded()
  Future<bool> tornarProgramaPredefinido(@Path('codPrograma') num codPrograma);

  @Headers(<String, dynamic>{'empresaId': '{empresaId}'})
  @GET('${ConfigURL.TREINO}/prest/psec/atividades/montarTreino')
  Future<List<AtividadeFicha>> consultarAtividades(@Header('empresaId') num empresaId, @Query('filters') String body, @Query('size') num size, @Query('page') num pages);

  @POST('${ConfigURL.TREINO}/prest/psec/fichas')
  Future<Fichas> incluirFichaEmBrancoNoProgramaTreino(@Query('programaId') num programaId);

  
  @POST('${ConfigURL.TREINO}/prest/psec/fichas')
  Future<Fichas> incluirFichaNoProgramaTreino(
  @Query('empresaId') num empresaId, @Query('preDefinidoId') num preDefinidoId, @Query('programaId') num programaId, @Body() Map<String, dynamic> body);

  
  @POST('${ConfigURL.TREINO}/prest/psec/programas')
  Future<ProgramaTreino> incluirProgramaTreinoNoAluno(@Query('preDefinidoId') num preDefinidoId, @Body() Map<String, dynamic> body, @Header('empresaId') num empresaId);

  @POST('${ConfigURL.TREINO}/prest/psec/programas')
  Future<ProgramaTreino> criarProgramaTreinoVazio(@Header('empresaId') num empresaId, @Body() Map<String, dynamic> body);

  @DELETE('${ConfigURL.TREINO}/prest/psec/programas/{codPrograma}')
  @FormUrlEncoded()
  Future<ProgramaTreino> deletarProgramaTreino(@Path('codPrograma') num codPrograma);

  @DELETE('${ConfigURL.TREINO}/prest/psec/fichas/{codigoFicha}')
  @FormUrlEncoded()
  Future<bool> deletarFicha(@Path('codigoFicha') num codigoFicha);

  @GET('${ConfigURL.TREINO}/prest/psec/alunos/{codigoAluno}')
  @FormUrlEncoded()
  Future<Aluno> consultarAlunoDetalhado(@Path('codigoAluno') num codigoAluno, @Header('empresaId') num empresaId);

  @GET('${ConfigURL.TREINO}/prest/psec/programas/{codigoPrograma}')
  @FormUrlEncoded()
  Future<ProgramaTreino> consultarProgramaAluno(@Path('codigoPrograma') num codigoPrograma, @Header('empresaId') num empresaId);

  @GET('${ConfigURL.TREINO}/prest/psec/atividades/{codAtividade}')
  Future<ProgramaAtividadeReplace> consultarAtividadesAlternativas({
    @Path('codAtividade') required num codAtividade,
    @Header('Authorization') required String token,
    @Header('empresaId') required num codEmpresa,
  });

  @PUT('${ConfigURL.TREINO}/prest/atividades/{chave}/{codigoFicha}')
  Future<ResponseTrocaDeAtividade> trocarAtividadePorOutra({
    @Path('codigoFicha') required num codigoFicha,
    @Query('codAtividadeOriginal') required num codAtividadeOriginal,
    @Query('codAtividadeSubstituir') required num codAtividadeSubstituir,
  });
}
