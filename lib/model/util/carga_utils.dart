import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:get_it/get_it.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/PersonalRecord.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';

class CargaUtils {
	static String getDescricaoCarga(FichaAtividade atvFicha, int index) {
		final controladorWod = GetIt.I.get<ControladorWod>();
		final cargaStr = obterCargaSugerida(atvFicha, index);

    if(isBiOuTriSet(atvFicha)){
      return cargaStr;
    }

		String cargaStrFormatada = cargaStr.contains('/') ? cargaStr.split('/')[0].trim() : cargaStr;
    double cargaKg = double.tryParse(cargaStrFormatada.replaceAll(',', '.')) ?? 0.0;

		if (controladorWod.isLibra) {
			double cargaLbs = controladorWod.converterQuiloEmLibra(cargaKg);
			if ((cargaLbs - cargaLbs.round()).abs() < 0.05) {
				return cargaLbs.round().toString();
			}
			return cargaLbs.toStringAsFixed(1);
		} else {
			if ((cargaKg - cargaKg.round()).abs() < 0.05) {
				return cargaKg.round().toString();
			}
			return cargaKg.toStringAsFixed(1);
		}
	}

	static String obterCargaSugerida(FichaAtividade atvFicha, int index) {
		final cargaAtual = atvFicha.series?[index].cargaApp ?? '0';
		final codigoAtividade = atvFicha.codigoAtividade.toString();
		final codigoFicha = atvFicha.codFicha.toString();
		final codigoSerie = atvFicha.series?[index].codSerie.toString() ?? '';

		if (atvFicha.series?[index].isEditada ?? false) {
			return cargaAtual;
		}
    
		final prList = GetIt.I.get<ControladorWod>().getPersonalRecordsPorCodigoAtividade(codigoAtividade);
		final prListFiltrada = prList.where((pr) =>
			pr.codigoFicha == codigoFicha &&
			(pr.codigoSerie == codigoSerie || pr.codigoSerie == null || pr.codigoSerie!.isEmpty)
		).toList();

    final biOuTriSet = isBiOuTriSet(atvFicha);

		if (prListFiltrada.isEmpty || GetIt.I.get<ControladorApp>().getConfigWebTreino('MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA')) {
      return cargaAtual;
		}

		prListFiltrada.sort((a, b) {
			final dateA = DateTime.parse(a.dataInclusao ?? DateTime.now().toString());
			final dateB = DateTime.parse(b.dataInclusao ?? DateTime.now().toString());
			return dateB.compareTo(dateA);
		});

		PersonalRecords prMaisRecente = prListFiltrada.first;
		String cargaFormatadaPr = prMaisRecente.weight!.contains('/') ?
				prMaisRecente.weight!.split('/')[0].trim() :
				prMaisRecente.weight!;

		String cargaFormatada = cargaAtual.contains('/') ?
				cargaAtual.split('/')[1].trim() : '0';

		if (biOuTriSet) {
      return cargaFormatada == '0' ? cargaFormatadaPr : cargaFormatadaPr + '/' + cargaFormatada;
		} else {
			return prMaisRecente.weight ?? cargaAtual;
		}
	}
  

  static bool isBiOuTriSet(FichaAtividade atvFicha) {
	return atvFicha.nomeMetodoExecucao?.toUpperCase() == 'BI-SET' ||
		   atvFicha.nomeMetodoExecucao?.toUpperCase() == 'TRI-SET';
  }

	static String tratarValorCargaParaExibirNoText({required String valorCarga}) {
		var aposDecimal = valorCarga.toString().split('.');
		if (aposDecimal.length > 1 && (aposDecimal[1] == '0' || aposDecimal[1] == '00' || aposDecimal[1] == '000')) {
			return num.parse(valorCarga.toString()).toInt().toString();
		} else {
			return valorCarga.toString();
		}
	}
}
