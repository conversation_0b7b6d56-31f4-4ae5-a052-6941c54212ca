# Configuração CodeRabbit otimizada para projetos Flutter - Foco em Potential Issues
language: "pt-BR"
tone_instructions: "Seja direto e foque EXCLUSIVAMENTE em potential issues: bugs, vulnerabilidades de segurança, memory leaks, problemas de lifecycle, race conditions e problemas de performance. Ignore completamente estilo, formatação e sugestões de refatoração."

reviews:
  # Perfil assertivo para detectar mais potential issues
  profile: "chill"

  # Configurações otimizadas para focar apenas em potential issues críticos
  high_level_summary: false
  changed_files_summary: false
  sequence_diagrams: false
  poem: true
  suggested_labels: false
  estimate_code_review_effort: true
  assess_linked_issues: false
  related_issues: true
  related_prs: true
  suggested_reviewers: true
  collapse_walkthrough: true

  # Desabilitar funcionalidades que não são potential issues
  finishing_touches:
    docstrings:
      enabled: false
    unit_tests:
      enabled: false

  # Configurações de auto review para branches principais
  auto_review:
    enabled: true
    auto_incremental_review: true
    drafts: false
    base_branches:
      - master
      - main
      - development
      - develop

  # Filtros de caminho para ignorar arquivos gerados e desnecessários
  path_filters:
    - "!**/*.g.dart"
    - "!**/*.freezed.dart"
    - "!**/*.generated.dart"
    - "!**/*.mocks.dart"
    - "!**/l10n/*.dart"
    - "!**/generated_plugin_registrant.dart"
    - "!**/firebase_options.dart"
    - "!build/**"
    - "!.dart_tool/**"
    - "!ios/**"
    - "!android/**"
    - "!web/**"
    - "!macos/**"
    - "!windows/**"
    - "!linux/**"
    - "!.cert/**/*.json"
    - "!.cert/**/*.pem" 
    - "!**/*google-services.json"
    - "!**/*.p12"
    - "!**/strings.xml"  # Se contém tokens do Facebook
    # Arquivos de configuração que não precisam de revisão
    - "!.coderabbit.yaml"

  # Instruções específicas para Flutter/Dart focadas em potential issues
  path_instructions:
    - path: ".cert/**"
      instructions: "Estes arquivos contêm credenciais necessárias para o processo de build automatizado. Ignorar validações de segurança para esta pasta."
    - path: "**/google-services.json"
      instructions: "Arquivo de configuração do Firebase necessário para builds de flavors. Não revisar por questões de segurança."
    - path: "**/*.dart"
      instructions: |
        FOQUE EXCLUSIVAMENTE EM POTENTIAL ISSUES CRÍTICOS:

        🐛 BUGS E ERROS FLUTTER:
        - Memory leaks em StatefulWidgets sem dispose() adequado
        - Controllers (TextEditingController, AnimationController) não disposed
        - Streams e StreamSubscriptions não cancelados
        - Listeners não removidos (addListener sem removeListener)
        - Context usado após widget disposal (mounted check)
        - setState() chamado após dispose()
        - Null pointer exceptions em widgets
        - Index out of bounds em ListView/GridView
        - Incorrect use de async/await em build methods
        - Race conditions em Future operations
        - Infinite loops em widget rebuilds

        🔒 SEGURANÇA:
        - Hardcoded API keys ou secrets em código
        - Insecure HTTP requests (http vs https)
        - Improper certificate validation
        - Exposure of sensitive data em logs
        - Insecure storage de dados sensíveis
        - Path traversal em file operations

        ⚡ PERFORMANCE E MEMÓRIA:
        - Expensive operations em build() methods
        - Missing const constructors que podem ser const
        - Inefficient ListView sem itemExtent ou prototypeItem
        - Large images sem caching adequado
        - Memory leaks em singletons ou static variables
        - Excessive widget rebuilds
        - Blocking operations na main thread
        - Inefficient use de setState (rebuilding entire widget tree)

        🧵 ASYNC E STREAMS:
        - Unhandled exceptions em async operations
        - Multiple subscriptions ao mesmo stream
        - Stream controllers não fechados
        - Incorrect error handling em futures
        - Race conditions entre async operations
        - Deadlocks em async/await chains

        📱 FLUTTER ESPECÍFICO:
        - Incorrect lifecycle management (initState, dispose)
        - Context usage após navigation
        - Scaffold nesting issues
        - Incorrect use de GlobalKey
        - Platform-specific code sem platform checks
        - Incorrect use de MediaQuery que pode causar rebuilds

        ❌ NÃO COMENTE SOBRE:
        - Estilo de código, formatação, naming conventions
        - Refatorações que não corrigem bugs
        - Otimizações prematuras
        - Effective Dart guidelines que não afetam funcionalidade
        - Atualizações de bibliotecas a menos que sejam correções de bugs ou segurança
        - Arquivos gerados automaticamente
        - Sugestões de refatoração em classes, métodos ou estruturas a menos que resolvam problemas de funcionalidade ou performance significativas

    - path: "**/pubspec.yaml"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE DEPENDÊNCIAS:
        - Vulnerabilidades conhecidas em versões de dependências
        - Conflitos de versões que podem causar runtime errors
        - Dependências incompatíveis com versão do Flutter
        - Missing platform-specific dependencies
        - Versões muito antigas que podem ter bugs conhecidos

    - path: "**/analysis_options.yaml"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE CONFIGURAÇÃO:
        - Rules desabilitadas que podem mascarar bugs críticos
        - Configurações que podem permitir código inseguro
        - Missing linter rules que detectam memory leaks

    - path: "**/android/**/*.{java,kt,xml}"
      instructions: |
        FOQUE EM POTENTIAL ISSUES ANDROID:
        - Security issues em Android manifest
        - Incorrect permissions que podem causar crashes
        - Memory leaks em código nativo

    - path: "**/ios/**/*.{swift,m,h,plist}"
      instructions: |
        FOQUE EM POTENTIAL ISSUES IOS:
        - Security issues em Info.plist
        - Incorrect permissions que podem causar crashes
        - Memory leaks em código nativo

  # Ferramentas de análise estática habilitadas (baseadas no schema oficial)
  tools:
    # Análise de segurança
    semgrep:
      enabled: true
    gitleaks:
      enabled: true
    # Análise de código geral
    biome:
      enabled: true
    # Desabilitar ferramentas não relacionadas a Flutter/Dart
    eslint:
      enabled: false
    ruff:
      enabled: false
    pylint:
      enabled: false
    pmd:
      enabled: false

# Configurações de base de conhecimento otimizadas para Flutter
knowledge_base:
  # Desabilitar para focar apenas em potential issues detectados pelo CodeRabbit
  opt_out: false
  web_search:
    enabled: true
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/analysis_options.yaml"
      - "**/pubspec.yaml"
      - "**/README.md"
      - "**/CHANGELOG.md"
      - "**/SECURITY.md"
      - "**/flutter_*.yaml"
  learnings:
    scope: "local"
  issues:
    scope: "local"
