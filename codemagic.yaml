workflows:
  flutter-build:
    name: Flutter Build
    max_build_duration: 120
    instance_type: mac_mini_m2
    environment:
      groups:
        - firebase_credentials # Contém GOOGLE_SERVICES_JSON (se não vier da API)
      vars:
        FLAVOR: "apppersonalizado" # ID da flavor (ex.: development, production)
        XCODE_WORKSPACE: "ios/Runner.xcworkspace"
        XCODE_SCHEME: "apppersonalizado"
        ENTRY_POINT: "lib/main_personalizados/main-personalizado.dart"
      flutter: stable
      xcode: latest
      cocoapods: default
    triggering:
      events:
        - tag
      branch_patterns:
        - pattern: "*-personalizados-*-tf-*"
    cache:
      cache_paths:
        - ~/.pub-cache
        - $HOME/Library/Caches/CocoaPods
        - $HOME/.gradle/caches
    scripts:
      - name: Extrair DOCUMENT_KEY da tag
        script: |
          TAG_NAME=$(git describe --exact-match --tags HEAD)
          DOCUMENT_KEY=$(echo "$TAG_NAME" | sed -E 's/0\.0\.0-personalizados-(.*)-tf-0/\1/')
          if [ -z "$DOCUMENT_KEY" ]; then
            echo "Erro: Não foi possível extrair DOCUMENT_KEY da tag $TAG_NAME"
            exit 1
          fi
          echo "export DOCUMENT_KEY=$DOCUMENT_KEY" >> $CM_ENV
      - name: Verificar variáveis de ambiente
        script: |
          if [ -z "$DOCUMENT_KEY" ]; then
            echo "Erro: DOCUMENT_KEY não definido"
            exit 1
          fi
      - name: Fetch app configuration from API
        script: |
          RESPONSE_RAW=$(curl -s --location "https://app-do-aluno-unificado.web.app/clienteApp/consultarAppPersonalizado?documentKey=${DOCUMENT_KEY}" \
          --header 'Authorization: functionAlunoAntigo' \
          --header 'mc: Mobile Center BuildNumber' --write-out "%{http_code}" --output /tmp/api_response.json)
          HTTP_STATUS=$(tail -n1 <<< "$RESPONSE_RAW")
          if [ "$HTTP_STATUS" -ne 200 ]; then
            echo "Erro na requisição à API: HTTP $HTTP_STATUS"
            exit 1
          fi
          RESPONSE_JSON=$(cat /tmp/api_response.json)
          IOS_BUNDLE_ID=$(echo "$RESPONSE_JSON" | jq -r '.iosBundleId')
          IOS_ISSUER_ID=$(echo "$RESPONSE_JSON" | jq -r '.iosIssuerId')
          IOS_P8_FILE=$(echo "$RESPONSE_JSON" | jq -r '.iosP8File')
          IOS_P8_PASSWORD=$(echo "$RESPONSE_JSON" | jq -r '.iosP8Password')
          GOOGLE_SERVICES_JSON=$(echo "$RESPONSE_JSON" | jq -r '.firebaseGoogleServicesJsonAndroid')
          if [ -z "$IOS_BUNDLE_ID" ] || [ -z "$IOS_ISSUER_ID" ] || [ -z "$IOS_P8_FILE" ] || [ -z "$IOS_P8_PASSWORD" ]; then
            echo "Erro: Campos obrigatórios ausentes na resposta da API"
            exit 1
          fi
          echo "export IOS_BUNDLE_ID=$IOS_BUNDLE_ID" >> $CM_ENV
          echo "export IOS_ISSUER_ID=$IOS_ISSUER_ID" >> $CM_ENV
          echo "export IOS_P8_PASSWORD=$IOS_P8_PASSWORD" >> $CM_ENV
      - name: Save P8 file
        script: |
          mkdir -p "$CM_BUILD_DIR/ios/"
          echo "$IOS_P8_FILE" | base64 --decode > "$CM_BUILD_DIR/ios/AuthKey.p8"
          if [ ! -s "$CM_BUILD_DIR/ios/AuthKey.p8" ]; then
            echo "Erro: Falha ao salvar AuthKey.p8"
            exit 1
          fi
      - name: Save google-services.json
        script: |
          if [ -n "$GOOGLE_SERVICES_JSON" ]; then
            echo "$GOOGLE_SERVICES_JSON" | base64 --decode > "$CM_BUILD_DIR/android/app/google-services.json"
            if [ ! -s "$CM_BUILD_DIR/android/app/google-services.json" ]; then
              echo "Erro: Falha ao salvar google-services.json"
              exit 1
            fi
          else
            echo "Aviso: google-services.json não fornecido pela API"
          fi
      - name: Verificar ponto de entrada da flavor
        script: |
          if [ ! -f "$ENTRY_POINT" ]; then
            echo "Erro: Arquivo de entrada $ENTRY_POINT não encontrado"
            exit 1
          fi
      - name: Install dependencies
        script: |
          flutter pub get
      - name: Fetch signing files for iOS
        script: |
          app-store-connect fetch-signing-files "$IOS_BUNDLE_ID" --type IOS_APP_STORE --create \
          --issuer-id "$IOS_ISSUER_ID" \
          --private-key-path "$CM_BUILD_DIR/ios/AuthKey.p8" \
          --private-key-password "$IOS_P8_PASSWORD"
      - name: Set up code signing
        script: |
          keychain initialize
          keychain add-certificates
          xcode-project use-profiles
      - name: Build iOS IPA
        script: |
          flutter build ipa --release \
          --flavor="$FLAVOR" \
          --build-name=10.0 \
          --build-number=$(($(app-store-connect get-latest-testflight-build-number "$IOS_BUNDLE_ID") + 1)) \
          --export-options-plist=/Users/<USER>/export_options.plist \
          -t "$ENTRY_POINT"
      - name: Build Android AAB
        script: |
          flutter build appbundle --release \
          --flavor="$FLAVOR" \
          -t "$ENTRY_POINT"
    artifacts:
      - build/ios/ipa/*.ipa
      - build/app/outputs/bundle/$FLAVOR/*.aab
      - /tmp/xcodebuild_logs/*.log
    publishing:
      app_store_connect:
        api_key: "$CM_BUILD_DIR/ios/AuthKey.p8"
        key_id: "$IOS_P8_PASSWORD"
        issuer_id: "$IOS_ISSUER_ID"
        submit_to_testflight: true
      google_play:
        credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
        track: internal
      email:
        recipients:
          - <EMAIL>
        notify:
          success: true
          failure: true